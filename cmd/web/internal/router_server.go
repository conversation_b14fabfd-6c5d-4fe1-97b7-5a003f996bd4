package internal

import (
	"fmt"

	"godsp/conf"
	"godsp/modules/admin"
	"godsp/modules/email"
	"godsp/modules/facebook"
	"godsp/modules/tiktok"
	"godsp/pkg/gos/midd"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/dev-networldasia/dspgos/sctx/component/jwtc"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/csrf"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func RouterWeb(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store) {
	app.Use(logger.New(logger.Config{
		Format: `{"ip":${ip}, "timestamp":"${time}", "status":${status}, "latency":"${latency}", "method":"${method}", "path":"${path}"}` + "\n",
	}))
	app.Use(compress.New())
	app.Use(cors.New())

	if serviceCtx.EnvName() == configs.AppProd {
		app.Use(midd.Recovery(serviceCtx))
		app.Use(csrf.New())
	}

	jwtComp := serviceCtx.MustGet(configs.KeyCompJWT).(jwtc.JWTProvider)
	permissionMidd := midd.RequireAuth(jwtComp, serviceCtx)
	authMidd := midd.AuthMidd(jwtComp)
	app.Use([]string{"login", "api", "admins", "dsp"}, authMidd, permissionMidd)

	//admin
	admin.SetupRoutesAdmin(app, serviceCtx, store)

	//facebook
	facebook.SetupRoutesFacebook(app, serviceCtx, store)

	//tiktok
	tiktok.SetupRoutesTiktok(app, serviceCtx, store)

	//email
	email.SetupRoutesEmail(app, serviceCtx, store)

	app.Static("/static", fmt.Sprintf("./%s", conf.UploadPathPublic))
}
