/**
 * Facebook Campaign List Page
 * Handles campaign, adset, and ad tables with custom columns and drag & drop functionality
 */

import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
import { requestApi } from "/static/js/common/httpService.js";
import { 
  defaultConfigTableFacebook, 
  initSortableColumns, 
  onSelectAllRowFacebook, 
  onDefaultTableFacebook,
  customColumnManager,
  defaultFacebookColumns
} from "/static/js/facebook/config-table.js";

// Global variables
let campaignTable, adsetTable, adsTable;
let campaignSortable, adsetSortable, adsSortable;
let selectedCampaigns = [], selectedAdSets = [], selectedAds = [];
let currentTab = 'campaigns';

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", function() {
  // Initialize tabs
  initTabs();
  
  // Initialize tables
  initCampaignTable();
  initAdSetTable();
  initAdsTable();
  
  // Initialize custom column modal
  initCustomColumnModal();
  
  // Initialize filters
  initFilters();
  
  // Initialize create buttons
  initCreateButtons();
});

// Initialize tab switching
function initTabs() {
  const tabLinks = document.querySelectorAll('#navTabTableFacebook .nav-link');
  
  tabLinks.forEach(tab => {
    tab.addEventListener('click', function(e) {
      e.preventDefault();
      
      // Update current tab
      currentTab = this.getAttribute('data-tab');
      
      // Adjust filter section based on tab
      updateFilterSection(currentTab);
      
      // Adjust table columns if needed
      setTimeout(() => {
        if (currentTab === 'campaigns' && campaignTable) {
          campaignTable.columns.adjust().responsive.recalc();
        } else if (currentTab === 'adsets' && adsetTable) {
          adsetTable.columns.adjust().responsive.recalc();
        } else if (currentTab === 'ads' && adsTable) {
          adsTable.columns.adjust().responsive.recalc();
        }
      }, 100);
    });
  });
}

// Update filter section based on active tab
function updateFilterSection(tabName) {
  const filterSection = document.getElementById('filterSection');
  if (!filterSection) return;
  
  // Clear existing filters
  filterSection.innerHTML = '';
  
  // Common filters
  const commonFilters = `
    <div class="col-md-3">
      <label class="form-label">Ad Account</label>
      <select class="form-select" id="adAccountFilter">
        <option value="">All Ad Accounts</option>
      </select>
    </div>
    <div class="col-md-3">
      <label class="form-label">Status</label>
      <select class="form-select" id="statusFilter">
        <option value="">All Status</option>
        <option value="ACTIVE">Active</option>
        <option value="PAUSED">Paused</option>
        <option value="ARCHIVED">Archived</option>
      </select>
    </div>
    <div class="col-md-3">
      <label class="form-label">Date Range</label>
      <input type="text" class="form-control" id="dateRangeFilter" placeholder="Select date range"/>
    </div>
  `;
  
  // Tab-specific filters
  let specificFilter = '';
  
  if (tabName === 'campaigns') {
    specificFilter = `
      <div class="col-md-3">
        <label class="form-label">Client</label>
        <select class="form-select" id="clientFilter">
          <option value="">All Clients</option>
        </select>
      </div>
    `;
  } else if (tabName === 'adsets') {
    specificFilter = `
      <div class="col-md-3">
        <label class="form-label">Campaign</label>
        <select class="form-select" id="campaignFilter">
          <option value="">All Campaigns</option>
        </select>
      </div>
    `;
  } else if (tabName === 'ads') {
    specificFilter = `
      <div class="col-md-3">
        <label class="form-label">Ad Set</label>
        <select class="form-select" id="adsetFilter">
          <option value="">All Ad Sets</option>
        </select>
      </div>
    `;
  }
  
  // Combine filters
  filterSection.innerHTML = specificFilter + commonFilters;
  
  // Initialize date range picker
  if ($.fn.daterangepicker) {
    $('#dateRangeFilter').daterangepicker({
      opens: 'left',
      autoUpdateInput: false,
      locale: {
        cancelLabel: 'Clear',
        format: 'MM/DD/YYYY'
      }
    });
    
    $('#dateRangeFilter').on('apply.daterangepicker', function(ev, picker) {
      $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
      reloadActiveTable();
    });
    
    $('#dateRangeFilter').on('cancel.daterangepicker', function(ev, picker) {
      $(this).val('');
      reloadActiveTable();
    });
  }
  
  // Add event listeners to filters
  document.querySelectorAll('#filterSection select').forEach(filter => {
    filter.addEventListener('change', reloadActiveTable);
  });
  
  // Populate filters
  populateFilters(tabName);
}

// Populate filter dropdowns
function populateFilters(tabName) {
  // Populate ad accounts
  requestApi('GET', '/dsp/facebook/api/ad-accounts/list')
    .then(response => {
      if (response && response.data) {
        const adAccountSelect = document.getElementById('adAccountFilter');
        if (adAccountSelect) {
          response.data.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = account.name;
            adAccountSelect.appendChild(option);
          });
        }
      }
    })
    .catch(error => {
      console.error('Error loading ad accounts:', error);
    });
  
  // Populate client filter
  if (tabName === 'campaigns') {
    requestApi('GET', '/dsp/facebook/api/clients/list')
      .then(response => {
        if (response && response.data) {
          const clientSelect = document.getElementById('clientFilter');
          if (clientSelect) {
            response.data.forEach(client => {
              const option = document.createElement('option');
              option.value = client.id;
              option.textContent = client.name;
              clientSelect.appendChild(option);
            });
          }
        }
      })
      .catch(error => {
        console.error('Error loading clients:', error);
      });
  }
  
  // Populate campaign filter for adsets tab
  if (tabName === 'adsets') {
    requestApi('GET', '/dsp/facebook/api/campaigns/list')
      .then(response => {
        if (response && response.data) {
          const campaignSelect = document.getElementById('campaignFilter');
          if (campaignSelect) {
            response.data.forEach(campaign => {
              const option = document.createElement('option');
              option.value = campaign.id;
              option.textContent = campaign.name;
              campaignSelect.appendChild(option);
            });
          }
        }
      })
      .catch(error => {
        console.error('Error loading campaigns:', error);
      });
  }
  
  // Populate adset filter for ads tab
  if (tabName === 'ads') {
    requestApi('GET', '/dsp/facebook/api/adsets/list')
      .then(response => {
        if (response && response.data) {
          const adsetSelect = document.getElementById('adsetFilter');
          if (adsetSelect) {
            response.data.forEach(adset => {
              const option = document.createElement('option');
              option.value = adset.id;
              option.textContent = adset.name;
              adsetSelect.appendChild(option);
            });
          }
        }
      })
      .catch(error => {
        console.error('Error loading adsets:', error);
      });
  }
}

// Initialize campaign table
function initCampaignTable() {
  const tableElement = document.getElementById('campaignTable');
  if (!tableElement) return;
  
  // Get columns configuration
  const columns = customColumnManager.getCurrentColumns('campaign');
  
  // Build columns for DataTable
  const dtColumns = [
    {
      data: null,
      orderable: false,
      searchable: false,
      width: "30px",
      className: "align-middle",
      render: function(data, type, row) {
        return `<div class="form-check">
          <input class="form-check-input" type="checkbox" value="${row.id || ''}" />
        </div>`;
      }
    }
  ];
  
  // Add custom columns
  columns.forEach(column => {
    if (column.key === 'status') {
      dtColumns.push({
        data: 'status',
        name: 'status',
        className: "align-middle",
        render: function(data, type, row) {
          const statusClass = data === 'ACTIVE' ? 'success' : 
                             data === 'PAUSED' ? 'warning' : 'secondary';
          return `<span class="badge bg-${statusClass}">${data}</span>`;
        }
      });
    } else if (column.key !== 'actions') {
      dtColumns.push({
        data: column.key,
        name: column.key,
        className: "align-middle"
      });
    }
  });
  
  // Add actions column
  dtColumns.push({
    data: null,
    orderable: false,
    searchable: false,
    className: "align-middle",
    render: function(data, type, row) {
      return `<div class="d-flex gap-2">
        <button type="button" class="btn btn-sm btn-primary edit-campaign" data-id="${row.id || ''}">
          <i class="ri-pencil-line"></i>
        </button>
        <button type="button" class="btn btn-sm btn-danger delete-campaign" data-id="${row.id || ''}">
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>`;
    }
  });
  
  // Initialize DataTable
  campaignTable = $(tableElement).DataTable({
    ...defaultConfigTableFacebook({ displayStart: 0 }, "Facebook Campaigns"),
    processing: true,
    serverSide: true,
    ajax: {
      url: '/dsp/facebook/api/campaigns/list-datatable',
      type: 'POST',
      data: function(d) {
        return {
          ...d,
          ad_account_id: $('#adAccountFilter').val(),
          client_id: $('#clientFilter').val(),
          status: $('#statusFilter').val(),
          date_range: $('#dateRangeFilter').val()
        };
      }
    },
    columns: dtColumns,
    order: [[1, 'asc']]
  });
  
  // Initialize sortable columns
  campaignSortable = initSortableColumns('campaignTableHeader', function(newOrder) {
    // Save new column order
    const columns = customColumnManager.getCurrentColumns('campaign');
    const newColumns = newOrder.map(key => columns.find(col => col.key === key)).filter(Boolean);
    customColumnManager.saveColumns('campaign', newColumns);
    
    // Emit event for column change
    document.dispatchEvent(new CustomEvent('facebook.columns.changed', { detail: { type: 'campaign' } }));
  });
  
  // Initialize row selection
  onSelectAllRowFacebook($('#campaignTable'), campaignTable, updateSelectedCampaigns);
  onDefaultTableFacebook($('#campaignTable'), campaignTable, 'campaign_selected', updateSelectedCampaigns);
  
  // Add event listeners for edit/delete buttons
  $(tableElement).on('click', '.edit-campaign', function() {
    const id = $(this).data('id');
    openCampaignEditor(id);
  });
  
  $(tableElement).on('click', '.delete-campaign', function() {
    const id = $(this).data('id');
    confirmDeleteCampaign(id);
  });
}

// Initialize adset table
function initAdSetTable() {
  // Similar to initCampaignTable but for adsets
  // Implementation omitted for brevity
}

// Initialize ads table
function initAdsTable() {
  // Similar to initCampaignTable but for ads
  // Implementation omitted for brevity
}

// Initialize custom column modal
function initCustomColumnModal() {
  const customColumnBtn = document.getElementById('customColumnBtn');
  if (!customColumnBtn) return;
  
  customColumnBtn.addEventListener('click', function() {
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('customColumnModal'));
    modal.show();
    
    // Load modal content
    loadCustomColumnModalContent();
  });
}

// Load custom column modal content
function loadCustomColumnModalContent() {
  loader(true);
  
  requestApi('POST', '/dsp/facebook/api/custom-column-table/custom-modal')
    .then(response => {
      if (response && response.modal_custom_column_html) {
        document.getElementById('customColumnModalBody').innerHTML = response.modal_custom_column_html;
        
        // Initialize sortable for selected columns
        initModalSortable();
        
        // Initialize event listeners for column selection
        initColumnSelectionEvents();
      }
    })
    .catch(error => {
      console.error('Error loading custom column modal:', error);
      errorAlert('Failed to load custom column modal. Please try again.');
    })
    .finally(() => {
      loader(false);
    });
}

// Initialize sortable in custom column modal
function initModalSortable() {
  const selectedColumnsList = document.getElementById('selectedColumnsList');
  if (!selectedColumnsList) return;
  
  new Sortable(selectedColumnsList, {
    animation: 150,
    handle: '.ri-drag-move-2-fill',
    onEnd: function() {
      // Get new column order
      const columns = Array.from(selectedColumnsList.querySelectorAll('li'))
        .map(li => ({
          key: li.dataset.columnKey,
          name: li.querySelector('span').textContent.trim(),
          required: li.querySelector('.badge') !== null
        }));
      
      // Save new order
      customColumnManager.saveColumns(currentTab, columns);
    }
  });
}

// Initialize column selection events in modal
function initColumnSelectionEvents() {
  // Handle column checkbox changes
  document.querySelectorAll('.column-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      const columnKey = this.value;
      const columnName = this.parentElement.querySelector('label').textContent.trim();
      
      if (this.checked) {
        // Add column to selected list
        addColumnToSelected(columnKey, columnName);
      } else {
        // Remove column from selected list
        removeColumnFromSelected(columnKey);
      }
    });
  });
  
  // Handle remove column button clicks
  document.querySelectorAll('.remove-column').forEach(button => {
    button.addEventListener('click', function() {
      const columnKey = this.dataset.columnKey;
      removeColumnFromSelected(columnKey);
      
      // Uncheck corresponding checkbox
      document.querySelector(`.column-checkbox[value="${columnKey}"]`).checked = false;
    });
  });
  
  // Handle save button click
  document.getElementById('saveCustomColumns').addEventListener('click', function() {
    saveCustomColumns();
  });
}

// Add column to selected list
function addColumnToSelected(key, name) {
  const selectedColumnsList = document.getElementById('selectedColumnsList');
  if (!selectedColumnsList) return;
  
  // Check if column already exists
  if (selectedColumnsList.querySelector(`li[data-column-key="${key}"]`)) return;
  
  // Create new list item
  const li = document.createElement('li');
  li.className = 'list-group-item d-flex justify-content-between align-items-center';
  li.dataset.columnKey = key;
  
  // Check if column is required
  const isRequired = defaultFacebookColumns[currentTab].find(col => col.key === key && col.required);
  
  li.innerHTML = `
    <div>
      <i class="ri-drag-move-2-fill me-2 text-muted"></i>
      <span>${name}</span>
      ${isRequired ? '<span class="badge bg-secondary ms-2">Required</span>' : ''}
    </div>
    ${isRequired ? '' : `
      <button type="button" class="btn btn-sm btn-outline-danger remove-column" data-column-key="${key}">
        <i class="ri-close-line"></i>
      </button>
    `}
  `;
  
  // Add to list
  selectedColumnsList.appendChild(li);
  
  // Add event listener to remove button
  const removeButton = li.querySelector('.remove-column');
  if (removeButton) {
    removeButton.addEventListener('click', function() {
      removeColumnFromSelected(key);
      
      // Uncheck corresponding checkbox
      document.querySelector(`.column-checkbox[value="${key}"]`).checked = false;
    });
  }
}

// Remove column from selected list
function removeColumnFromSelected(key) {
  const item = document.querySelector(`#selectedColumnsList li[data-column-key="${key}"]`);
  if (item && !item.querySelector('.badge')) {
    item.remove();
  }
}

// Save custom columns
function saveCustomColumns() {
  const selectedColumnsList = document.getElementById('selectedColumnsList');
  if (!selectedColumnsList) return;
  
  // Get selected columns
  const columns = Array.from(selectedColumnsList.querySelectorAll('li'))
    .map(li => ({
      key: li.dataset.columnKey,
      name: li.querySelector('span').textContent.trim(),
      required: li.querySelector('.badge') !== null
    }));
  
  // Save columns
  customColumnManager.saveColumns(currentTab, columns);
  
  // Close modal
  bootstrap.Modal.getInstance(document.getElementById('customColumnModal')).hide();
  
  // Reload active table
  reloadActiveTable();
  
  // Show success message
  successAlert('Column configuration saved successfully.');
}

// Initialize create buttons
function initCreateButtons() {
  // Campaign create button
  document.getElementById('createCampaignBtn')?.addEventListener('click', function() {
    window.location.href = '/dsp/facebook/campaigns/create';
  });
  
  // Ad Set create button
  document.getElementById('createAdSetBtn')?.addEventListener('click', function() {
    window.location.href = '/dsp/facebook/adsets/create';
  });
  
  // Ad create button
  document.getElementById('createAdBtn')?.addEventListener('click', function() {
    window.location.href = '/dsp/facebook/ads/create';
  });
}

// Update selected campaigns
function updateSelectedCampaigns() {
  selectedCampaigns = [];
  $('#campaignTable tbody .form-check-input:checked').each(function() {
    selectedCampaigns.push($(this).val());
  });
  
  // Update counter
  const counter = document.getElementById('campaign_selected');
  if (counter) {
    if (selectedCampaigns.length > 0) {
      counter.classList.remove('d-none');
      counter.textContent = `${selectedCampaigns.length} selected`;
    } else {
      counter.classList.add('d-none');
    }
  }
}

// Update selected ad sets
function updateSelectedAdSets() {
  // Similar to updateSelectedCampaigns
}

// Update selected ads
function updateSelectedAds() {
  // Similar to updateSelectedCampaigns
}

// Reload active table
function reloadActiveTable() {
  if (currentTab === 'campaigns' && campaignTable) {
    campaignTable.ajax.reload();
  } else if (currentTab === 'adsets' && adsetTable) {
    adsetTable.ajax.reload();
  } else if (currentTab === 'ads' && adsTable) {
    adsTable.ajax.reload();
  }
}

// Open campaign editor
function openCampaignEditor(id) {
  // Implementation omitted for brevity
}

// Confirm delete campaign
function confirmDeleteCampaign(id) {
  // Implementation omitted for brevity
}

// Export functions for global access
window.facebookCampaign = {
  reloadTables: reloadActiveTable,
  getSelectedCampaigns: () => selectedCampaigns,
  getSelectedAdSets: () => selectedAdSets,
  getSelectedAds: () => selectedAds
};
