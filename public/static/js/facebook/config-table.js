/**
 * Facebook Table Configuration
 * Provides configuration for Facebook campaign, adset, and ad tables
 * with support for custom columns and drag & drop functionality
 */

// Default configuration for Facebook tables
export const defaultConfigTableFacebook = (options = {}, title = "") => {
  return {
    dom: "Bfrtip",
    responsive: true,
    autoWidth: false,
    lengthMenu: [10, 25, 50, 100],
    pageLength: options.pageLength || 25,
    displayStart: options.displayStart || 0,
    ordering: true,
    searching: true,
    info: true,
    language: {
      emptyTable: "No data available",
      info: "Showing _START_ to _END_ of _TOTAL_ entries",
      infoEmpty: "Showing 0 to 0 of 0 entries",
      infoFiltered: "(filtered from _MAX_ total entries)",
      lengthMenu: "Show _MENU_ entries",
      loadingRecords: "Loading...",
      processing: "Processing...",
      search: "Search:",
      zeroRecords: "No matching records found",
      paginate: {
        first: "First",
        last: "Last",
        next: "Next",
        previous: "Previous",
      },
    },
    buttons: [
      {
        extend: "collection",
        text: '<i class="ri-download-2-line"></i> Export',
        buttons: [
          {
            extend: "excel",
            text: '<i class="ri-file-excel-2-line"></i> Excel',
            title: title,
            exportOptions: {
              columns: ":visible:not(.no-export)",
            },
          },
          {
            extend: "csv",
            text: '<i class="ri-file-text-line"></i> CSV',
            title: title,
            exportOptions: {
              columns: ":visible:not(.no-export)",
            },
          },
          {
            extend: "pdf",
            text: '<i class="ri-file-pdf-line"></i> PDF',
            title: title,
            exportOptions: {
              columns: ":visible:not(.no-export)",
            },
          },
        ],
      },
    ],
    drawCallback: function () {
      $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
      numberFormat(".number-format");
    },
  };
};

// Get custom columns for Facebook tables
export const getCustomColumnsFacebook = (
  customFields,
  tableType = "CAMPAIGN"
) => {
  if (!customFields || !Array.isArray(customFields)) {
    return [];
  }

  // Filter fields based on table type
  const filteredFields = customFields.filter((field) => {
    if (!field.tableTypes || field.tableTypes.length === 0) {
      return true; // Include if no specific table types are defined
    }
    return field.tableTypes.includes(tableType);
  });

  // Map fields to DataTables column format
  return filteredFields.map((field) => {
    return {
      data: field.key,
      name: field.key,
      title: field.name,
      className: "align-middle",
      render: function (data, type, row) {
        if (type === "display") {
          // Format based on field type
          if (field.format === "currency") {
            return formatCurrency(data);
          } else if (field.format === "percentage") {
            return formatPercentage(data);
          } else if (field.format === "number") {
            return formatNumber(data);
          } else if (field.format === "date") {
            return formatDate(data);
          }
        }
        return data;
      },
    };
  });
};

// Initialize sortable columns for drag & drop
export const initSortableColumns = (tableId, onOrderChange) => {
  const headerRow = document.querySelector(`#${tableId} thead tr`);
  if (!headerRow) return;

  // Initialize Sortable.js on the header row
  const sortable = new Sortable(headerRow, {
    animation: 150,
    handle: ".drag-handle",
    filter: ".no-drag", // Elements with this class won't be draggable
    onEnd: function (evt) {
      if (typeof onOrderChange === "function") {
        // Get the new column order
        const columns = Array.from(headerRow.querySelectorAll("th"))
          .filter((th) => th.dataset.key) // Only include columns with data-key attribute
          .map((th) => th.dataset.key);

        onOrderChange(columns);
      }
    },
  });

  return sortable;
};

// Handle row selection for Facebook tables
export const onSelectAllRowFacebook = (
  tableElement,
  dataTable,
  updateSelectedCallback
) => {
  const selectAllCheckbox = tableElement.find("thead .form-check-input");

  selectAllCheckbox.on("change", function () {
    const isChecked = $(this).prop("checked");
    tableElement.find("tbody .form-check-input").prop("checked", isChecked);

    if (typeof updateSelectedCallback === "function") {
      updateSelectedCallback();
    }
  });
};

// Default table initialization for Facebook tables
export const onDefaultTableFacebook = (
  tableElement,
  dataTable,
  selectedCounterId,
  updateSelectedCallback
) => {
  // Handle individual row selection
  tableElement.on("change", "tbody .form-check-input", function () {
    if (typeof updateSelectedCallback === "function") {
      updateSelectedCallback();
    }

    // Update select all checkbox state
    const totalCheckboxes = tableElement.find("tbody .form-check-input").length;
    const checkedCheckboxes = tableElement.find(
      "tbody .form-check-input:checked"
    ).length;

    const selectAllCheckbox = tableElement.find("thead .form-check-input");

    if (checkedCheckboxes === 0) {
      selectAllCheckbox.prop("indeterminate", false).prop("checked", false);
    } else if (checkedCheckboxes === totalCheckboxes) {
      selectAllCheckbox.prop("indeterminate", false).prop("checked", true);
    } else {
      selectAllCheckbox.prop("indeterminate", true);
    }

    // Update selected counter
    const selectedCounter = $(`#${selectedCounterId}`);
    if (checkedCheckboxes > 0) {
      selectedCounter
        .removeClass("d-none")
        .find("span")
        .text(`${checkedCheckboxes} selected`);
    } else {
      selectedCounter.addClass("d-none");
    }
  });

  // Handle selected counter close button
  $(`#${selectedCounterId}-close`).on("click", function (e) {
    e.preventDefault();
    e.stopPropagation();

    tableElement.find(".form-check-input").prop("checked", false);
    if (typeof updateSelectedCallback === "function") {
      updateSelectedCallback();
    }

    $(`#${selectedCounterId}`).addClass("d-none");
  });
};

// Format helpers
function formatCurrency(value) {
  if (value === null || value === undefined) return "";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(value);
}

function formatPercentage(value) {
  if (value === null || value === undefined) return "";
  return new Intl.NumberFormat("en-US", {
    style: "percent",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100);
}

function formatNumber(value) {
  if (value === null || value === undefined) return "";
  return new Intl.NumberFormat("en-US").format(value);
}

function formatDate(value) {
  if (value === null || value === undefined) return "";
  return new Date(value).toLocaleDateString();
}

// Default column definitions for Facebook tables
export const defaultFacebookColumns = {
  campaign: [
    { key: "status", name: "Status", required: true },
    { key: "campaign_name", name: "Campaign Name", required: true },
    { key: "objective", name: "Objective" },
    { key: "impressions", name: "Impressions" },
    { key: "clicks", name: "Clicks" },
    { key: "ctr", name: "CTR" },
    { key: "spend", name: "Spend" },
    { key: "cpc", name: "CPC" },
    { key: "cpm", name: "CPM" },
    { key: "conversions", name: "Conversions" },
    { key: "cost_per_conversion", name: "Cost per Conversion" },
  ],
  adset: [
    { key: "status", name: "Status", required: true },
    { key: "adset_name", name: "Ad Set Name", required: true },
    { key: "campaign_name", name: "Campaign", required: true },
    { key: "bid_strategy", name: "Bid Strategy" },
    { key: "impressions", name: "Impressions" },
    { key: "clicks", name: "Clicks" },
    { key: "ctr", name: "CTR" },
    { key: "spend", name: "Spend" },
    { key: "cpc", name: "CPC" },
    { key: "cpm", name: "CPM" },
  ],
  ad: [
    { key: "status", name: "Status", required: true },
    { key: "ad_name", name: "Ad Name", required: true },
    { key: "adset_name", name: "Ad Set", required: true },
    { key: "campaign_name", name: "Campaign", required: true },
    { key: "impressions", name: "Impressions" },
    { key: "clicks", name: "Clicks" },
    { key: "ctr", name: "CTR" },
    { key: "spend", name: "Spend" },
    { key: "cpc", name: "CPC" },
    { key: "cpm", name: "CPM" },
  ],
};

// Custom column management
export const customColumnManager = {
  // Get saved column configuration from localStorage
  getSavedColumns: function (tableType) {
    const saved = localStorage.getItem(
      `facebook_columns_${tableType.toLowerCase()}`
    );
    return saved ? JSON.parse(saved) : null;
  },

  // Save column configuration to localStorage
  saveColumns: function (tableType, columns) {
    localStorage.setItem(
      `facebook_columns_${tableType.toLowerCase()}`,
      JSON.stringify(columns)
    );
  },

  // Get current column configuration (saved or default)
  getCurrentColumns: function (tableType) {
    const saved = this.getSavedColumns(tableType);
    return saved || defaultFacebookColumns[tableType.toLowerCase()] || [];
  },

  // Update table columns based on configuration
  updateTableColumns: function (tableId, columns) {
    const table = $(`#${tableId}`).DataTable();
    if (!table) return;

    // Rebuild table with new columns
    table.destroy();
    this.rebuildTableHeader(tableId, columns);
    // Re-initialize table (this should be done by the calling code)
  },

  // Rebuild table header with new columns
  rebuildTableHeader: function (tableId, columns) {
    const headerRow = document.querySelector(`#${tableId} thead tr`);
    if (!headerRow) return;

    // Clear existing columns (except checkbox and actions)
    const existingCols = headerRow.querySelectorAll("th");
    existingCols.forEach((col, index) => {
      if (index > 0 && !col.dataset.key?.includes("actions")) {
        col.remove();
      }
    });

    // Add new columns
    const actionsCol = headerRow.querySelector('th[data-key="actions"]');
    columns.forEach((column) => {
      if (column.key === "actions") return; // Skip actions column

      const th = document.createElement("th");
      th.dataset.key = column.key;
      th.className = "sortable-column";
      th.innerHTML = `
        <span class="drag-handle">⋮⋮</span>
        ${column.name}
      `;

      if (actionsCol) {
        headerRow.insertBefore(th, actionsCol);
      } else {
        headerRow.appendChild(th);
      }
    });
  },
};
