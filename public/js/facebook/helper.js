/**
 * Get fraction digits for number formatting
 */
export function getFractionDigits(value) {
    if (value === 0) return 0;
    if (value < 1) return 4;
    if (value < 10) return 3;
    if (value < 100) return 2;
    return 0;
}

/**
 * Format currency value
 */
export function formatCurrency(value, currency = 'USD') {
    if (value === null || value === undefined) return '0.00';
    
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
}

/**
 * Format percentage value
 */
export function formatPercentage(value) {
    if (value === null || value === undefined) return '0.00%';
    
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value / 100);
}

/**
 * Format number with appropriate decimal places
 */
export function formatNumber(value) {
    if (value === null || value === undefined) return '0';
    
    const fractionDigits = getFractionDigits(value);
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits
    }).format(value);
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatLargeNumber(value) {
    if (value === null || value === undefined) return '0';
    
    if (value >= 1000000000) {
        return (value / 1000000000).toFixed(1) + 'B';
    } else if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'K';
    }
    
    return formatNumber(value);
}

/**
 * Calculate CTR (Click-through rate)
 */
export function calculateCTR(clicks, impressions) {
    if (!impressions || impressions === 0) return 0;
    return (clicks / impressions) * 100;
}

/**
 * Calculate CPC (Cost per click)
 */
export function calculateCPC(spend, clicks) {
    if (!clicks || clicks === 0) return 0;
    return spend / clicks;
}

/**
 * Calculate CPM (Cost per 1,000 impressions)
 */
export function calculateCPM(spend, impressions) {
    if (!impressions || impressions === 0) return 0;
    return (spend / impressions) * 1000;
}

/**
 * Calculate ROAS (Return on ad spend)
 */
export function calculateROAS(conversionValue, spend) {
    if (!spend || spend === 0) return 0;
    return conversionValue / spend;
}

/**
 * Calculate conversion rate
 */
export function calculateConversionRate(conversions, clicks) {
    if (!clicks || clicks === 0) return 0;
    return (conversions / clicks) * 100;
}

/**
 * Get status badge class
 */
export function getStatusBadgeClass(status) {
    switch (status?.toUpperCase()) {
        case 'ACTIVE':
            return 'bg-success';
        case 'PAUSED':
            return 'bg-warning';
        case 'ARCHIVED':
            return 'bg-secondary';
        case 'DELETED':
            return 'bg-danger';
        case 'PENDING_REVIEW':
            return 'bg-info';
        case 'DISAPPROVED':
            return 'bg-danger';
        case 'PREAPPROVED':
            return 'bg-primary';
        default:
            return 'bg-secondary';
    }
}

/**
 * Get delivery status badge class
 */
export function getDeliveryStatusBadgeClass(deliveryStatus) {
    switch (deliveryStatus?.toUpperCase()) {
        case 'ACTIVE':
            return 'bg-success';
        case 'NOT_DELIVERING':
            return 'bg-warning';
        case 'PENDING_REVIEW':
            return 'bg-info';
        case 'REJECTED':
            return 'bg-danger';
        case 'LEARNING':
            return 'bg-primary';
        default:
            return 'bg-secondary';
    }
}

/**
 * Format date string
 */
export function formatDate(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Format datetime string
 */
export function formatDateTime(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text, maxLength = 50) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

/**
 * Get Facebook objective display name
 */
export function getObjectiveDisplayName(objective) {
    const objectiveMap = {
        'OUTCOME_APP_PROMOTION': 'App Promotion',
        'OUTCOME_AWARENESS': 'Awareness',
        'OUTCOME_ENGAGEMENT': 'Engagement',
        'OUTCOME_LEADS': 'Lead Generation',
        'OUTCOME_SALES': 'Sales',
        'OUTCOME_TRAFFIC': 'Traffic',
        'BRAND_AWARENESS': 'Brand Awareness',
        'REACH': 'Reach',
        'LINK_CLICKS': 'Link Clicks',
        'IMPRESSIONS': 'Impressions',
        'VIDEO_VIEWS': 'Video Views',
        'CONVERSIONS': 'Conversions',
        'APP_INSTALLS': 'App Installs',
        'LEAD_GENERATION': 'Lead Generation',
        'MESSAGES': 'Messages',
        'PAGE_LIKES': 'Page Likes',
        'POST_ENGAGEMENT': 'Post Engagement',
        'STORE_VISITS': 'Store Visits'
    };
    
    return objectiveMap[objective] || objective;
}

/**
 * Get bid strategy display name
 */
export function getBidStrategyDisplayName(bidStrategy) {
    const bidStrategyMap = {
        'LOWEST_COST_WITHOUT_CAP': 'Lowest Cost',
        'LOWEST_COST_WITH_BID_CAP': 'Lowest Cost with Bid Cap',
        'TARGET_COST': 'Target Cost',
        'LOWEST_COST_WITH_MIN_ROAS': 'Lowest Cost with Min ROAS',
        'COST_CAP': 'Cost Cap'
    };
    
    return bidStrategyMap[bidStrategy] || bidStrategy;
}

/**
 * Debounce function
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
export function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
