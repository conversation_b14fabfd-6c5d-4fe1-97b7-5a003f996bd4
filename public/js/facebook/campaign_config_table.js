// Default custom fields for Facebook campaigns
export const defaultCustomFields = [
    // Basic campaign information
    {
        key: "campaign_name",
        title: "Campaign Name",
        type: "string",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: false,
        is_custom_cell: false
    },
    {
        key: "objective",
        title: "Objective",
        type: "string",
        available: ["CAMPAIGN"],
        is_report: false,
        is_custom_cell: false
    },
    {
        key: "status",
        title: "Status",
        type: "string",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: false,
        is_custom_cell: false
    },
    {
        key: "delivery_status",
        title: "Delivery",
        type: "string",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: false,
        is_custom_cell: false
    },

    // Budget and bidding
    {
        key: "budget",
        title: "Budget",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET"],
        is_report: false,
        is_custom_cell: true
    },
    {
        key: "bid_strategy",
        title: "Bid Strategy",
        type: "string",
        available: ["ADSET"],
        is_report: false,
        is_custom_cell: true
    },
    {
        key: "optimization_goal",
        title: "Optimization Goal",
        type: "string",
        available: ["ADSET"],
        is_report: false,
        is_custom_cell: true
    },

    // Performance metrics
    {
        key: "impressions",
        title: "Impressions",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "clicks",
        title: "Clicks",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "spend",
        title: "Amount Spent",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "ctr",
        title: "CTR",
        type: "float64",
        unit: "%",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cpc",
        title: "CPC",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cpm",
        title: "CPM",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "reach",
        title: "Reach",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "frequency",
        title: "Frequency",
        type: "float64",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Conversion metrics
    {
        key: "conversions",
        title: "Conversions",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "conversion_rate",
        title: "Conversion Rate",
        type: "float64",
        unit: "%",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cost_per_conversion",
        title: "Cost per Conversion",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "conversion_value",
        title: "Conversion Value",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "roas",
        title: "ROAS",
        type: "float64",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Video metrics
    {
        key: "video_play_actions",
        title: "Video Plays",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "video_p25_watched_actions",
        title: "Video Watches at 25%",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "video_p50_watched_actions",
        title: "Video Watches at 50%",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "video_p75_watched_actions",
        title: "Video Watches at 75%",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "video_p100_watched_actions",
        title: "Video Watches at 100%",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Engagement metrics
    {
        key: "post_engagements",
        title: "Post Engagements",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "page_likes",
        title: "Page Likes",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "post_reactions",
        title: "Post Reactions",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "post_comments",
        title: "Post Comments",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "post_shares",
        title: "Post Shares",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Link clicks and landing page views
    {
        key: "link_clicks",
        title: "Link Clicks",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "outbound_clicks",
        title: "Outbound Clicks",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "landing_page_views",
        title: "Landing Page Views",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cost_per_landing_page_view",
        title: "Cost per Landing Page View",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // App install metrics
    {
        key: "app_installs",
        title: "App Installs",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cost_per_app_install",
        title: "Cost per App Install",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Lead generation metrics
    {
        key: "leads",
        title: "Leads",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cost_per_lead",
        title: "Cost per Lead",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Purchase metrics
    {
        key: "purchases",
        title: "Purchases",
        type: "int32",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "purchase_value",
        title: "Purchase Value",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },
    {
        key: "cost_per_purchase",
        title: "Cost per Purchase",
        type: "float64",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: true,
        is_custom_cell: false
    },

    // Custom result fields
    {
        key: "result",
        title: "Results",
        type: "string",
        unit: "",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: false,
        is_custom_cell: true
    },
    {
        key: "cost_per_result",
        title: "Cost per Result",
        type: "string",
        unit: "CURRENCY",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: false,
        is_custom_cell: true
    },
    {
        key: "result_rate",
        title: "Result Rate",
        type: "string",
        unit: "%",
        available: ["CAMPAIGN", "ADSET", "AD"],
        is_report: false,
        is_custom_cell: true
    }
];

// Get default fields for specific level
export function getDefaultFieldsForLevel(level) {
    return defaultCustomFields.filter(field => 
        !field.available || field.available.includes(level)
    );
}

// Get report fields only
export function getReportFields() {
    return defaultCustomFields.filter(field => field.is_report);
}

// Get custom cell fields only
export function getCustomCellFields() {
    return defaultCustomFields.filter(field => field.is_custom_cell);
}
