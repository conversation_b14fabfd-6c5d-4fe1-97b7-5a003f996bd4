import Alert from "/static/js/components/alert.js";
import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
import { requestApi } from "/static/js/common/httpService.js";
import { getFractionDigits } from "/static/js/facebook/helper.js";
import { defaultCustomFields } from "/static/js/facebook/campaign_config_table.js";

const OFFSET_COLUMN = 4; // Number of fixed columns (checkbox, status, name, objective)

function disableDragColumn() {
    let isFixedCol = false;

    $(document).on("mousedown", "th[data-dt-column]", function () {
        const colIndex = parseInt($(this).data("dt-column"), 10);
        isFixedCol = colIndex < OFFSET_COLUMN;
    });

    $(document).on("mouseup", function () {
        isFixedCol = false;
    });

    $(document).on("mousemove", function (e) {
        if (isFixedCol) {
            e.stopImmediatePropagation();
        }
    });
}

export const defaultConfigTableFacebook = ({ displayStart }, titleExcel = "Facebook Campaigns") => {
    disableDragColumn();
    return {
        initComplete: function () {
            const api = this.api();
            const $table = $(api.table().node());
            
            // Store the last column order
            $table.data("lastOrder", api.colReorder.order().slice());
            
            api.columns().every(function (idx) {
                const header = $(this.header());
                if (idx >= OFFSET_COLUMN) {
                    header.prepend('<span class="drag-icon me-2 fs-15 text-muted" style="float: left;">⠿</span>');
                }

                const columnIdx = this.index();

                // Set minimum width for columns
                const currentWidth = header.outerWidth();
                $(header).css("min-width", currentWidth + 25 + "px");
                $(header).css("max-width", currentWidth + 25 + "px");
                $(header).css("width", currentWidth + 25 + "px");
            });

            // Initialize column reordering
            if (api.colReorder) {
                api.colReorder.order(getColumnOrder());
            }

            // Save column order on change
            api.on('column-reorder', function (e, settings, details) {
                saveColumnOrder(api.colReorder.order());
            });
        },
        
        processing: true,
        serverSide: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        
        dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
             "<'row'<'col-sm-12'tr>>" +
             "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        
        language: {
            processing: "Loading...",
            search: "Search:",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            },
            emptyTable: "No data available in table"
        },
        
        // Column reordering
        colReorder: {
            fixedColumnsLeft: OFFSET_COLUMN,
            realtime: false
        },
        
        // Responsive
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        },
        
        // Styling
        className: 'table-striped table-hover',
        
        // Export buttons
        buttons: [
            {
                extend: 'excel',
                title: titleExcel,
                className: 'btn btn-success btn-sm',
                text: '<i class="ri-file-excel-line"></i> Excel'
            },
            {
                extend: 'csv',
                title: titleExcel,
                className: 'btn btn-info btn-sm',
                text: '<i class="ri-file-text-line"></i> CSV'
            }
        ],
        
        // Custom search delay
        searchDelay: 500,
        
        // Error handling
        error: function (xhr, error, code) {
            console.error('DataTable error:', error, code);
            errorAlert('Error loading table data. Please refresh the page.');
        }
    };
};

// Get custom columns for Facebook table
export function getCustomColumnsFacebook(customFields, level = "CAMPAIGN") {
    let columnTable = customFields.map((field) => {
        if (field.hasOwnProperty("available") && !field.available.includes(level)) return null;

        const col = {
            name: field.key,
            title: field.title,
            orderable: false,
            className: field?.type === "string" ? "align-middle text-start" : "align-middle text-end",
            data: null,
        };

        // Cell specific handling for different field types
        switch (field.key) {
            case "budget":
                return getBudgetColumnHtml(col, field, level);
            case "bid_strategy":
            case "optimization_goal":
                return getBidStrategyColumnHtml(col, field);
            case "result":
            case "result_rate":
            case "cost_per_result":
                return getResultColumnHtml(col, field);
            default:
                return getDefaultColumnHtml(col, field);
        }
    }).filter(col => col !== null);

    return columnTable;
}

function getBudgetColumnHtml(col, field, level) {
    return {
        ...col,
        render: function (data, type, row) {
            if (type === 'display') {
                const budget = row.budget || 0;
                const currency = row.account_currency || 'USD';
                return `<span class="budget-cell">${currency} ${formatCurrency(budget)}</span>`;
            }
            return data;
        }
    };
}

function getBidStrategyColumnHtml(col, field) {
    return {
        ...col,
        render: function (data, type, row) {
            if (type === 'display') {
                const bidStrategy = row.bid_strategy || 'N/A';
                return `<span class="bid-strategy-cell">${bidStrategy}</span>`;
            }
            return data;
        }
    };
}

function getResultColumnHtml(col, field) {
    return {
        ...col,
        render: function (data, type, row) {
            if (type === 'display') {
                const value = row[field.key] || 0;
                if (field.unit === '%') {
                    return `<span class="result-cell">${formatPercentage(value)}</span>`;
                } else if (field.unit === 'CURRENCY') {
                    const currency = row.account_currency || 'USD';
                    return `<span class="result-cell">${currency} ${formatCurrency(value)}</span>`;
                } else {
                    return `<span class="result-cell">${formatNumber(value)}</span>`;
                }
            }
            return data;
        }
    };
}

function getDefaultColumnHtml(col, field) {
    return {
        ...col,
        render: function (data, type, row) {
            if (type === 'display') {
                const value = row[field.key] || '';
                if (field.unit === '%') {
                    return formatPercentage(value);
                } else if (field.unit === 'CURRENCY') {
                    const currency = row.account_currency || 'USD';
                    return `${currency} ${formatCurrency(value)}`;
                } else if (field.type === 'int32' || field.type === 'float64') {
                    return formatNumber(value);
                } else {
                    return value;
                }
            }
            return data;
        }
    };
}

// Utility functions
function formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value);
}

function formatPercentage(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value / 100);
}

function formatNumber(value) {
    return new Intl.NumberFormat('en-US').format(value);
}

// Column order management
function getColumnOrder() {
    const saved = localStorage.getItem('facebook_table_column_order');
    return saved ? JSON.parse(saved) : null;
}

function saveColumnOrder(order) {
    localStorage.setItem('facebook_table_column_order', JSON.stringify(order));
}

// Custom field management
export function getCustomFields() {
    const saved = localStorage.getItem('facebook_custom_fields');
    return saved ? JSON.parse(saved) : defaultCustomFields;
}

export function saveCustomFields(fields) {
    localStorage.setItem('facebook_custom_fields', JSON.stringify(fields));
}

// Table event handlers
export const onDefaultTableFacebook = (tableElRef, table, storageKey, callBackRowChange) => {
    const checkSelectPath = "input.row-select";

    // Handle row selection
    tableElRef.on("change", checkSelectPath, function () {
        const isChecked = $(this).prop("checked");
        const tr = $(this).closest("tr");
        
        if (isChecked) {
            tr.addClass("selected");
            table.row(tr).select();
        } else {
            tr.removeClass("selected");
            table.row(tr).deselect();
        }
        
        callBackRowChange();
    });

    // Handle row click
    tableElRef.on("click", "tr", function (event) {
        if ($(event.target).is(checkSelectPath)) {
            return;
        }
        
        const checkbox = $(this).find(checkSelectPath);
        const isChecked = !checkbox.prop("checked");
        
        checkbox.prop("checked", isChecked).trigger("change");
    });

    // Table events
    table.on("select", function (e, dt, type, indexes) {
        if (type === "row") {
            callBackRowChange();
        }
    });

    table.on("deselect", function (e, dt, type, indexes) {
        if (type === "row") {
            callBackRowChange();
        }
    });
};

export const onSelectAllRowFacebook = (tableElRef, table, callBack) => {
    tableElRef.find("#selectAll").on("click", function () {
        const isChecked = $(this).prop("checked");
        $(".row-select").prop("checked", isChecked);

        if (isChecked) {
            table.rows({ page: "current" }).select();
        } else {
            table.rows({ page: "current" }).deselect();
        }
        callBack();
    });
};
