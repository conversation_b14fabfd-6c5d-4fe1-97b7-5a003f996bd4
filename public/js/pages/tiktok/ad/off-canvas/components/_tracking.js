export default (() => {
    const adForm = $("#ad-form");
    const tracking = adForm.find(".ad-component_tracking");
    const trackingEditEventTrackingBtn = tracking.find("#editEventTrackingBtn");
    const trackingEventBriefInfo = tracking.find(".tiktok-events-tracking-blk");
    const trackingEventTrackingDetails = tracking.find(".tracking-event-tracking");

    function onClickEditTrackingEvents() {
        trackingEditEventTrackingBtn.on("click", function () {
            trackingEventBriefInfo.addClass("d-none");
            trackingEventTrackingDetails.show();
        });
    }

    function init() {
        onClickEditTrackingEvents();
    }

    return {
        init,
    }
})()