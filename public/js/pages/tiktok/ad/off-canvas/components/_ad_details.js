import { CTA_OPTIONS } from "/static/js/pages/tiktok/ad/off-canvas/constant.js"
export default (() => {
    let isIniatializedAdDetails = false;

    const adForm = $("#ad-form");
    const adDetails = adForm.find(".ad_component_ad_details");
    const adDetailsCallToActionToggle = adDetails.find("#ad_creative_cta_toggle");
    const adDetailsCallToActionSetup = adDetails.find(".ad-creative-cta-setup");
    const adDetailsCallToActionType = adDetails.find('[name="cta-type"]');
    const adDetailsCallToActionDynamicContent = adDetails.find('[name="cta-type-dynamic-content"]');
    const adDetailsCallToActionStandardContent = adDetails.find('[name="cta-type-standard-content"]');
    const adDetailsCallToActionSelect = adDetails.find('[name="cta-type-select"]');

    const adDetailsDestinationOptions = adDetails.find('[name="destination-type-btn"]');
    const adDetailsDestinationSetUp = adDetails.find('.destination_setup');
    const adDetailsDestinationWebsiteContent = adDetailsDestinationSetUp.find('.destination_website_content');
    const adDetailsDestinationDeeplinkToggle = adDetailsDestinationSetUp.find('#destination_url_deeplink_first_toggle');
    const adDetailsDestinationDeeplinkInputContainer = adDetailsDestinationSetUp.find('.destination_deeplink_input');
    const adDetailsDestinationTiktokInstantPageContent = adDetailsDestinationSetUp.find('.destination_tiktok_instant_page_content');

    function initCallToActionSelect() {
        if (adDetailsCallToActionDefault) {
            adDetailsCallToActionDefault.empty();
            console.log(adDetailsCallToActionDefault);
            Object.values(CTA_OPTIONS).forEach((cta) => {
                const option = $('<option>', {
                    value: cta.value,
                    text: cta.text
                });
                adDetailsCallToActionDefault.append(option);
            });

            adDetailsCallToActionDefault.select2({
                placeholder: 'Select CTA',
                width: '100%',
                allowClear: true,
                multiple: true,
                closeOnSelect: false,
            });
        }
    }


    function onToggleCallToAction() {
        adDetailsCallToActionToggle.on("change", function () {
            const isCallToAction = $(this).is(":checked");
            toggleDestination(isCallToAction)

            isCallToAction ? adDetailsCallToActionSetup.removeClass("d-none") : adDetailsCallToActionSetup.addClass("d-none");
        }).trigger("change");
    }

    function onChangeCallToActionType() {
        adDetailsCallToActionType.on("change", function () {
            if ($(this).is(":checked")) {
                const ctaType = $(this).val();
                adDetailsCallToActionDynamicContent.toggle(ctaType === "dynamic");
                adDetailsCallToActionStandardContent.toggle(ctaType === "standard");
            }
        }).trigger("change");
    }

    function toggleDestination(enabled) {
        adDetailsDestinationOptions.prop("disabled", !enabled);

        if (enabled) {
            let anyChecked = false;

            adDetailsDestinationOptions.each(function (index) {
                const label = $(`label[for="${this.id}"]`);
                label.removeClass("disabled text-muted");

                if (this.checked) {
                    anyChecked = true;
                }
            });

            if (!anyChecked) {
                const first = adDetailsDestinationOptions.get(0);
                if (first) {
                    first.checked = true;
                    $(first).trigger("change");
                }
            } else {
                const selected = adDetailsDestinationOptions.filter(":checked").get(0);
                if (selected) {
                    $(selected).trigger("change");
                }
            }
        } else {
            adDetailsDestinationOptions.each(function () {
                this.checked = false;
                const label = $(`label[for="${this.id}"]`);
                label.addClass("disabled text-muted");
            });

            adDetailsDestinationSetUp.hide();
            adDetailsDestinationWebsiteContent.hide();
            adDetailsDestinationTiktokInstantPageContent.hide();
        }
    }

    function onChangeDestinationType() {
        adDetailsDestinationOptions.on("change", function (e) {
            const destinationValue = $(this).val();
            switch (destinationValue) {
                case "website": {
                    adDetailsDestinationSetUp.show();
                    adDetailsDestinationWebsiteContent.show(220);
                    adDetailsDestinationTiktokInstantPageContent.hide();
                    break;
                };
                case "tiktok_instant_page": {
                    adDetailsDestinationSetUp.show();
                    adDetailsDestinationWebsiteContent.hide();
                    adDetailsDestinationTiktokInstantPageContent.show(220);
                    break;
                }
                case "": {
                    adDetailsDestinationSetUp.hide();
                    adDetailsDestinationWebsiteContent.hide();
                    adDetailsDestinationTiktokInstantPageContent.show(220);
                    break;
                }
                default: {
                    break;
                }
            }
        })
    }

    function onToggleDestinationDeeplink() {
        adDetailsDestinationDeeplinkToggle.on("change", function () {
            const isDeeplink = $(this).is(":checked");
            if (isDeeplink) {
                adDetailsDestinationDeeplinkInputContainer.removeClass("d-none");
                adDetailsDestinationDeeplinkInputContainer.show(220)
            } else {
                adDetailsDestinationDeeplinkInputContainer.hide(220)
                adDetailsDestinationDeeplinkInputContainer.addClass("d-none");
            }
        }).trigger("change");
    }

    function renderCalToActionSelect() {
        adDetailsCallToActionSelect.empty();
        Object.values(CTA_OPTIONS).forEach((cta) => {
            const option = $('<option>', {
                value: cta.value,
                text: cta.text
            });
            adDetailsCallToActionSelect.append(option);
        });
    }


    function init() {
        if (!isIniatializedAdDetails) {
            isIniatializedAdDetails = true;
            initCallToActionSelect();
            onChangeCallToActionType();
            onToggleCallToAction();
            onChangeDestinationType();
            onToggleDestinationDeeplink();
            renderCalToActionSelect();
        }
    }

    return {
        init,
    }
})()