import { getTable, reloadTable, getFilter } from "/static/js/pages/audiences/list.js";
export default (() => {
  let isInit = true;
  const ID_CLIENTS = "#clientsChoices";
  const ID_ADACCOUNT = "#adAccountChoices";

  let dataItems = [];

  function getOptionData() {
    $(ID_CLIENTS)
      .find("option")
      .each(function (index) {
        dataItems.push({
          value: $(this).val(),
          label: $(this).data("label"),
          disabled: !index,
          selected: !index,
          customProperties: {
            icon: $(this).data("icon"),
          },
        });
      });
    $(ID_CLIENTS).empty();
    return dataItems;
  }

  /**
   * Template user option
   */
  function getOptionTemplate(template) {
    return {
      item: ({ classNames }, data) => {
        return template(`
                    <div data-ok class="${classNames.item} ${data.highlighted ? classNames.highlightedState : classNames.itemSelectable}" data-item data-id="${data.id}"  data-value="${data.value}">
                        ${data.label}
                        <button type="button" class="${classNames.button} btn-remove-selected" data-value="${data.value}" aria-label="Remove item: ${data.label}" data-button="">Remove item</button>
                    </div>
                `);
      },
      choice: ({ classNames }, data) => {
        return template(`
                  <div class="${classNames.item} ${classNames.itemChoice} ${data.disabled ? classNames.itemDisabled : classNames.itemSelectable}" data-select-text="${this.config.itemSelectText}" data-choice ${data.disabled ? 'data-choice-disabled aria-disabled="true"' : "data-choice-selectable"} data-id="${
          data.id
        }" data-value="${data.value}" ${data.groupId > 0 ? 'role="treeitem"' : 'role="option"'} >                 
                     <div class="d-flex align-items-center gap-2">
                       ${
                         data.customProperties?.icon
                           ? ` <img class="avatar-xs rounded" src="${data.customProperties.icon}" />
                          <div>
                          <span class="fw-semibold">${data.label}</span><br>
                          <span>ID: ${data.value}</span>
                          </div>`
                           : `<span class="fw-semibold">${data.label}</span>`
                       }
                     </div>
                  </div>
                `);
      },
    };
  }

  /**
   * Render User Info Profile
   */
  async function renderClientsListSelectionUI() {
    let choices = new Choices(ID_CLIENTS, {
      shouldSort: false,
      allowHTML: true,
      removeItemButton: true,
      renderSelectedChoices: "auto",
      wrap: false,
      placeholder: true,
      searchEnabled: true,
      callbackOnCreateTemplates: getOptionTemplate,
      searchPlaceholderValue: "Search...",
      choices: getOptionData(),
    });
  }

  /**
   * Render User Info Profile
   */
  function onChangeSelectClient() {
    $(ID_CLIENTS).on("change", function () {
      window.ClientID = $(this).val();
      reloadTable(window.ClientID, window.AdAccountId);
    });
  }

  /**
   * On Change Select Add Account
   */
  function onChangeSelectAddAccount() {
    $(ID_ADACCOUNT).on("change", function () {
      window.AdAccountId = $(this).val();
      reloadTable(window.ClientID, window.AdAccountId);
    });
  }

  /**
   * Init User
   */
  function init() {
    const user = getUserInfo();
    if (user && user.role_name === "ADMIN") {
      renderClientsListSelectionUI();
      onChangeSelectClient();
      onChangeSelectAddAccount();
      isInit = false;
    }
  }
  return {
    init,
  };
})();
