import { getCookie } from "/static/js/common/helpers.js";
import { TIKTOK_API_URL } from "/static/js/common/httpService.js";
import { PATHS } from "/static/js/tiktok/constants/routes.js";
import { onUpdateSelectedRowTableToURL, onDefaultTable, onSelectAllRow, getCustomColumn } from "/static/js/tiktok/list-table-ads/helper.js";
import { ADGROUP_OPERATION_STATUS_ON } from "/static/js/tiktok/constants/adgroup.js";
import { STATUS_ENUMS, ENTITY_TYPES } from "/static/js/tiktok/constants/status.js";
import { defaultConfigTableTiktok, HandlerError, getHeaderTable, renderFooterTableByMetric, getCustomFieldTable } from "/static/js/tiktok/list-table-ads/config-table.js";
// import { customFields } from "/static/js/tiktok/ads/ad_config_table.js";
import { DATETIME_FORMAT_TYPES } from "/static/js/constants/common-constant.js";

export default (() => {
    // export function CampaignList() {
    // const htmlSelectedCamp = $("#teamplate_camp_btn_selected").html();
    let isFristLoad = true;
    const nameAdsetSelected = $("#selected_adgroup_name");
    const nameAdSeltected = $("#selected_ad_name");
    const ID_TABLE = "data-table-adgroup";
    const ID_OFFCANVAS = "adGroupOffCanvas";

    const tableSelector = "table#data-table-adgroup";
    const tableElRef = $(tableSelector);
    const headerTable = ["Off/On", "Name", "Status"];
    const eventManager = {
        renderFooter: null,
    };
    let customFields = [];
    let campaignIdSelected = [];

    let datatable;
    const filterData = { filter: { search: "", page: 1, start: 0, time: null, adgroup_id: "", campaign_ids: [], nameUser: "", userId: "", advertiserId: "", clientId: "" } };

    /**
     * Init Table p
     */
    const initTable = function () {
        tableElRef.html(getHeaderTable(headerTable));
        datatable = tableElRef.DataTable({
            ...defaultConfigTableTiktok({ displayStart: filterData.filter.start }, "Adgroups"),
            columns: [
                {
                    data: null,
                    orderable: false,
                    searchable: false,
                    width: "20px",
                    className: "align-middle",
                    render: function (data, type, row) {
                        return `<div class="form-check" >
                                    <input class="form-check-input row-select" type="checkbox" name="chk_child" value="${row.campaign_id}">
                                </div>`;
                    },
                },
                {
                    orderable: false,
                    width: "30px",
                    className: "align-middle",
                    data: function (row) {
                        return `
                        <div class="form-check form-switch form-check-inline" dir="ltr">
                            <input
                                type="checkbox"
                                class="form-check-input form-switch-md"
                                id="switch-check-${row.adgroup_id}"
                                ${row.operation_status === ADGROUP_OPERATION_STATUS_ON ? "checked" : ""} />
                        </div>
                    `;
                    },
                },
                {
                    title: "Name",
                    name: "adgroup_name",
                    render: function (data, type, row) {
                        return `
                        <div class="name-blk ">
                            <a href="#" class="text-nowrap-3" title="${row.adgroup_name}">${row.adgroup_name}</a>
                            <div class="action-blk">
                                <button class="btn p-0 me-1" hidden><i class="ri-bar-chart-box-line"></i> Charts</button>                         
                                <button
                                    class="btn p-0 me-1 btn-edit-adgroup btn-small"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#OffCanvasCampAdgroupAd"
                                    aria-controls="OffCanvasCampAdgroupAd"><i class="ri-pencil-fill"></i> Edit</button>                         
                                <button class="btn p-0 me-1" hidden><i class="ri-file-copy-2-fill"></i> Duplicate</button>                         
                                <button class="btn p-0 me-1" hidden><i class="ri-pushpin-fill"></i> Pin</button>                         
                            </div>
                        </div>
                    `;
                    },
                },
                {
                    title: "Status",
                    name: "secondary_status",
                    className: "align-middle",
                    render: function (data, type, row) {
                        const status = STATUS_ENUMS[ENTITY_TYPES.ADGROUP]?.[row.secondary_status];
                        if (status) {
                            return `<i class="${status.iconClass + " " + status.textColorClass} align-middle"></i> ${status.text}`;
                        }
                        return `<i class="ri-question-circle-fill text-warning align-middle"></i>  row.secondary_status}`;
                    },
                },
                ...getCustomColumn(customFields, "ADGROUP"),
            ],
            ajax: onAjaxTable,
        });
        initAddEventForTable();

        // if (isFristLoad) {
        // }
        $("#table#data-table-adgroup thead th").each(function (index) {
            if (index < 3) {
                $(this).addClass("no-drag");
                $(this).on("mousedown", function (e) {
                    e.stopImmediatePropagation(); // Ngăn colReorder khởi động
                });
            }
        });
        tableElRef.off("preDraw.dt", eventManager.renderFooter);
        eventManager.renderFooter = renderFooterTableByMetric(tableElRef, datatable, "adgroups", customFields, "ADGROUP", ID_TABLE);
    };

    /**
     * Update filter table
     */
    function updateFilterTable() {
        let currentPage = getParamURL("page");
        filterData.filter.page = currentPage || 1;
        currentPage = currentPage ? parseInt(currentPage, 10) : 1;
        filterData.filter.start = (currentPage - 1) * 10;
        filterData.filter.campaign_ids = getCampaignIdsSelected();

        const datePicker = $("#filter-search-datetime").data("daterangepicker");
        const start = datePicker.startDate.format(DATETIME_FORMAT_TYPES.UTC);
        const end = datePicker.endDate.format(DATETIME_FORMAT_TYPES.UTC);

        filterData.filter.time = [start, end];
    }

    /**
     * Ajax config
     */
    function onAjaxTable(data, callback) {
        updateFilterTable();
        $.ajax({
            url: TIKTOK_API_URL.ADGROUPS,
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            headers: {
                "X-CSRF-Token": getCookie("csrf_"), // Hàm lấy CSRF token
            },
            data: {
                ...data,
                ...filterData.filter,
            },
            success: function (response) {
                callback({
                    data: response.data,
                    recordsTotal: response.recordsTotal, // Tổng số bản ghi
                    recordsFiltered: response.recordsFiltered, // Số bản ghi sau khi filter
                });
            },
            error: function (xhr, status, error) {
                HandlerError(xhr, status, error, ID_TABLE);
            },
        });
    }

    /**
     * Init Add Event For Table
     */
    function initAddEventForTable() {
        onDefaultTable(tableElRef, datatable, "selected_adgroup_ids", onSelectRowChange);

        // On Select All
        onSelectAllRow(tableElRef, datatable, onSelectRowChange);

        /**
         * Edit Campaign
         */
        tableElRef.on("click", "tr td button.btn-edit-adgroup", function () {
            removeParamURL("edit_campaign_id");
            removeParamURL("edit_adgroup_id");
            removeParamURL("edit_ad_id");

            const tr = $(this).closest("tr");
            const id = tr.attr("id");

            if (id) {
                const router = new Navigo("/");
                router.navigate(`${PATHS.adgroup.edit}?edit_adgroup_id=${id.replace("row_", "")}&${getCurrentQuery()}`);
            }
        });
    }

    /**
     * On Select Row Change
     */
    const onSelectRowChange = () => {
        const nameAdSeltected = $("#selected_ad_name");
        const adsetSelected = $("#adgroup_selected");
        const selects = datatable.rows({ selected: true }).data().toArray();
        onUpdateSelectedRowTableToURL(selects, "selected_adgroup_ids", "adgroup_id");
        if (!selects || selects.length === 0) {
            nameAdSeltected.text("Ads");
            return adsetSelected.addClass("d-none");
        }
        adsetSelected
            .contents()
            .filter(function () {
                return this.nodeType === Node.TEXT_NODE && this.nodeValue.trim() !== "";
            })
            .first()
            .replaceWith(selects.length + " selected");

        nameAdSeltected.text(`Ads for  ${selects.length} Adgroups`);
        adsetSelected.removeClass("d-none");
    };

    /**
     * On Close Campaign select
     */
    const onCloseSelected = () => {
        $("#adgroup_selected #adgroup-selected-close").on("click", () => {
            datatable.rows({ page: "current" }).deselect();
            datatable.$("input.row-select").prop("checked", false); // Uncheck checkboxes
            onSelectRowChange();
        });
    };

    /**
     * On Change select user filter
     */
    const onSelectUserFilter = (data) => {
        filterData.filter.clientId = data?.clientId || "";
        filterData.filter.advertiserId = data?.advertiserId || "";

        if (!datatable) return;
        datatable.ajax.reload();
    };

    /**
     * Get campaignIdsSelected form URL
     */
    function getCampaignIdsSelected(params) {
        if (!params) {
            const url = new URL(window.location.href);
            const campIds = url.searchParams.get("selected_campaign_ids");
            if (campIds) {
                return campIds.split(",");
            } else return [];
        }
        let ids = [];
        if (params?.params?.selected_campaign_ids) {
            ids = params.params.selected_campaign_ids.split(",");
        }
        return ids;
    }

    /**
     * Is Campaign Selected Change
     */
    function compareArrays(a, b) {
        if (a.length !== b.length) {
            return true;
        }
        for (let i = 0; i < a.length; i++) {
            if (a[i] !== b[i]) {
                return true;
            }
        }
        return false;
    }

    /**
     * Reload Data table
     */
    const onActiveAdgroupTab = () => {
        document.addEventListener("adgroupTabActive", (event) => {
            if (event.detail.init) {
                // if (event.detail.isReloadPage) {
                //     updateFilterTable();
                // } else {
                //     updateFilterTable();
                //     filterData.filter.page = 1;
                //     filterData.filter.start = 0;
                //     setParamURL("page", 1);
                //     setTimeout(() => setParamURL("page", 1), 50);
                // }
                filterData.filter.page = 1;
                filterData.filter.start = 0;
                setParamURL("page", 1);
                setTimeout(() => setParamURL("page", 1), 50);
                updateFilterTable();
                initAdgroup();
            } else {
                setTimeout(() => {
                    const page = datatable.page.info();
                    setParamURL("page", page.page + 1);
                }, 50);
                const ids = getCampaignIdsSelected(event.detail.params);
                if (compareArrays(ids, campaignIdSelected)) {
                    campaignIdSelected = ids;
                    datatable.ajax.reload();
                    removeParamURL("selected_adgroup_ids");
                }
            }
        });
    };

    /**
     * Filter table change
     */
    const initOnChangeFilterTable = (event) => {
        document.addEventListener("onUpdateFilterChange", function (event) {
            if ($(".tab-pane.active#adgroup").length > 0) {
                const { start, end, search, id } = event.detail;
                filterData.filter.time = [start, end];
                if (!datatable) return;

                if (!!start && !!end && $('#navTabTableAds li > a[data-tab="adgroups"].active').length > 0) {
                    datatable.ajax.reload();
                }

                if (search || search === "") {
                    filterData.filter.search = search;
                    filterData.filter.adgroup_id = "";
                    datatable.ajax.reload();
                }

                if (id) {
                    filterData.filter.search = "";
                    filterData.filter.adgroup_id = id;
                    datatable.ajax.reload();
                }
            }
        });
    };

    /**
     * Change config column table
     */
    async function inChangeConfigColumnTable() {
        document.addEventListener("onChangeConfigColumnTable", async function (event) {
            if ($(".tab-pane.active#adgroup").length > 0) {
                datatable.destroy();
                $(`#${ID_TABLE}`).empty();
                customFields = await getCustomFieldTable();
                initTable();
            }
        });
    }

    async function initAdgroup() {
        customFields = await getCustomFieldTable();
        initTable();
        onCloseSelected();
    }

    function init() {
        if (!isFristLoad) return;

        isFristLoad = false;
        inChangeConfigColumnTable();
        onActiveAdgroupTab();
        initOnChangeFilterTable();
    }

    $().ready(function () {
        init();
    });

    return {
        onSelectUserFilter,
        datatable,
    };
    // }
})();
