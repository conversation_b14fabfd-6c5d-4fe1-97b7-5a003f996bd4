//SHOPPING_ADS_TYPE
export const SHOPPING_ADS_TYPE_UNSET = "UNSET",
    SHOPPING_ADS_TYPE_LIVE = "LIVE",
    SHOPPING_ADS_TYPE_CATALOG_LISTING_ADS = "CATALOG_LISTING_ADS",
    SHOPPING_ADS_TYPE_VIDEO = "VIDEO";

// AGE TARGETING
export const ageMapTargeting = {
    AGE_13_17: "13-17",
    AGE_18_24: "18-24",
    AGE_25_34: "25-34",
    AGE_35_44: "35-44",
    AGE_45_54: "45-54",
    AGE_55_100: "55+",
};

// GENDER TARGETING
export const genderMapTargeting = {
    GENDER_UNLIMITED: "GENDER_UNLIMITED",
    GENDER_MALE: "GENDER_MALE",
    GENDER_FEMALE: "GENDER_FEMALE",
};

// GENDER SPENDINGPOWER TARGETING
export const spendingpowerTargeting = {
    SPENDINGPOWER_ALL: "ALL",
    SPENDINGPOWER_HIGH: "HIGH",
};

// BILLING EVENT
export const BILLING_EVENT_LABELS = {
    CPC: "Click (CPC)",
    CPM: "Impression (CPM)",
    CPV: "View (CPV)",
    OCPC: "Optimized Click (oCPC)",
    GD: "Guaranteed Delivery (GD)",
    OCPM: "Impression (oCPM)",
};

// Delivery Type
export const BID_TYPE_PACING_OPTIONS = {
    BID_TYPE_CUSTOM: ["PACING_MODE_SMOOTH", "PACING_MODE_FAST"],
    BID_TYPE_NO_BID: ["PACING_MODE_SMOOTH"],
};
export const PACING_DISPLAY_MAP = {
    PACING_MODE_SMOOTH: "Standard",
    PACING_MODE_FAST: "Accelerated",
};
