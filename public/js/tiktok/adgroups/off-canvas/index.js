import { success<PERSON><PERSON><PERSON>, errorAlert, loader } from "/static/js/common/helpers.js";
import renderAdgroupDataModule from "/static/js/tiktok/adgroups/render_content_adgroup.js";
import locationTargetingUI from "/static/js/tiktok/adgroups/components/_locations.js";
import languageTargetingUI from "/static/js/tiktok/adgroups/components/_languages.js";
import audienceTargetingUI from "/static/js/tiktok/adgroups/components/_audiences.js";
import interestBehaviorTargetingUI from "/static/js/tiktok/adgroups/components/_interest_behavior.js";
import budgetScheduleUI from "/static/js/tiktok/adgroups/components/_budget_schedule.js";
import optimizationGoalUI from "/static/js/tiktok/adgroups/components/_optimization_goal.js";
import placementUI from "/static/js/tiktok/adgroups/components/_placement.js";
import optimizationLocationUI from "/static/js/tiktok/adgroups/components/_optimization_location.js";
import editAdgroup from "/static/js/tiktok/adgroups/render_content_adgroup.js";
import carrierTargetingUI from "/static/js/tiktok/adgroups/components/_carriers.js";
import ispTargetingUI from "/static/js/tiktok/adgroups/components/_internet_service_provider.js"
import devicePriceUI from "/static/js/tiktok/adgroups/components/_device_price.js"
import identityUI from "/static/js/tiktok/adgroups/components/_identity.js"
export default (() => {
    async function renderAdgroupOffCanvasContent() {
        try {
            loader();
            const adgroupId = getParamURL("edit_adgroup_id");
            if (!adgroupId) throw new Error("Thiếu adgroup_id");

            const adgroupRes = await fetch("/dsp/tiktok/api/adgroup/get-details", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: JSON.stringify({
                    adgroup_id: adgroupId,
                    // advertiser_id: "7015148626926141442",
                }),
            });

            if (!adgroupRes.ok) throw new Error("Lỗi khi fetch adgroup");

            const adgroupData = await adgroupRes.json();
            document.getElementById("tiktok-offcanvas-content").innerHTML = adgroupData.data.html;
            renderAdgroupDataModule.renderOffCanvasContent(adgroupData.data.data);
            const campaignId = adgroupData?.data?.data?.campaign_id;
            if (!campaignId) throw new Error("Không tìm thấy campaign_id từ adgroup");

            const campaignRes = await fetch("/dsp/tiktok/api/campaign/detail", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: JSON.stringify({ campaign_id: campaignId, active_id: adgroupId }),
            });

            if (!campaignRes.ok) throw new Error("Lỗi khi fetch campaign");

            const campaignData = await campaignRes.json();
            const sidebar = document.getElementById("menu-camp-adgroup-ad-left-side-bar");
            if (sidebar) sidebar.innerHTML = campaignData.html;
            init()
        } catch (error) {
            console.error("Không thể tải dữ liệu adgroup hoặc campaign:", error);
        } finally {
            loader(false);
        }
    }

    function init() {
        placementUI.init();
        identityUI.init();
        // optimizationLocationUI.init();
        locationTargetingUI.init();
        languageTargetingUI.init();
        audienceTargetingUI.init();
        interestBehaviorTargetingUI.init();
        budgetScheduleUI.init();
        optimizationGoalUI.init();
        // editAdgroup.init();
        carrierTargetingUI.init();
        ispTargetingUI.init();
        devicePriceUI.init();
    }

    return {
        init,
        renderAdgroupOffCanvasContent,
    };
})();
