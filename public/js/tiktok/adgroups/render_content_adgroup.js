import {
    SHOPPING_ADS_TYPE_LIVE,
    SHOPPING_ADS_TYPE_VIDEO,
    SHOPPING_ADS_TYPE_CATALOG_LISTING_ADS,
    SHOPPING_ADS_TYPE_UNSET,
    ageMapTargeting,
    BILLING_EVENT_LABELS,
    BID_TYPE_PACING_OPTIONS,
    PACING_DISPLAY_MAP,
} from "/static/js/tiktok/adgroups/constant.js";

import {
    OPTIMIZATION_GOAL,
    ADGROUP_BUDGET_MODE_INFINITE
} from "/static/js/tiktok/constants/adgroup.js";
import {
    OUTCOME_REACH,
    OUTCOME_TRAFFIC,
    OUTCOME_PRODUCT_SALES,
    CAMPAIGN_BUDGET_TITLE
} from "/static/js/tiktok/constants/campaign.js";

export default (function () {
    let isInit = false;
    const ID_OFFCANVAS = "tiktok-offcanvas-content";
    const ID_GENDER_TARGETING = "genderTargeting";
    const ID_SPENDINGPOWER_TARGETING = "spendingPowerTargeting";
    const ID_OPERATINGSYSTEM_TARGETING = "operatingSystemTargeting";
    const ID_CONNECTIONTYPE_TARGETING = "connectionTypeTargeting";
    const ID_DEVICEPRICE_TARGETING = "devicePriceTargeting";
    const ID_ADS_TYPE_CARDS = "ads-type-cards"
    const ID_OPTIMIZATION_LOCATION = "optimizationLocationCard"
    const ID_IDENTITY_SELECT = "identity_select"
    const ID_BID_CONTROL_INPUT = "bidAmount"

    /**
     * Render the offcanvas content with adgroup data.
     */
    function renderOffCanvasContent(data) {
        $(`#${ID_OFFCANVAS}`).find("#adgroupName").val(data.adgroup_name);


        // Shop Ads type (LIVE, VIDEO, PRODUCT)
        $(`#${ID_ADS_TYPE_CARDS}`).toggle(data.campaign.objective_type == OUTCOME_PRODUCT_SALES)
        const adType = data.shopping_ads_type || SHOPPING_ADS_TYPE_UNSET;
        setActiveShopAdsType(adType);

        setTimeout(() => {
            setIdentity(data);
        }, 200
        )

        // Placement - user comment
        setTimeout(() => {
            $("#outcomeObjective").val(data.campaign.objective_type).trigger("change");
            const userCommentCheckbox = document.getElementById("placementUserComment");
            if (data.comment_disabled === false) {
                userCommentCheckbox.setAttribute("checked", "checked");
                userCommentCheckbox.checked = true;
            } else {
                userCommentCheckbox.removeAttribute("checked");
                userCommentCheckbox.checked = false;
            }
        }, 200
        )

        //Optimization location
        $(`#${ID_OPTIMIZATION_LOCATION}`).toggle(data.campaign.objective_type == OUTCOME_TRAFFIC)

        // Targeting mapping
        targetingMapping(data);

        // Budget and schedule
        setBudget(data);
        setSchedule(data);

        // Optimization Goal
        renderOptimizationSelect2(data);
        // setBillingEvent(data);
        setBidControl(data);
        setROASBid(data);
        // setDeliveryType(data);
    }

    function targetingMapping(data) {
        ageMapping(data);
        genderMapping(data);
        spendingPowerMapping(data);
        deviceOperatingSystem(data);
        deviceConnectionType(data);

        setTimeout(() => {
            regionMapping(data);
            interestsAndBehaviorsMapping(data);
            carriersMapping(data);
            devicePrice(data);
            audienceMapping(data);
        }, 1500)
    }

    function audienceMapping(data) {
        $("#selectedAudiences").attr("data-value", JSON.stringify({}));

        data.audience_ids.forEach(audienceId => {
            console.log("🚀 ~ audienceMapping ~ audienceId:", audienceId)

            const $checkbox = $(`#searchSourceAudiencePopover .list-group-item[data-id="${audienceId}"]`);
            console.log("🚀 ~ audienceMapping ~  $checkbox:", $checkbox)

            if ($checkbox.length > 0) {
                $checkbox.trigger("click");
            } else {
                console.warn(`Không tìm thấy checkbox với id: ${audienceId}`);
            }
        })
    }

    function setBidControl(data) {
        $(`#${ID_BID_CONTROL_INPUT}`).val(data.bid_price);
    }

    function setIdentity(data) {
        $(`#${ID_IDENTITY_SELECT}`).val(data.identity_id).trigger("change")
    }

    function carriersMapping(data) {
        // console.log("🚀 ~ carriersMapping ~ data:", data.carrier_ids)
    }

    function interestsAndBehaviorsMapping(data) {
        $("#selectedDetailTargeting").attr("data-value", JSON.stringify({}));

        data.interest_category_ids.forEach(categoryId => {
            const $checkbox = $(`#browseDetailTargetingPopover .form-check-input[data-id="${categoryId}"]`);
            if ($checkbox.length > 0) {
                if (!$checkbox.prop("checked")) {
                    $checkbox.prop("checked", true).trigger("change");
                }
            } else {
                console.warn(`Không tìm thấy checkbox với id: ${categoryId}`)
            }
        })
    }

    function regionMapping(data) {
        $("#selectedGeoLocations").attr("data-value", JSON.stringify({}));

        data.location_ids.forEach(regionId => {
            const $checkbox = $(`#region-container .form-check-input#${regionId}`);

            if ($checkbox.length > 0) {
                $checkbox.trigger("click");
            } else {
                console.warn(`Không tìm thấy checkbox với id: region_${regionId}`);
            }
        });
    }

    /**
     * On Open Offcanvas
     */
    // function getAdgroupDetail() {
    //     const adgroupId = getParamURL("edit_adgroup_id");
    //     console.log("adgroup ID:", adgroupId);

    //     fetch("/dsp/tiktok/api/adgroup/get-details", {
    //         method: "POST",
    //         headers: {
    //             "Content-Type": "application/json",
    //             Accept: "application/json",
    //         },
    //         body: JSON.stringify({
    //             adgroup_id: adgroupId,
    //             // advertiser_id: "7015148626926141442",
    //         }),
    //     })
    //         .then((response) => response.json())
    //         .then((res) => {
    //             console.log(" 🚀 ~ .then ~ res:", res);
    //             renderOffCanvasContent(res.data.data);
    //         })
    //         .catch((error) => {
    //             console.error("Không thể tải dữ liệu chiến dịch:", error);
    //         });
    // }

    /**
     * Edit Adgroup offcanvas
     */
    function OnEditAdgroupOffcanvas() {
        const offcanvasEl = document.getElementById(ID_OFFCANVAS);
        if (getParamURL("edit_adgroup_id")) {
            var bsOffcanvas = new bootstrap.Offcanvas(offcanvasEl);
            bsOffcanvas.show();
        }
    }

    /**
     * On close offcanvas
     */
    function OnCloseOffcanvas() {
        $(`#${ID_OFFCANVAS}`).on("hidden.bs.offcanvas", () => {
            removeParamURL("edit_adgroup_id");
            const router = new Navigo("/");
            router.navigate(`/dsp/tiktok/adgroup/list?&${getCurrentQuery()}`);
        });
    }

    /**
     * On open offcanvas
     */
    function OnOpenOffcanvas() {
        $(`#${ID_OFFCANVAS}`).on("shown.bs.offcanvas", () => {
            // getAdgroupDetail();
        });
    }

    /**
     *
     * active shop ads type
     */
    function setActiveShopAdsType(type) {
        const cards = document.querySelectorAll(".ads-card");
        cards.forEach((card) => {
            card.classList.remove("active", "card-border-success");
            card.querySelector(".card-title")?.classList.remove("text-success");

            const cardType = card.dataset.type;
            if (
                (type === SHOPPING_ADS_TYPE_VIDEO && cardType === "video") ||
                (type === SHOPPING_ADS_TYPE_LIVE && cardType === "live") ||
                (type === SHOPPING_ADS_TYPE_CATALOG_LISTING_ADS && cardType === "product")
            ) {
                card.classList.add("active", "card-border-success");
                card.querySelector(".card-title")?.classList.add("text-success");
            }
        });

        // Show/hide sections
        const identitySection = document.querySelector("#ads-type-cards #identity-section");
        const showcaseInput = document.querySelector("#ads-type-cards #showcase-input");

        if (type === SHOPPING_ADS_TYPE_LIVE) {
            identitySection?.classList.remove("d-none");
            showcaseInput?.classList.add("d-none");
        } else {
            identitySection?.classList.add("d-none");

            if (type === SHOPPING_ADS_TYPE_VIDEO) {
                showcaseInput?.classList.remove("d-none");
            } else if (type === SHOPPING_ADS_TYPE_CATALOG_LISTING_ADS) {
                showcaseInput?.classList.add("d-none");
            }
        }
    }

    /**
     * Initialize Age targeting
     */
    function ageMapping(data) {
        const $container = $("#ageTargeting");
        const $checkboxes = $container.find('input[name="age"]');
        const $checkboxAll = $container.find('input[name="age"][value="all"]');

        // Reset all checkboxes
        $checkboxes.prop("checked", false);

        // Convert TikTok age codes to values used in checkbox values
        const selectedAges = (data.age_groups || []).map((code) => ageMapTargeting[code]);

        // Set checked boxes
        $checkboxes.each(function () {
            if (selectedAges.includes(this.value)) {
                this.checked = true;
            }
        });

        // Handle "All" logic
        if (selectedAges.length === 0) {
            $checkboxAll.prop("checked", true);
        } else {
            $checkboxAll.prop("checked", false);
        }
    }

    /**
     * Initialize Gender targeting
     */
    function genderMapping(data) {
        $(`#${ID_GENDER_TARGETING} input[name="render"]`).prop("checked", false);
        $(`#${ID_GENDER_TARGETING} input[name="render"][value="${data.gender}"]`).prop("checked", true);
    }

    /**
     * Initialize Spending Power
     */
    function spendingPowerMapping(data) {
        $(`#${ID_SPENDINGPOWER_TARGETING} input[name="spending_power"]`).prop("checked", false);
        $(`#${ID_SPENDINGPOWER_TARGETING} input[name="spending_power"][value="${data.spending_power}"]`).prop("checked", true);
    }

    /**
     * Initialize Device Operating System
     */
    function deviceOperatingSystem(data) {
        $(`#${ID_OPERATINGSYSTEM_TARGETING} input[name="operating_system"]`).prop("checked", false);
        $(`#${ID_OPERATINGSYSTEM_TARGETING} input[name="operating_system"][value="${data.operating_systems}"]`).prop("checked", true);
    }

    /**
     * Initialize Connection Type
     */
    function deviceConnectionType(data) {
        $(`#${ID_CONNECTIONTYPE_TARGETING} input[name="connection_type"]`).prop("checked", false);
        $(`#${ID_CONNECTIONTYPE_TARGETING} input[name="connection_type"][value="${data.network_types}"]`).prop("checked", true);
    }

    /**
     * Initialize device price
     */
    function devicePrice(data) {

        const $container = $(`#${ID_DEVICEPRICE_TARGETING}`);
        const $radioAny = $container.find('input[name="device_price"][value=""]');
        const $radioSpecific = $container.find('input[name="device_price"][value="specific"]');
        const $specificRange = $container.find("#specificRange");
        const min = Math.min(...data.device_price_ranges);
        const max = Math.max(...data.device_price_ranges) + 50;

        if (Array.isArray(data.device_price_ranges) && data.device_price_ranges.length > 0) {

            // Chọn Specific range
            $radioSpecific.prop("checked", true);
            $specificRange.show();

            // Gán giá trị min/max nếu tồn tại
            if (min !== undefined) $container.find("#minPrice").val(min);
            if (max !== undefined) $container.find("#maxPrice").val(max > 1000 ? "1000+" : max);
        } else {
            // Chọn Any price
            $radioAny.prop("checked", true);
            $specificRange.hide();

            // Reset min/max
            $container.find("#minPrice").val("");
            $container.find("#maxPrice").val("");
        }
    }

    /**
     * Initialize budget and schedule
     */
    function setBudget(data) {
        console.log("🚀 ~ setBudget ~ data:", data)
        const budgetSetupContainer = $("#adgroupBaseBudgetSetup")

        switch (data.budget_mode) {
            case ADGROUP_BUDGET_MODE_INFINITE: {
                budgetSetupContainer.empty().html($("#adgroupBaseBudgetSetup_CAMP").html());
                setBudgetCamp(data)
                break;
            }
            default: {
                budgetSetupContainer.empty().html($("#adgroupBaseBudgetSetup_ADGROUP").html());
                setBudgetAdgroup(data)
                break;
            }
        }

        function setBudgetCamp(data) {
            budgetSetupContainer.find("#camp_budget_mode").text(data.campaign.title = "BUDGET_MODE_DYNAMIC_DAILY_BUDGET" ? "Daily" : CAMPAIGN_BUDGET_TITLE[data.campaign.budget_mode]);
            budgetSetupContainer.find("#camp_budget").text(data.campaign.budget);
        }

        function setBudgetAdgroup(data) {
            const $budgetSelect = $("#adsetBaseBudgetType");
            const $budgetInput = $("#adsetBaseBudgetAmount");

            // Mapping từ budget_mode → select value
            const modeMap = {
                BUDGET_MODE_DAY: "daily",
                BUDGET_MODE_TOTAL: "life",
            };

            const selectValue = modeMap[data.budget_mode] || "daily";
            $budgetSelect.val(selectValue);
            $budgetInput.val(data.budget);
        }

    }

    function setSchedule(data) {
        const { schedule_type, schedule_start_time, schedule_end_time } = data;

        const formatDateTime = (datetimeStr) => {
            if (!datetimeStr) return "";

            const date = new Date(datetimeStr);

            const datePart = new Intl.DateTimeFormat("en-US", {
                month: "short",
                day: "2-digit",
                year: "numeric"
            }).format(date);

            const timePart = new Intl.DateTimeFormat("en-US", {
                hour: "numeric",
                minute: "2-digit",
                hour12: true
            }).format(date);

            return `${datePart} ${timePart}`;
        };


        $("#start-time-field").val(formatDateTime(schedule_start_time));

        if (schedule_type === "SCHEDULE_START_END") {
            $("#set-an-end-date").prop("checked", true);
            $("#end-time-field").val(formatDateTime(schedule_end_time));
        } else {
            $("#set-an-end-date").prop("checked", false);
            $("#end-time-field").val("");
        }
    }


    /**
     * Render Optimization Goal Select2
     * @param {Object} data
     */
    function renderOptimizationSelect2(data) {
        const $select2OptimizationGoal = $("#optimization_goal_select");

        if (!$select2OptimizationGoal.hasClass("select2-hidden-accessible")) {
            $select2OptimizationGoal.select2({
                placeholder: "Select Optimization Goal",
                data: OPTIMIZATION_GOAL.map((item) => ({
                    id: item.value,
                    text: item.title,
                    disabled: item.disabled,
                })),
                width: "100%",
            });
        }

        const selectedGoal = OPTIMIZATION_GOAL.find((goal) => goal.value === data.optimization_goal);

        if (selectedGoal) {
            $select2OptimizationGoal.val(selectedGoal.value).trigger("change");
        } else {
            $select2OptimizationGoal.val(null).trigger("change");
        }
    }

    /**
     * Initialize the Billing Event
     * @param {Object} data
     */
    function setBillingEvent(data) {
        const billingEvent = data.billing_event;
        const label = BILLING_EVENT_LABELS[billingEvent] || billingEvent;
        $("#billingEvent").val(label);
    }

    /**
     * set Delivery Type
     * @param {Object} data
     */
    function setDeliveryType(data) {
        const bidType = data.bid_type;
        const pacing = data.pacing;

        const validPacingOptions = BID_TYPE_PACING_OPTIONS[bidType] || [];

        const finalPacing = validPacingOptions.includes(pacing) ? pacing : validPacingOptions[0] || null;

        const displayText = PACING_DISPLAY_MAP[finalPacing] || "Unknown";

        $("#deliveryType").val(displayText);
    }

    /**
    * set ROAS Bid
    */
    function setROASBid(data) {
        if (!data) return;
        if (!data.roas_bid) {
            $("#roasGoal").hide();
            return;
        }
        $("#roasGoal").show();
        $("#roasBid").val(parseFloat(data.roas_bid).toFixed(2));
    }

    /**
     * Initialize
     */

    function init() {
        if (!isInit) {
            isInit = true;

            OnEditAdgroupOffcanvas();

            OnCloseOffcanvas();

            OnOpenOffcanvas();
        }
    }

    return {
        init,
        renderOffCanvasContent,
    };
})();

