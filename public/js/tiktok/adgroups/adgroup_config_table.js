import { ADGROUP_BUDGET_TITLE } from "/static/js/tiktok/constants/adgroup.js";
import { evaluateMetricWithMathJS } from "/static/js/tiktok/list-table-ads/config-table.js";

export const adgroupCustomFields = [
    {
        title: "Bid",
        key: "optimization_goal",
        unit: "CURRENCY",
        type: "string",
    },
    {
        title: "Ad group ID",
        key: "adgroup_id",
        unit: "",
        type: "string",
    },
    {
        title: "Ad group name",
        key: "adgroup_name",
        unit: "",
        type: "string",
    },
    {
        title: "Budget",
        key: "budget",
        unit: "CURRENCY",
        type: "string",
    },
    {
        title: "Cost",
        key: "spend",
        unit: "CURRENCY",
        type: "int32",
    },
    {
        key: "cpc_destination",
        title: "CPC (destination)",
        unit: "CURRENCY",
        metric: "{spend}/{clicks}",
        type: "int32",
    },
    {
        key: "cpm",
        unit: "CURRENCY",
        title: "CPM",
        metric: "({spend}/{impressions})*1000",
        type: "int32",
    },
    {
        key: "impressions",
        title: "Impressions",
        unit: "",
        type: "int32",
    },
    {
        key: "clicks",
        title: "Clicks (destination)",
        unit: "",
        type: "int32",
    },
    {
        key: "ctr_destination",
        title: "CTR (destination)",
        unit: "%",
        metric: "({impressions}/{spend})*100",
        type: "float64",
    },
    {
        key: "conversion",
        title: "Conversions",
        unit: "",
        type: "int32",
    },

    {
        key: "cost_per_conversion",
        title: "Cost per conversion",
        unit: "CURRENCY",
        metric: "({spend}/{conversion})",
        type: "int32",
    },
    {
        key: "conversion_rate",
        title: "Conversion rate (CVR)",
        unit: "%",
        metric: "({conversion}/{impressions})*100",
        type: "float64",
    },
];

const cellPercific = {
    budget: "bugetCellTable",
    bid: "bidCellTable",
    rawValue: "rawValueCellTable",
};

/**
 * Get ad group bid information based on ad properties
 */
export function getAdgroupBidInfo(ad) {
    const { optimization_goal: goal, bid_type, bid_price, conversion_bid_price, deep_bid_type, roas_bid } = ad;

    const goalLabel = {
        CONVERT: "Purchases",
        CLICK: "Clicks",
        VALUE: "Gross revenue",
        INITIATE_ORDER: "Initiate checkouts",
        PRODUCT_CLICK_IN_LIVE: "Product clicks in LIVE",
        MT_LIVE_ROOM: "Viewer retention",
        REACH: "Thousand Impressions",
    };

    // Case: VALUE + VO_MIN_ROAS khi có roas_bid > 0
    if (goal === "VALUE" && deep_bid_type === "VO_MIN_ROAS" && roas_bid > 0) {
        return {
            value: roas_bid,
            label: "Target ROAS (Shop Ads only)",
        };
    }

    // Case: VALUE + VO_HIGHEST_VALUE
    if (goal === "VALUE" && deep_bid_type === "VO_HIGHEST_VALUE") {
        return {
            value: 0,
            label: "Highest gross revenue",
        };
    }

    // Case: REACH + BID_TYPE_CUSTOM
    if (goal === "REACH" && bid_type === "BID_TYPE_CUSTOM") {
        return {
            value: bid_price || 0,
            label: goalLabel[goal],
        };
    }

    // Case: CUSTOM bids for conversion, product/live click, viewer retention
    if (bid_type === "BID_TYPE_CUSTOM") {
        if (["CONVERT", "PRODUCT_CLICK_IN_LIVE", "MT_LIVE_ROOM"].includes(goal)) {
            return {
                value: conversion_bid_price || 0,
                label: goalLabel[goal],
            };
        }
        if (goal === "CLICK") {
            return {
                value: bid_price || 0,
                label: goalLabel[goal],
            };
        }
    }

    // Default
    return {
        value: 0,
        label: goalLabel[goal] || goal || "-",
    };
}

/**
 * Get cost per result info adgroup
 */
export function getAdgroupResultsInfo(ad, type = "result") {
    const { optimization_goal: goal, bid_type, bid_price, conversion_bid_price, deep_bid_type, result, spend, clicks } = ad;
    // console.log(" 🚀 ~ getAdgroupResultsInfo ~ result:", result);
    let lastResult = 0;
    switch (type) {
        case "result_rate":
            lastResult = clicks ? (result / clicks) * 100 : 0;
            break;
        case "cost_per_result":
            lastResult = result ? spend / result : 0;
            break;
        default:
            lastResult = result || 0;
            break;
    }
    const goalLabel = {
        CONVERT: "Purchases",
        CLICK: "Click",
        VALUE: "Gross revenue",
        INITIATE_ORDER: "Initiate checkouts",
        PRODUCT_CLICK_IN_LIVE: "Product clicks",
        MT_LIVE_ROOM: "Effective LIVE Views",
        REACH: "Thousand Impressions",
    };

    // Case: VALUE + VO_MIN_ROAS khi có roas_bid > 0
    if (goal === "VALUE" && deep_bid_type === "VO_MIN_ROAS" && result > 0) {
        return {
            value: lastResult,
            label: "Target ROAS (Shop Ads only)",
        };
    }

    // Case: VALUE + VO_HIGHEST_VALUE
    if (goal === "VALUE" && deep_bid_type === "VO_HIGHEST_VALUE") {
        return {
            value: 0,
            label: "Highest gross revenue",
        };
    }

    // Case: REACH + BID_TYPE_CUSTOM
    if (goal === "REACH" && bid_type === "BID_TYPE_CUSTOM") {
        return {
            value: lastResult,
            label: goalLabel[goal],
        };
    }

    // Case: CUSTOM bids for conversion, product/live click, viewer retention
    if (bid_type === "BID_TYPE_CUSTOM") {
        if (["CONVERT", "PRODUCT_CLICK_IN_LIVE", "MT_LIVE_ROOM"].includes(goal)) {
            return {
                value: lastResult,
                label: goalLabel[goal],
            };
        }
        if (goal === "CLICK") {
            return {
                value: lastResult,
                label: goalLabel[goal],
            };
        }
    }

    // Default
    return {
        value: lastResult,
        label: goalLabel[goal] || goal || "-",
    };
}
