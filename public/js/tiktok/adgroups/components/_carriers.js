import { requestApi } from "/static/js/common/httpService.js";
import regionModule from "/static/js/tiktok/adgroups/components/_locations.js";

const API_END_POINT = {
    TARGETING_CARRIERS_LIST: `${window.location.origin}/dsp/tiktok/api/targeting/carriers/list`,
};
 
export default (() => {
    const carrierSelectSelector = '#carrierChoices';
    const geoLocationsSelector = '#selectedGeoLocations';
    let carriers = null;
    let carrierChoicesInstance;

    function mapCarriersToChoices(data) {
        const choices = [];

        data.forEach(country => {
            (country.carriers || []).forEach(carrier => {
                if (carrier.in_use) {
                    choices.push({
                        value: carrier.carrier_id,
                        label: carrier.name.trim(),
                    });
                }
            });
        });

        return choices;
    }

    async function InitCarrierSelect() {
        const element = document.querySelector(carrierSelectSelector);
        if (!element) return;

        function destroyAndInitChoices(choices = []) {
            if (carrierChoicesInstance && carrierChoicesInstance.destroy) {
                try {
                    carrierChoicesInstance.destroy();
                } catch (err) {
                    console.warn("Choices destroy failed:", err);
                }
                carrierChoicesInstance = null;
            }

            element.innerHTML = '';

            carrierChoicesInstance = new Choices(element, {
                shouldSort: false,
                allowHTML: true,
                removeItemButton: true,
                renderSelectedChoices: "auto",
                placeholder: true,
                placeholderValue: 'All',
                searchEnabled: true,
                classNames: {
                    containerOuter: "choices tiktok-custom",
                },
                choices
            });
        }

        window.addEventListener('regionSelectionUpdated', async function (event) {
            const { selectedCountryCodes = [] } = event.detail;

            if (!Array.isArray(selectedCountryCodes) || selectedCountryCodes.length === 0) {
                destroyAndInitChoices([]);
                return;
            }

            try {
                const res = await requestApi("POST", API_END_POINT.TARGETING_CARRIERS_LIST, {
                    search: "",
                    country_codes: selectedCountryCodes,
                });

                const carriers = Array.isArray(res.data) ? res.data : [];
                const choices = mapCarriersToChoices(carriers);

                destroyAndInitChoices(choices);
            } catch (error) {
                console.error("Failed to load carriers:", error);
                destroyAndInitChoices([]);
            }
        });

        const selectedGeoEl = document.querySelector("#selectedGeoLocations");
        if (selectedGeoEl) {
            try {
                const selected = JSON.parse(selectedGeoEl.getAttribute("data-value") || "{}");
                const selectedCountryCodes = selected?.COUNTRY || [];
                window.dispatchEvent(new CustomEvent("regionSelectionUpdated", {
                    detail: {
                        selectedCountryCodes,
                        selectedRegionItems: selected
                    }
                }));
            } catch (err) {
                console.warn("Failed to parse initial selectedGeoLocations:", err);
            }
        }
    }

    function init() {
        InitCarrierSelect();
    }

    return {
        init,
        reload: InitCarrierSelect,
    };
})();