// import budgetSchedulingUI from "/static/js/pages/camp-adset-ad/off-canvas/adset/_budget-scheduling.js";
import { DATETIME_FORMAT_TYPES } from "/static/js/constants/adset-constants.js";

export default (() => {
    const ID_ADGOUP_FORM = "#adgroupForm";
    const ID_BUDGET_TYPE_CONTAINER = "#adgroupBaseBudgetTypeContainer";
    const formAdset = $(ID_ADGOUP_FORM);

    const inputStartTimeEl = formAdset.find("#start-time-field");

    const adsetBaseBudgetTypeEl = formAdset.find("#adsetBaseBudgetType");
    const adsetBaseBudgetAmountEl = formAdset.find("#adsetBaseBudgetAmount");

    const adsetBudgetToggleEndDateEl = formAdset.find("#set-an-end-date");
    const adsetBudgetStartDateEl = formAdset.find("#start-time-field");
    const adsetBudgetEndDateEl = formAdset.find("#end-time-field");

    let adsetData;
    let isInit = true;

    /**
     * Handle Update Ui By Adset
     */
    function handleUpdateBudgetByAdset(adset) {
        if (!adset) {
            adsetBaseBudgetTypeEl.val("daily");
            adsetBaseBudgetTypeEl.prop("disabled", false);
            adsetBaseBudgetAmountEl.val("50.000");
            return;
        }

        const budgetType = !isNaN(adset?.daily_budget) && parseInt(adset.daily_budget) > 0 ? "daily" : parseInt(adset.lifetime_budget) == 0 ? "daily" : "life";
        adsetBaseBudgetTypeEl.val(budgetType);
        adsetBaseBudgetTypeEl.prop("disabled", true);
        adsetBaseBudgetAmountEl.val(formatNumberWithSeparator(parseInt(adset?.daily_budget > 0 ? adset.daily_budget : adset?.lifetime_budget)) || 0);
    }

    /**
     * Mapping Budget Schedule
     */
    const mappingBudgetAndSchedule = (adsetData) => {
        // Start time
        const startTime = adsetData?.start_time ?? "";
        if (startTime !== "") {
            const formattedStartTime = moment(startTime);
            if (formattedStartTime.isBefore(moment())) {
                adsetBudgetStartDateEl.prop("disabled", true);
            }
            adsetBudgetStartDateEl.data("daterangepicker").setStartDate(formattedStartTime.format(DATETIME_FORMAT_TYPES.DISPLAY));
            adsetBudgetStartDateEl.data("daterangepicker").setEndDate(null);
            adsetBudgetStartDateEl.val(getFormatDatetime(formattedStartTime.format(DATETIME_FORMAT_TYPES.DISPLAY)));
        } else {
            adsetBudgetStartDateEl.prop("disabled", false);
            adsetBudgetStartDateEl.data("daterangepicker").setStartDate(moment().startOf("hour").add(1, "h").format(DATETIME_FORMAT_TYPES.DISPLAY));
        }
        // End time
        const endTime = adsetData?.end_time ?? "";
        if (endTime !== "" && endTime !== "0001-01-01T00:00:00Z") {
            const formattedEndTime = moment(endTime);
            $('input[name="end_time"]').attr("before-value", formattedEndTime.format(DATETIME_FORMAT_TYPES.DISPLAY));
            adsetBudgetEndDateEl.data("daterangepicker").setStartDate(formattedEndTime.format(DATETIME_FORMAT_TYPES.DISPLAY));
            adsetBudgetEndDateEl.data("daterangepicker").setEndDate(null);
            adsetBudgetEndDateEl.val(getFormatDatetime(formattedEndTime.format(DATETIME_FORMAT_TYPES.DISPLAY)));
            adsetBudgetToggleEndDateEl.prop("checked", true);
            adsetBudgetEndDateEl.prop("disabled", false);
        } else {
            adsetBudgetToggleEndDateEl.prop("checked", false);
            adsetBudgetEndDateEl.prop("disabled", true);
            adsetBudgetEndDateEl.val("");
        }
    };

    /**
     * On Change Budget Type
     */
    function onChangeBudgetType() {
        adsetBaseBudgetTypeEl.on("change", function () {
            if (this.value === "life") {
                adsetBudgetToggleEndDateEl.prop("checked", false).click();
            } else {
                adsetBudgetToggleEndDateEl.prop("checked", true).click();
            }
        });
    }

    function disableFieldByEditAdset() {
        if (adsetData.adset_id) {
            inputStartTimeEl.prop("disabled", true);
        }
    }

    function getFormatDatetime(datetime) {
        const regexExpress = /(\+\d{2}):(\d{2})/;
        return datetime.replace(regexExpress, "$1$2");
    }

    function initSetDatetime() {
        const startField = $("#start-time-field");
        const endField = $("#end-time-field");
        const toggleEnd = $("#set-an-end-date");

        [
            { field: startField, days: 0 },
            { field: endField, days: 3 },
        ].forEach(function (item) {
            const $f = item.field;
            const hidden = $f.next("input"); // giả sử input[type=hidden] ngay sau
            const addDays = item.days;

            // set giá trị mặc định cho hidden
            hidden.val(getFormatDatetime(moment().add(addDays, "d").format(DATETIME_FORMAT_TYPES.ISO)));

            // init daterangepicker trên chính field
            $f.daterangepicker(
                {
                    startDate: moment().startOf("hour").add(1, "h").add(addDays, "d"),
                    timePicker: true,
                    singleDatePicker: true,
                    timePicker24Hour: true,
                    locale: { format: "lll" },
                    minDate: item.field.is(startField) ? moment().startOf("hour").add(15, "m") : null,
                },
                function (start) {
                    hidden.val(getFormatDatetime(start.format(DATETIME_FORMAT_TYPES.ISO)));
                }
            );

            // khi apply datetimerange chọn xong
            $f.on("apply.daterangepicker", function (e, picker) {
                hidden.val(getFormatDatetime(picker.startDate.format(DATETIME_FORMAT_TYPES.ISO)));
            });

            // disable endField ban đầu
            if (item.field.is(endField)) {
                endField.prop("disabled", true).val("");
                endField.next("input").val("");
            }
        });

        // giống budgetToggle, bật/tắt end-date
        toggleEnd.off("click").on("click", function () {
            if (this.checked) {
                endField.prop("disabled", false);

                // set picker + hidden = now + 3 days
                const d = moment().startOf("hour").add(1, "h").add(3, "d");
                endField.data("daterangepicker").setStartDate(d.format("lll"));
                endField.next("input").val(getFormatDatetime(d.format(DATETIME_FORMAT_TYPES.ISO)));

                // minDate bằng startField hiện tại
                const min = startField.data("daterangepicker").startDate;
                endField.data("daterangepicker").minDate = min.isAfter(moment()) ? min : moment();
            } else {
                endField.prop("disabled", true).val("");
                endField.next("input").val("");
            }
        });
    }

    /**
     * handle Update Layout Budget Schedule Ui
     */
    function updateLayoutBudgetSchedule(detailAdset) {
        resetHTML();
        const camp = window.campaignEdit;

        if (parseInt(camp.daily_budget) || parseInt(camp.lifetime_budget)) {
            const textHint = parseInt(camp.daily_budget)
                ? `You set a daily Advantage campaign budget of  ₫${formatNumberWithSeparator(parseInt(camp.daily_budget))}`
                : `You set a lifetime Advantage campaign budget of ₫${formatNumberWithSeparator(camp.lifetime_budget)}`;
            adsetBaseBudgetTypeEl.closest(ID_BUDGET_TYPE_CONTAINER).find(".control-budget-campaign .text-hint").text(textHint);

            adsetBaseBudgetTypeEl.closest(ID_BUDGET_TYPE_CONTAINER).find(".control-budget-campaign").show();
            adsetBaseBudgetTypeEl.closest(ID_BUDGET_TYPE_CONTAINER).find(".control-budget-adset").hide();
        } else {
            adsetBaseBudgetTypeEl.closest(ID_BUDGET_TYPE_CONTAINER).find(".control-budget-campaign").hide();
            adsetBaseBudgetTypeEl.closest(ID_BUDGET_TYPE_CONTAINER).find(".control-budget-adset").show();
        }

        adsetData = detailAdset;
        disableFieldByEditAdset();
        mappingBudgetAndSchedule(detailAdset);

        if (detailAdset?.adset_id) {
            handleUpdateBudgetByAdset(detailAdset);
        } else {
            handleUpdateBudgetByAdset();
        }
    }

    function resetHTML() {
        adsetBaseBudgetAmountEl.val("500.000");
    }

    /**
     * Handle Mapping schedule budget to request
     */
    function handleExcecutiveScheduleBudgetToRequest(requestData) {
        // Executive Scheduling

        if (adsetBaseBudgetTypeEl.val() === "life") {
            delete requestData["daily_budget"];
            requestData["lifetime_budget"] = parseInt(adsetBaseBudgetAmountEl.val().replace(/[\.,]/g, ""), 10);
        } else {
            delete requestData["lifetime_budget"];
            requestData["daily_budget"] = parseInt(adsetBaseBudgetAmountEl.val().replace(/[\.,]/g, ""), 10);
        }
        delete requestData["amount_budget"];

        requestData["start_time"] = moment(requestData["start_time"]).format(DATETIME_FORMAT_TYPES.ISO);

        if (adsetBudgetToggleEndDateEl.prop("checked") && requestData["end_time"]) {
            requestData["end_time"] = moment(requestData["end_time"]).format(DATETIME_FORMAT_TYPES.ISO);
        }

        const camp = window.campaignEdit;

        if (parseInt(camp.daily_budget) || parseInt(camp.lifetime_budget)) {
            delete requestData["lifetime_budget"];
            delete requestData["daily_budget"];
            if (parseInt(camp.lifetime_budget)) {
                delete requestData["daily_spend_cap"];
                delete requestData["daily_min_spend_target"];
            }
        } else {
            ["daily_spend_cap", "daily_min_spend_target"].forEach((key) => delete requestData[key]);
        }

        return requestData;
    }

    function init() {
        initSetDatetime();
        onChangeBudgetType();
    }

    return {
        handleExcecutiveScheduleBudgetToRequest,
        updateLayoutBudgetSchedule,
        resetHTML,
        init,
    };
})();
