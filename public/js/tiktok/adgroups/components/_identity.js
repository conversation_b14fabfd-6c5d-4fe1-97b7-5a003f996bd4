import { requestApi } from "/static/js/common/httpService.js";

export default (() => {
    const ID_IDENTITY_CHOICES = "identity_select";
    const API_END_POINT = {
        LIST_IDENTITY_TIKTOK_ACCOUNT: `${window.location.origin}/dsp/tiktok/api/identity/list-tt-account`,
    }

    async function renderListTikTokAccount() {
        const response = await axios.post(API_END_POINT.LIST_IDENTITY_TIKTOK_ACCOUNT, {});
        const data = response.data.data;

        const $select = $(`#${ID_IDENTITY_CHOICES}`);
        $select.empty();

        $select.append(new Option("Select an identity", "", true, true)).prop("disabled", false);


        data.forEach((item) => {
            const option = new Option(item.display_name, item.identity_id, false, false);
            $(option).attr("data-avatar", item.profile_image);
            $(option).attr("data-description", `Authorized through ${item.identity_authorized_bc_id}`);
            $select.append(option);
        });

        $select.select2({
            placeholder: "Select an identity",
            templateResult: formatOption,
            templateSelection: formatSelected,
            width: "100%",
            language: {
                searching: function () {
                    return "Search by name";
                }
            }
        });

        function formatOption(option) {
            if (!option.id) return option.text;

            const $option = $(option.element);
            const avatar = $option.data("avatar") || "https://via.placeholder.com/40";
            const description = $option.data("description") || "";

            return $(`
                <div class="d-flex align-items-center gap-2">
                    <img src="${avatar}" class="rounded-circle" width="40" height="40" />
                    <div>
                        <div>${option.text}</div>
                        <small class="text-muted">${description}</small>
                    </div>
                </div>
            `);
        }

        function formatSelected(option) {
            if (!option.id) return option.text;

            const $option = $(option.element);
            const avatar = $option.data("avatar") || "https://via.placeholder.com/20";

            return $(`
                <div class="d-flex align-items-center gap-2">
                    <img src="${avatar}" class="rounded-circle" width="20" height="20" />
                    <span>${option.text}</span>
                </div>
            `);
        }
    }

    function init() {
        if ($(`#${ID_IDENTITY_CHOICES}`).length) {
            renderListTikTokAccount();
        }
    }

    return {
        init,
    }
})()