import { requestApi } from "/static/js/common/httpService.js";

const API_END_POINT = {
    TARGETING_INTERESTS_AND_BEHAVIORS_LIST: `${window.location.origin}/dsp/tiktok/api/targeting/interests-behaviors/list`,
};
const noResultHTML = `<div class="no-results">
            <img src="/static/images/no-data.png" alt="No Results" style="max-width: 250px; margin-bottom: 10px;">
            <p>No Data</p>
         </div>`;

/**
 * Detail Targeting UI
 */
export default (() => {
    let isInit = false;
    const inputDetailTargetingEl = 'input[name="targeting[interestsAndBehaviors][]"]';
    const searchDetailTargetingPopoverID = "#searchDetailTargetingPopover";
    const browseDetailTargetingPopoverID = "#browseDetailTargetingPopover";
    const btnBrowseInputDetailTargetingID = "#btnDetailTargetingBrowse";
    const elListSearchDetailTargetingPopoverID = "#searchDetailTargetingPopover #content_tab_detail_targeting ul"

    const inputDetailTargeting = $('input[name="targeting[flexible_spec][]"]');
    const controlDetailTargetingSearch = $("#inputControlSearchDetatlTargeting");
    const resultsSelectedDetailTargeting = $("#detailTargetingResultsSelected");
    const searchDetailTargetingPopover = $("#searchDetailTargetingPopover");
    const elListSearchDetailTargetingPopover = $("#searchDetailTargetingPopover #content_tab_detail_targeting ul");
    const browseDetailTargetingPopover = $("#browseDetailTargetingPopover");
    const elListDemographicsSuggestions = $("#accordionDemographics .accordion-body ul");
    const elListInterestsSuggestions = $("#accordionInterests .accordion-body ul");
    const elListBehavioursSuggestions = $("#accordionBehaviours .accordion-body ul");
    const btnBrowseInputDetailTargeting = $("#btnDetailTargetingBrowse");
    const inputSearchInterestsSuggestions = $("#searchInterestsSuggestions");

    let listDetailTargetingSearch = null;
    // let selectedItemsDefault = []; // From Adset
    let selectedItems = [];
    let interestsAndBehaviors = null;

    const htmlSpinner = `<div class="spinner-border text-secondary" role="status" style="margin: 20px auto;">
                           <span class="sr-only">Loading...</span>
                       </div>`;

    const actionFilter = {
        elevance: $("#btn_detail_targeting_elevance"),
        size: $("#btn_detail_targeting_elevance"),
    };

    function resetDetailsTargeting() {
        selectedItems = [];
        inputDetailTargeting.val("");
        resultsSelectedDetailTargeting.html("");
        resultsSelectedDetailTargeting.css("display", "none");
        elListSearchDetailTargetingPopover.html("");
        elListDemographicsSuggestions.html("");
        elListInterestsSuggestions.html("");
        elListBehavioursSuggestions.html("");
        renderSuggestionBrowse();
        // renderUiListTargetingSearch();
    }

    function onInputSearchForcus() {
        $(inputDetailTargetingEl).on("focus", function () {
            $(searchDetailTargetingPopoverID).show();
            $(browseDetailTargetingPopoverID).hide();
        });
    }

    function onBrowse() {
        $(btnBrowseInputDetailTargetingID).on("click", function (e) {
            $(searchDetailTargetingPopoverID).hide();
            $(browseDetailTargetingPopoverID).show();
            // getDetailTargetingBrowse();
        });
    }

    function onClickOutsideToHidePopover() {
        $(document).on("click", function (e) {
            const $target = $(e.target);

            if (
                !$target.closest(inputDetailTargetingEl).length &&
                !$target.closest(btnBrowseInputDetailTargetingID).length &&
                !$target.closest(searchDetailTargetingPopoverID).length &&
                !$target.closest(browseDetailTargetingPopoverID).length
            ) {
                $(searchDetailTargetingPopoverID).hide();
                $(browseDetailTargetingPopoverID).hide();
            }
        });
    }

    // function onSearchInterestSuggestion() {
    //     inputSearchInterestsSuggestions.on(
    //         "input",
    //         debounce(function () {
    //             const searchValue = this.value.toLowerCase();

    //             [elListDemographicsSuggestions, elListInterestsSuggestions, elListBehavioursSuggestions].forEach((list) => {
    //                 list.find("li").each(function () {
    //                     this.style.display = this.dataset.name.toLowerCase().includes(searchValue) || !searchValue ? "flex" : "none";
    //                 });
    //             });
    //         }, 300)
    //     );
    // }

    // function handleSearchDetailTargeting(value) {
    //     controlDetailTargetingSearch.on("click", "#btnDetailTargetingSearch", function () {
    //         const searchValue = inputDetailTargeting.val().trim();
    //         elListSearchDetailTargetingPopover.html("");
    //         elListSearchDetailTargetingPopover.append(htmlSpinner);
    //         if (searchValue) {
    //             requestApi("GET", `${API_END_POINT.TARGETING_SEARCH}?q=${searchValue}`).then(({ data: { data } }) => {
    //                 if (!data || data?.length === 0) {
    //                     listDetailTargetingSearch = [];
    //                 } else {
    //                     listDetailTargetingSearch = data;
    //                 }
    //                 renderUiListTargetingSearch();
    //             });
    //         } else {
    //             listDetailTargetingSearch = window?.adset?.targetingBrowse || [];
    //             renderUiListTargetingSearch();
    //         }
    //     });
    // }

    // function onRemoveSelectedItem() {
    //     resultsSelectedDetailTargeting.on("click", "i.btn-remove-item", function (e) {
    //         e.stopPropagation();

    //         const idRemove = this.dataset.id;
    //         const itemRemove = selectedItems.find((i) => i.id == idRemove);
    //         if (!itemRemove) return;

    //         selectedItems = selectedItems.filter((i) => i.id != idRemove);

    //         const groupItem = resultsSelectedDetailTargeting.find(`.group-item[data-group="${itemRemove.path.join(" > ")}"]`);
    //         if (groupItem.length) {
    //             groupItem.children(`.selected-item[data-id="${idRemove}"]`).remove();
    //             if (!groupItem.children(".selected-item").length) {
    //                 groupItem.remove();
    //             }
    //         }

    //         browseDetailTargetingPopover.find(`input[id="${idRemove}"]`).prop("checked", false);
    //     });
    // }

    // function onCheckedInterestItem() {
    //     function handleAddItem(e) {
    //         console.log("handleAddItem", this);
    //         e.preventDefault();
    //         const { value: id, checked } = this;
    //         let item = targetingBrowse.findLast((i) => i.id === id);
    //         if (!item) {
    //             item = listDetailTargetingSearch.findLast((i) => i.id === id);
    //             if (!item) return;
    //         }

    //         selectedItems = checked ? [...selectedItems, item].filter((v, i, a) => a.findIndex((t) => t.id === v.id) === i) : selectedItems.filter((i) => i.id !== id);
    //         selectedItems = selectedItems.sort((a, b) => {
    //             const pathComparison = a.path[0].localeCompare(b.path[0]);
    //             if (pathComparison !== 0) return pathComparison;
    //             return b.path.length - a.path.length;
    //         });

    //         renderUiListTargetingSelected();
    //     }
    //     browseDetailTargetingPopover.on("change", "input", handleAddItem);
    //     searchDetailTargetingPopover.on("change", "input", handleAddItem);
    // }

    // function groupByType(data) {
    //     return data.reduce((acc, item) => {
    //         const cate = item.path[0]?.toLowerCase();
    //         if (!acc[cate]) {
    //             acc[cate] = [];
    //         }
    //         acc[cate].push(item);
    //         return acc;
    //     }, {});
    // }

    /**
     * Fetch data and initialize rendering
     */
    function getDetailTargetingBrowse() {
        requestApi("POST", API_END_POINT.TARGETING_INTERESTS_AND_BEHAVIORS_LIST, { search: "" }).then(res => {

            interestsAndBehaviors = res.data[0];
            listDetailTargetingSearch = res.data;

            renderSuggestionBrowse();
            renderUiListTargetingSearch();
        });
    }

    function renderSelectItemPopover(data) {
        function generatePopoverContent(size, name, type, description) {
            return `
               <div class="d-flex flex-column gap-2">
                   ${generatePopoverItem("Size", size)}
                   ${generatePopoverItem("Name", name)}
                   ${generatePopoverItem("Type", type)}
                   ${generatePopoverItem("Description", description)}
               </div>
           `;
        }

        function generatePopoverItem(label, value) {
            return `
               <div class="d-flex flex-column">
                   <b>${label}: </b>
                   <p>${value}</p>
               </div>
           `;
        }

        const { id, name, description, type, path, audience_size_lower_bound, audience_size_upper_bound } = data;
        const formattedType = type.replaceAll("_", "");
        const size =
            audience_size_lower_bound < 0
                ? "Below 1000"
                : `${Intl.NumberFormat("en-US", { maximumFractionDigits: 0 }).format(audience_size_lower_bound)} - 
              ${Intl.NumberFormat("en-US", { maximumFractionDigits: 0 }).format(audience_size_upper_bound)}`;

        return `
           <li class="list-group-item pt-1 pb-1" data-id="${id}" data-name="${name}" data-bs-toggle="popover" 
               data-bs-html="true" data-bs-trigger="hover" data-bs-content='${generatePopoverContent(size, name, formattedType, description)}'>
               <label class="d-flex flex-1 gap-2 align-items-center" for="${id}">
                   <div class="d-flex flex-column align-content-center item-left">
                       <div class="item-name">
                           <span class="text-success fs-1">•</span>
                           <span class="ml-2 name">${name}</span>
                       </div>
                       <div class="fs-6 text-muted">Size: ${size}</div>
                   </div>
                   <div class="text-muted item-right">${formattedType}</div>
                   <div>
                       <input class="form-check-input" type="checkbox" value="${id}" id="${id}" 
                           data-type="${type}">     
                   </div>
               </label>
           </li>
       `;
    }

    function renderUiListTargetingSearch() {
        $(elListSearchDetailTargetingPopoverID).html("");
        $(elListSearchDetailTargetingPopoverID).append(noResultHTML);
        // if (listDetailTargetingSearch.length === 0) {

        // } else {
        //     console.log("listDetailTargetingSearch: ", listDetailTargetingSearch);
        //     listDetailTargetingSearch.forEach((item) => {
        //         $(elListSearchDetailTargetingPopoverID).append(renderSelectItemPopover(item));
        //     });

        //     if (selectedItems.length > 0) {
        //         const listId = selectedItems.map((i) => i.id);
        //         $(elListSearchDetailTargetingPopoverID).find(`input[type="checkbox"]`).each(function () {
        //             if (listId.includes(this.value)) this.click();
        //         });
        //     }
        // }
        initPopoverDetailTargetingSearch();
    }

    /**
     * Render suggestion list by tab
     */
    function renderSuggestionBrowse() {

        const behaviorsData = interestsAndBehaviors;

        const tabMapping = {
            general_interest: "#interest-and-behaviors-interests",
            video_interaction: "#interest-and-behaviors-video-interactions",
            creator_interaction: "#interest-and-behaviors-creator-interactions",
            hashtag_interaction: "#interest-and-behaviors-hashtag-interactions",
        };

        Object.entries(tabMapping).forEach(([key, selector]) => {
            const container = $(selector).empty();
            const list = behaviorsData[key]?.list_result || [];
            if (!list.length) return;

            const idMap = buildIdMap(list);
            const roots = findRootItems(list);
            const html = renderItemTree(roots, idMap, null, key);
            container.html(html);
        });

        initToggleBehavior();
        initCheckboxCascadeBehavior();
        onItemsSelect();

    }

    /**
     * Build Map list interests & behaviors
     */
    function buildIdMap(list) {
        return new Map(list.map(item => [item.id, item]));
    }

    /**
     * Find root items
     */
    function findRootItems(list) {
        return list.filter(item => !list.some(parent => parent.children_ids.includes(item.id)));
    }

    /**
     * Render item checkbox
     */
    function renderItemTree(items, idMap, parentId = null, type) {
        return items.map(item => {
            const children = (item.children_ids || []).map(id => idMap.get(id)).filter(Boolean);
            const hasChildren = children.length > 0;

            return `
           <div class="targeting-item" data-id="${item.id}">
            <div class="d-flex gap-3 align-items-start">
                <!-- Chevron -->
                ${hasChildren
                    ? `<i class="bx bx-chevron-right toggle-icon fs-20 mt-1" style="cursor:pointer; flex-shrink:0;"></i>`
                    : `<div style="width: 1.25rem;"></div>`}

                <!-- Checkbox + label -->
                <div class="form-check d-flex gap-2 flex-grow-1">
                <input class="form-check-input mt-1 targeting-checkbox"
                        type="checkbox"
                        data-id="${item.id}" data-type="${type}"
                        ${parentId ? `data-parent-id="${parentId}"` : ''}>
                <label class="form-check-label text-wrap flex-grow-1" style="max-width: 100%;">
                    ${item.name}
                </label>
                </div>
            </div>

            ${hasChildren
                    ? `<div class="targeting-children ms-4 my-1" style="display:none;">
                    ${renderItemTree(children, idMap, item.id, type)}
                </div>`
                    : ''}
            </div>
        `;
        }).join('');
    }


    /**
     * Init toggle behavior
     */
    function initToggleBehavior() {
        $(document).off("click", ".toggle-icon, .toggle-icon *").on("click", ".toggle-icon, .toggle-icon *", function (e) {
            const $icon = $(this).closest(".toggle-icon");
            const $parent = $icon.closest(".targeting-item");
            const $children = $parent.children(".targeting-children");

            $children.slideToggle();
            $icon.toggleClass("bx-chevron-right bx-chevron-down");

        });
    }

    /**
     * Init checkbox behavior
     */
    function initCheckboxCascadeBehavior() {
        $(document).off('change', '.targeting-checkbox').on('change', '.targeting-checkbox', function () {
            const $checkbox = $(this);
            const $item = $checkbox.closest('.targeting-item');
            const isChecked = $checkbox.is(':checked');

            resetCheckboxState($checkbox);
            $item.find('.targeting-children .targeting-checkbox').each(function () {
                this.checked = isChecked;
                this.indeterminate = false;
                resetCheckboxState($(this));
            });

            updateParentCheckboxState($checkbox);
        });
    }

    /**
     * Reset checkbox state
     */
    function resetCheckboxState($checkbox) {
        $checkbox.prop('indeterminate', false);
        $checkbox.closest('.form-check').removeClass('partial-state');
    }

    /**
     * Update parent checkbox state
     */
    function updateParentCheckboxState($checkbox) {
        const $parentItem = $checkbox.closest('.targeting-children').closest('.targeting-item');
        if (!$parentItem.length) return;

        const $parentCheckbox = $parentItem.find('> .d-flex .targeting-checkbox');
        const $formCheck = $parentCheckbox.closest('.form-check');
        const $childCheckboxes = $parentItem.find('> .targeting-children > .targeting-item > .d-flex .targeting-checkbox');

        const total = $childCheckboxes.length;
        const checked = $childCheckboxes.filter(':checked').length;

        $parentCheckbox.prop('indeterminate', false);
        $formCheck.removeClass('partial-state');

        if (checked === 0) {
            $parentCheckbox.prop('checked', false);
        } else if (checked === total) {
            $parentCheckbox.prop('checked', true);
        } else {
            $parentCheckbox.prop('checked', false).prop('indeterminate', true);
            $formCheck.addClass('partial-state');
        }

        updateParentCheckboxState($parentCheckbox);
    }

    /**
     * Get Selected Data
     */
    function getSelectedData() {
        try {
            return JSON.parse($('#selectedDetailTargeting').attr('data-value') || '{}');
        } catch {
            return {};
        }
    }

    /**
     *  Set Selected Data 
     */
    function setSelectedData(data) {
        $('#selectedDetailTargeting').attr('data-value', JSON.stringify(data));
    }

    /**
     * On change checkbox 
     */
    function onItemsSelect() {
        $(document).on('change', '.targeting-checkbox', function () {
            const $checkbox = $(this);
            const id = $checkbox.data('id');
            const type = $checkbox.data('type');
            const parentId = $checkbox.data('parent-id');
            const isChecked = $checkbox.is(':checked');
            const selected = getSelectedData();

            selected[type] ??= [];

            const add = id => !selected[type].includes(id) && selected[type].push(id);
            const remove = id => selected[type] = selected[type].filter(i => i !== id);

            if (isChecked) {
                if (!parentId) {
                    $(`.targeting-checkbox[data-type='${type}'][data-parent-id='${id}']`).each(function () {
                        remove($(this).data('id'));
                    });
                    add(id);
                } else {
                    remove(parentId);
                    add(id);

                    const $siblings = $(`.targeting-checkbox[data-type='${type}'][data-parent-id='${parentId}']`);
                    const allChecked = $siblings.length && $siblings.filter(':checked').length === $siblings.length;

                    if (allChecked) {
                        $siblings.each(function () { remove($(this).data('id')); });
                        const $parent = $(`.targeting-checkbox[data-type='${type}'][data-id='${parentId}']`);
                        $parent.prop('checked', true);
                        resetCheckboxState($parent);
                        add(parentId);
                    }
                }

            } else {
                remove(id);

                if (parentId && selected[type].includes(parentId)) {
                    const $parent = $(`.targeting-checkbox[data-type='${type}'][data-id='${parentId}']`);
                    $parent.prop('checked', false);
                    resetCheckboxState($parent);
                    remove(parentId);

                    const childrenChecked = $(`.targeting-checkbox[data-type='${type}'][data-parent-id='${parentId}']`)
                        .filter(function () {
                            return $(this).data('id') !== id && $(this).is(':checked');
                        }).map(function () {
                            return $(this).data('id');
                        }).get();

                    childrenChecked.forEach(add);
                }
            }

            setSelectedData(selected);
            renderSelectedItems();
        });
    }



    /**
     * Render selected interests and behaviors
     */
    function renderSelectedItems() {
        const selected = getSelectedData();
        const $container = $('#detailTargetingResultsSelected');
        $container.empty();

        const typeLabelMap = {
            general_interest: 'Interests',
            video_interaction: 'Video interactions',
            creator_interaction: 'Creator interactions',
            hashtag_interaction: 'Hashtag interactions',
        };

        Object.entries(selected).forEach(([type, ids]) => {
            if (!Array.isArray(ids) || ids.length === 0) return;

            const groupLabel = `${ids.length} ${typeLabelMap[type] || ''}`;

            const $group = $('<div>', { class: 'group-item', 'data-value': type });
            $group.append($('<div>', { class: 'header text-secondary', text: groupLabel }));

            ids.forEach(id => {
                const $label = $(`.targeting-checkbox[data-id='${id}']`).next('label');
                const name = $label.length ? $label.text().trim() : id;

                const $item = $('<div>', {
                    class: 'selected-item',
                    'data-id': id,
                    'data-group': type,
                    'data-name': name
                });

                $item.append($('<div>').text(name));
                $item.append($('<i>', { class: 'ri-close-circle-fill btn-remove-item', style: 'cursor: pointer;' }));

                $group.append($item);
            });

            $container.append($group);
        });

        $container.toggle($container.children().length > 0);
    }


    /**
     *  Remove selected item
     */
    function onRemoveSelectedItem() {
        $(document).off('click', '.btn-remove-item').on('click', '.btn-remove-item', function () {
            const $item = $(this).closest('.selected-item');
            const id = $item.data('id');
            const type = $item.data('group');

            const $checkbox = $(`.targeting-checkbox[data-id='${id}']`);
            $checkbox.prop('checked', false).trigger('change');
        });
    }




    function renderItemRecursive(item, container, idMap, level) {
        const marginLeft = Math.min(level * 3, 5); // Max ms-5
        const html = `
            <div class="form-check ms-${marginLeft} mb-1">
                <input class="form-check-input" type="checkbox" id="targeting-${item.id}">
                <label class="form-check-label" for="targeting-${item.id}">
                    ${item.name}
                </label>
            </div>
        `;
        container.append(html);

        if (item.children_ids && item.children_ids.length > 0) {
            item.children_ids.forEach(childId => {
                const child = idMap.get(childId);
                if (child) {
                    renderItemRecursive(child, container, idMap, level + 1);
                }
            });
        }
    }

    function rendUIListTargetingSearchEnoughtPath(data) {
        let result = [];

        for (const key in data) {
            if (Array.isArray(data[key])) {
                data[key].forEach((item) => {
                    if (item.id) {
                        result.push({
                            id: item.id,
                            name: item.name,
                            type: key,
                            path: [key.charAt(0).toUpperCase() + key.slice(1)],
                        });
                    }
                });
            }
        }

        let interestNotRender = [];

        result.forEach((interest) => {
            let isPush = true;
            resultsSelectedDetailTargeting.find(".selected-item").each(function () {
                if (interest.id === this.dataset.id) {
                    isPush = false;
                }
            });

            if (isPush) {
                interestNotRender.push(interest);
            }
        });

        interestNotRender.forEach((interest) => {
            selectedItems.push(interest);
            renderItemSelectedUi(interest);
        });
    }

    function updateInterestsFromAdset(flexbleSpec) {
        resetDetailsTargeting();
        if (flexbleSpec.length == 0) return;

        if (!Array.isArray(flexbleSpec) || !flexbleSpec.length) return;

        selectedItems = flexbleSpec[0];
        renderUiListTargetingSearch();
        renderSuggestionBrowse();
        rendUIListTargetingSearchEnoughtPath(flexbleSpec[0]);
    }

    function mappingSelectedItemsToReq(data) {
        const transformedData = {};

        // Lặp qua từng phần tử trong data
        data.forEach((item) => {
            const category = item.type;

            if (!transformedData[category]) {
                transformedData[category] = [];
            }

            if (category === "education_statuses" || category === "relationship_statuses") {
                transformedData[category].push(parseInt(item.id));
            } else {
                transformedData[category].push({
                    id: item.id,
                    name: item.name,
                });
            }
        });

        return transformedData;
    }

    function renderUiListTargetingSelected() {
        if (selectedItems.length > 0) {
            resultsSelectedDetailTargeting.css("display", "flex");
            resultsSelectedDetailTargeting.html("");
            selectedItems.forEach((interest) => {
                renderItemSelectedUi(interest);
            });
            // let selectedItemsValue = selectedItems.map((i) => ({ id: i.id, name: i.name, path: i.path }));
            let selectedItemsValue = mappingSelectedItemsToReq(selectedItems);
            inputDetailTargeting.attr("data-value", JSON.stringify(selectedItemsValue));
        } else {
            resultsSelectedDetailTargeting.css("display", "none");
        }
    }

    function renderItemSelectedUi(interest) {
        let arrayPath = interest.path;
        if (arrayPath[arrayPath.length - 1] === interest.name) {
            arrayPath.pop();
        }
        const path = arrayPath.join(" > ");
        const groupSelector = `.group-item[data-group="${path}"]`;
        let group = resultsSelectedDetailTargeting.find(groupSelector);

        const itemSelected = `
           <div class="selected-item" data-id="${interest.id}">
               <div>${interest.name}</div>
               <i class="ri-close-circle-fill btn-remove-item" data-id="${interest.id}"></i>
           </div>
       `;

        if (group.length) {
            group.append(itemSelected);
        } else {
            const path = interest.path.join(" > ");
            resultsSelectedDetailTargeting.append(`
               <div class="group-item" data-group="${path}" data-group-type="${interest.type}">
                   <div class="header text-secondary">
                       ${path}
                   </div>
                   ${itemSelected}
               </div>
           `);
        }
    }

    const initPopover = () => {
        const $popoverTrigger = browseDetailTargetingPopover.find('[data-bs-toggle="popover"]');
        $popoverTrigger.popover({
            html: true,
            trigger: "hover", // Hoặc 'click' nếu bạn cần
        });
    };

    const initPopoverDetailTargetingSearch = () => {
        const $popoverTrigger = $(searchDetailTargetingPopoverID).find('[data-bs-toggle="popover"]');
        $popoverTrigger.popover({
            html: true,
            trigger: "hover", // Hoặc 'click' nếu bạn cần
        });
    };

    /**
     * Init Interest & Behavior Targeting UI
     */
    function init() {

        // Show popover when input is focused
        onInputSearchForcus();

        // Click btn Browse
        onBrowse();

        // On click outside popover
        onClickOutsideToHidePopover();

        // Handle input typing
        // // onInputChange();

        // // Hide popover when clicking outside

        // // OnInputSearchInterestsSuggestions
        // onSearchInterestSuggestion();

        // // On select item insterest on popover


        // On remove selected Item
        onRemoveSelectedItem()

        // Handle Search Detail Targeting
        // handleSearchDetailTargeting();

        getDetailTargetingBrowse();


        // // Handle get Targeting Browse
        // getDetailTargetingBrowse();
    }

    return {
        updateInterestsFromAdset,
        resetDetailsTargeting,
        init,
    };
})();
