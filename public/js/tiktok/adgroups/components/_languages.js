import { LANGUAGES_TARGETING } from "/static/js/constants/adset-constants.js";
let isInit = true;
export default (() => {
    let languagesChoices;
    const ID_LANGUAGE_CHOICES = "#languageTargeting";
    const API_END_POINT = {
        LIST_LANGUAGE: `${window.location.origin}/dsp/tiktok/api/targeting/languages/list`,
    }
    const languagesChoicesConfig = {
        shouldSort: false,
        allowHTML: true,
        removeItemButton: true,
        renderSelectedChoices: "auto",
        placeholder: true,
        placeholderValue: "--- Choose Language - Default All languages ---",
        searchEnabled: true,
        classNames: {
            containerOuter: "choices tiktok-custom",
        },
        //  callbackOnCreateTemplates: getOptionTemplate,
    };

    function setSelectDefault() {
        let defaultValue = $(ID_LANGUAGE_CHOICES).data("default-value");
        if (defaultValue) {
            try {
                if (!Array.isArray(defaultValue)) {
                    defaultValue = JSON.parse(defaultValue.replaceAll(" ", ","));
                }
                if (defaultValue && defaultValue.length > 0) {
                    defaultValue = defaultValue.map((i) => i.toString());
                    languagesChoices.setChoiceByValue(defaultValue);
                    languagesChoices.enable();
                }
            } catch (e) { }
        }
    }

    function setSelectByValues(values) {
        languagesChoices.setChoiceByValue(values);
        languagesChoices.enable();
    }

    const intEventHandler = () => {
        //Custom event remove Pages Selected
        $(ID_LANGUAGE_CHOICES)
            .parent(".choices__inner")
            .on("click", ".btn-remove-page-selected", (event) => {
                event.stopPropagation();
                languagesChoices.removeActiveItemsByValue(event.target.dataset.value);
            });
    };

    const getDataChoice = async function (choiceEl) {
        languagesChoices = choiceEl;
        //Remove old options
        languagesChoices.clearChoices();

        const response = await axios.post(API_END_POINT.LIST_LANGUAGE, {
            search: null,
        });

        //Add new options
        let newOptions = response.data.data.map((item) => ({
            value: item.code,
            label: item.name,
            id: item.code,
        }));

        //Update state choice
        languagesChoices.setChoices(newOptions, "value", "label", true);
        setSelectDefault();
    };

    function init() {
        if ($(ID_LANGUAGE_CHOICES).length) {

            const choice = new Choices(ID_LANGUAGE_CHOICES, {
                ...languagesChoicesConfig,
            }).disable();
            getDataChoice(choice);
            choice.enable();
        }
        intEventHandler();
    }
    return {
        init,
        setSelectByValues,
        languagesChoicesConfig,
    };
})();
