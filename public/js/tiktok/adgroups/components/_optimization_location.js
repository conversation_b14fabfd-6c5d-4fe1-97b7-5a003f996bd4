import {
  <PERSON>UTCO<PERSON>_REACH,
  OUTCOME_TRAFFIC,
  OUTCOME_LEAD_GENERATION,
} from "/static/js/tiktok/constants/campaign.js";
import {
  OPTIMIZATION_LOCATION_WEBSITE_LEAD,
  OPTIMIZATION_LOCATION_INSTANT_FORM,
  OPTI<PERSON>ZATION_LOCATION_TIKTOK_DIRECT_MESSAGES,
  OPTIMIZATION_LOCATION_INSTANT_MESSAGING_APPS,
} from "/static/js/tiktok/constants/adgroup.js";

export default (() => {
  let isInit = false;
  const ID_ADGOUP_FORM = "#adgroupForm";
  const $optimizationLocationCard = $("#optimizationLocationCard");
  const $optionOptimizationLocationByOutcomeObjective = $(
    "#optionOptimizationLocationByOutcomeObjective"
  );
  const $outcomeObjectiveEl = $("#outcomeObjective");

  /**
   * Render the optimization location options based on the selected outcome objective.
   */
  function renderOptionByOutcomeObjectiveUI() {
    const outcomeObjective = $outcomeObjectiveEl.val();
    switch (outcomeObjective) {
      case OUTCOME_REACH:
        $optimizationLocationCard.hide(150);
        $optionOptimizationLocationByOutcomeObjective.empty();
        break;
      case OUTCOME_TRAFFIC:
        $optimizationLocationCard.show(150);
        $optionOptimizationLocationByOutcomeObjective
          .empty()
          .html($("#optionOptimizationLocationHtmlByTRAFFIC").html());
        updateTitleOptimizationLocationCard(
          "Optimization location",
          "Select where you'd like to direct traffic."
        );

        addEventForOptimizationLocationWEBSITE();
        break;
      case OUTCOME_LEAD_GENERATION:
        $optimizationLocationCard.show(150);
        $optionOptimizationLocationByOutcomeObjective
          .empty()
          .html($("#optionOptimizationLocationHtmlByLEAD_GENERATION").html());
        updateTitleOptimizationLocationCard(
          "Optimization",
          "Select your lead goal, add events, and choose where you’d like to drive more leads."
        );
        addEventForOptimizationLocationLEAD_GENERATION();
        break;
    }
  }

  function updateTitleOptimizationLocationCard(title, subtitle) {
    $("#optimizationLocationTitle").text(title);
    $("#optimizationLocationDes").text(subtitle);
  }

  /**
   * Add event listeners for the optimization location options when the outcome objective is WEBSITE.
   */
  function addEventForOptimizationLocationWEBSITE() {
    const $websiteOption = $("#optimizationLocationWEBSITE");
    const $appOption = $("#optimizationLocationAPP");
    const $appContainer = $("#optimizationLocationContainerAPP");

    $appOption.on("change", function () {
      if ($(this).is(":checked")) {
        $appContainer.show(200);
      }
    });
    $websiteOption.on("change", function () {
      if ($(this).is(":checked")) {
        $appContainer.hide(200);
      }
    });
  }

  /**
   * Render the additional configuration container based on the selected optimization location.
   */
  function renderMoreConfigContainerByLocation(locationSelected) {
    const $moreConfigContainerByLocation = $("#moreConfigContainerByLocation");

    switch (locationSelected) {
      case OPTIMIZATION_LOCATION_WEBSITE_LEAD:
        $moreConfigContainerByLocation
          .empty()
          .html($("#moreConfigContainerByLocationWEBISTE_LEAD").html());
        break;
      case OPTIMIZATION_LOCATION_INSTANT_FORM:
        $moreConfigContainerByLocation
          .empty()
          .html($("#moreConfigContainerByLocationINSTANT_FORM").html());
        break;
      case OPTIMIZATION_LOCATION_TIKTOK_DIRECT_MESSAGES:
        $moreConfigContainerByLocation
          .empty()
          .html(
            $("#moreConfigContainerByLocationTIKTOK_DIRECT_MESSAGES").html()
          );
        break;
      case OPTIMIZATION_LOCATION_INSTANT_MESSAGING_APPS:
        $moreConfigContainerByLocation
          .empty()
          .html(
            $("#moreConfigContainerByLocationINSTANT_MESSAGING_APPS").html()
          );
        break;
    }
  }

  /**
   * Add event listeners for the optimization location options when the outcome objective is LEAD_GENERATION.
   */
  function addEventForOptimizationLocationLEAD_GENERATION() {
    // const $websiteOption = $("#optimizationLocationWEBSITE");
    // const $instantFormOption = $("#optimizationLocationINSTANT_FORM");
    // const $tiktokDirectMessagesOption = $("#optimizationLocationTIKTOK_DIRECT_MESSAGES");
    // const $instantMessagingAppsOption = $("#optimizationLocationINSTANT_MESSAGING_APPS");

    const $locationOptions = $("input[name='optimization_location']");
    $locationOptions.each(function (v, i) {
      const $this = $(this);
      if ($this.is(":checked")) {
        renderMoreConfigContainerByLocation($this.val());
      }
    });

    $locationOptions.on("change", function () {
      let locationSelected = "";
      if ($(this).is(":checked")) {
        locationSelected = $(this).val();
      }
      renderMoreConfigContainerByLocation(locationSelected);
    });
  }

  /**
   * Initialize the optimization location UI.
   */
  function init() {
    renderOptionByOutcomeObjectiveUI();
    if (!isInit) {
      addEventForOptimizationLocationWEBSITE();
      isInit = true;
    }
  }
  return {
    init,
  };
})();
