import { requestApi } from "/static/js/common/httpService.js";

export default (() => {
    const internetServiceProviderSelector = '#internetProviderChoices';
    const geoLocationsSelector = '#selectedGeoLocations';

    let internetServiceProviderChoiceInstance;
    let isps = null;

    var API_END_POINT = {
        TARGETING_ISP_LIST: `${window.location.origin}/dsp/tiktok/api/targeting/isp/list`,
    }

    function mapInternetServiceProviderToChoices(data) {
        return data.map(item => ({
            value: item.isp.isp_id,
            label: item.isp.region_code + ": " + item.name.trim(),
        }));
    }

    function InitInternetServiceProvider() {
        const element = document.querySelector(internetServiceProviderSelector);
        if (!element) return;

        function destroyAndInitChoices(choices = []) {
            if (internetServiceProviderChoiceInstance && internetServiceProviderChoiceInstance.destroy) {
                try {
                    internetServiceProviderChoiceInstance.destroy();
                } catch (err) {
                    console.warn("Choices destroy failed:", err);
                }
                internetServiceProviderChoiceInstance = null;
            }

            element.innerHTML = '';

            internetServiceProviderChoiceInstance = new Choices(element, {
                shouldSort: false,
                allowHTML: true,
                removeItemButton: true,
                renderSelectedChoices: "auto",
                placeholder: true,
                placeholderValue: 'All',
                searchEnabled: true,
                classNames: {
                    containerOuter: "choices tiktok-custom",
                },
                choices
            });
        }

        window.addEventListener('regionSelectionUpdated', async function (event) {
            const { selectedCountryCodes = [] } = event.detail;

            if (!Array.isArray(selectedCountryCodes) || selectedCountryCodes.length === 0) {
                destroyAndInitChoices([]);
                return;
            }

            try {
                const res = await requestApi("POST", API_END_POINT.TARGETING_ISP_LIST, {
                    search: "",
                    country_codes: selectedCountryCodes
                });

                const isps = Array.isArray(res.data) ? res.data : [];
                const choices = mapInternetServiceProviderToChoices(isps);

                destroyAndInitChoices(choices);
            } catch (error) {
                console.error("Failed to load ISPs:", error);
                destroyAndInitChoices([]);
            }
        });

        const selectedGeoEl = document.querySelector("#selectedGeoLocations");
        if (selectedGeoEl) {
            try {
                const selected = JSON.parse(selectedGeoEl.getAttribute("data-value") || "{}");
                const selectedCountryCodes = selected?.COUNTRY || [];

                window.dispatchEvent(new CustomEvent("regionSelectionUpdated", {
                    detail: {
                        selectedCountryCodes,
                        selectedRegionItems: selected
                    }
                }));
            } catch (err) {
                console.warn("Failed to parse initial selectedGeoLocations:", err);
            }
        }
    }


    function init() {
        InitInternetServiceProvider();
    }

    return {
        init,
    }
})()