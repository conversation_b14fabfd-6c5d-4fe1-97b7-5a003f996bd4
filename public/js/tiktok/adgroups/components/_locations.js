import Alert from "/static/js/components/alert.js";
import { TARGETING_TYPES, TARGETING_GEO_LOCATION } from "/static/js/constants/targeting-constant.js";
import { convertStringToIntArray } from "/static/js/common/helpers.js";

const API_END_POINT = {
    GET_COUNTRY_GROUP: `${window.location.origin}/dsp/tiktok/api/targeting/regions/list`,
    // SEARCH_CITY_SUB_CITIES: `${window.location.origin}/dsp/facebook/api/targeting/search-targeting/city-sub-cities`,
    // TARGETING_BROWSE_LIST: `${window.location.origin}/dsp/facebook/api/targeting/targeting-browse/list`,
    // TARGETING_SEARCH: `${window.location.origin}/dsp/facebook/api/targeting/search`,
};
const offcanvasFormSelector = "#adgroupForm";



const popoverLocationPath = "#searchSearchLocationsPopover";
const inputSearchLocation = "#selectedGeoLocations";
const $popover = $(popoverLocationPath);
const $tabSubCities = $("#content_sub_cities");
const $tabCountries = $("#content_countries");
const resultsSelectorPath = `#geo_location_results_selected`;
const noResultHTML = `<div class="no-results">
            <img src="/static/images/no-data.png" alt="No Results" style="max-width: 250px; margin-bottom: 10px;">
            <p>No Data</p>
         </div>`;

/**
* Get Country Group
*/
const getCountryGroup = debounce(async (callback) => {
    try {
        const response = await axios.post(API_END_POINT.GET_COUNTRY_GROUP, {
            search: null,
        });
        callback(mappingCountryGroup(response.data.data.data));
    } catch (error) {
        console.error(error);
        callback(null);
        Alert.error(error?.response?.data?.message ?? "Load country fail!");
    }
}, 500);

/**
 * mappingCountryGroup
 */
function mappingCountryGroup(data) {
    const countryMapping = {
        countries: {},
        regions: {
            free_trade_areas: [],
            app_store_regions: [],
            emerging_markets: [],
            euro_area: [],
        },
    };
    data.forEach((item) => {
        // if (countryGroupCountries.countries[item.key]) {
        //     countryMapping.countries[item.key] = item;
        // } else if (countryGroupCountries.regions[item.key]) {
        //     countryMapping.regions[item.key] = item;
        // } else if (countryGroupCountries.regions.free_trade_areas.country_codes.includes(item.key)) {
        //     countryMapping.regions.free_trade_areas.push(item);
        // } else if (countryGroupCountries.regions.app_store_regions.country_codes.includes(item.key)) {
        //     countryMapping.regions.app_store_regions.push(item);
        // }
    });
    return data;
}

/**
 * Render Item audience select
 */
function renderItemSelectedRegion() {
    const selectedGeoEl = $(`${offcanvasFormSelector} #selectedGeoLocations`);
    const selectedItems = JSON.parse(selectedGeoEl.attr("data-value")) || {};
    const $selectEl = $(`${offcanvasFormSelector} #geo_location_results_selected`);


    const hasValidSelections = Object.values(selectedItems).some(arr => Array.isArray(arr) && arr.length > 0);
    if (!hasValidSelections) return $selectEl.empty().hide();

    $selectEl.empty().show();

    const regionMap = {};
    (function buildRegionMap(regions) {
        regions.forEach(r => {
            regionMap[r.region_id] = r;
            if (r.children) buildRegionMap(r.children);
        });
    })(window.targeting.location || []);

    function getFullRegionName(region) {
        if (region.region_level === "COUNTRY") return region.region_name;

        const names = [];
        let current = region;
        while (current && current.region_level !== "COUNTRY") {
            names.push(current.region_name);
            current = regionMap[current.parent_id];
        }

        return names.join(", ");
    }

    const groupByCountry = {};

    for (const [level, ids] of Object.entries(selectedItems)) {
        ids.forEach(id => {
            const region = regionMap[id];
            if (!region) return;

            // Determine country
            let current = region;
            while (current && current.region_level !== "COUNTRY") {
                current = regionMap[current.parent_id];
            }

            const countryId = current?.region_id || region.region_id;
            const countryName = current?.region_name || region.region_name;

            if (!groupByCountry[countryId]) {
                groupByCountry[countryId] = {
                    countryName,
                    items: []
                };
            }

            groupByCountry[countryId].items.push(region);
        });
    }

    const selectedHTML = Object.entries(groupByCountry).map(([countryId, group]) => {
        const itemsHTML = group.items.map(item => {
            return `
                <div class="selected-item d-flex align-items-center justify-content-between gap-2 mb-1" 
                    data-id="${item.region_id}" 
                    data-group="${countryId}" 
                    data-name="${item.region_name}">
                    <div>${getFullRegionName(item)}</div>
                    <i class="ri-close-circle-fill btn-remove-item text-muted cursor-pointer"></i>
                </div>
            `;
        }).join("");

        return `
            <div class="group-item" data-value="${countryId}">
                <div class="header text-secondary fw-semibold mb-1">${group.countryName}</div>
                ${itemsHTML}
            </div>
        `;
    }).join("");

    $selectEl.html(selectedHTML);
}

/**
 * Update State Checkbox Country Group
 */
export function updateStateCheckboxCountry() {
    let selectedItems = JSON.parse(inputSearchLocation.attr("data-value")) || {};
    const countriesSelected = selectedItems[TARGETING_GEO_LOCATION.COUNTRIES] || [];
    const countryGroupsSelected = selectedItems[TARGETING_GEO_LOCATION.COUNTRY_GROUP] || [];
    $(`${popoverLocationPath} input`).each(function () {
        this.checked = false;
    });
    countriesSelected.forEach((key) => {
        $(`${popoverLocationPath} input[value="${key}"]`).prop("checked", true);
    });
    countryGroupsSelected.forEach((key) => {
        $(`${popoverLocationPath} input[value="${key}"]`).prop("checked", true);
    });
}

/**
 * Handles Get Search Audience
 */
const handleSearchCitySubCities = debounce(async (value, callback) => {
    try {
        const response = await axios.post(API_END_POINT.SEARCH_CITY_SUB_CITIES, { search_value: value });
        callback(response.data.data.data);
    } catch (error) {
        console.error(error);
        callback([]);
        Alert.error(error?.response?.data?.message ?? "Please try again!");
    }
}, 1000);

/**
 * Handles Render Select Audience Item Audiences
 */
function renderSelectCitySubCitiesItem(data) {
    return `
       <li class="list-group-item city" data-id="${data.key}" data-name="${data.name}" data-group="${data.country_code}" data-country-name="${data.country_name}">
           <div class="d-flex flex-column align-content-center item-left">
               <div class="item-name">
                   <span class="text-success fs-1">&bull;</span>
                   <span class="ml-2 name">${data.name}</span>
               </div>
           </div>
           <div class="text-muted item-right">${data.type}</div>
       </li>`;
}

/**
 * Custom Include Audience
 */
export default (() => {
    const locationSelect = { COUNTRY: ["1562822"] };
    let isExistListCountry = false;

    function addSelectedRegion(region) {
        const selectedGeoEl = $(`${offcanvasFormSelector} #selectedGeoLocations`);
        const selectedItems = JSON.parse(selectedGeoEl.attr("data-value")) || {};

        // 1. Build full region map
        const regionMap = {};
        const buildRegionMap = (regions) => {
            regions.forEach(r => {
                regionMap[r.region_id] = r;
                if (Array.isArray(r.children)) buildRegionMap(r.children);
            });
        };
        buildRegionMap(window.targeting.location || []);

        const fullRegion = typeof region === 'string' ? regionMap[region] : regionMap[region.region_id];
        if (!fullRegion) return;

        const type = fullRegion.region_level;
        const id = fullRegion.region_id;

        const getParent = (r) => r?.parent_id ? regionMap[r.parent_id] : null;
        const getCountry = (r) => {
            let cur = r;
            while (cur && cur.region_level !== "COUNTRY") {
                cur = getParent(cur);
            }
            return cur;
        };

        const country = getCountry(fullRegion);
        const countryId = country?.region_id;

        if (type === "COUNTRY") {
            ["CITY", "PROVINCE", "DISTRICT"].forEach(childType => {
                const list = selectedItems[childType];
                if (Array.isArray(list)) {
                    selectedItems[childType] = list.filter(cid => {
                        const childRegion = regionMap[cid];
                        const childCountry = getCountry(childRegion);
                        return childCountry?.region_id !== id;
                    });
                }
            });
        }

        // 🆕 5. Nếu chọn PROVINCE/CITY/DISTRICT → loại bỏ các children đã chọn bên dưới nó
        const removeChildrenOf = (region) => {
            const queue = [region];
            while (queue.length) {
                const current = queue.shift();
                if (!current || !current.children) continue;

                current.children.forEach(child => {
                    // Xóa nếu đang có trong selected
                    const list = selectedItems[child.region_level];
                    if (Array.isArray(list)) {
                        const idx = list.indexOf(child.region_id);
                        if (idx > -1) {
                            list.splice(idx, 1);
                        }
                    }

                    // Tiếp tục duyệt xuống nữa
                    queue.push(regionMap[child.region_id]);
                });
            }
        };
        removeChildrenOf(fullRegion);

        // 6. Nếu COUNTRY đã chọn rồi thì không thêm lại PROVINCE/CITY
        if (type !== "COUNTRY") {
            const selectedCountries = selectedItems["COUNTRY"] || [];
            if (selectedCountries.includes(countryId)) return;
        }

        // 7. Thêm vào selectedItems
        const list = Array.isArray(selectedItems[type]) ? selectedItems[type] : [];
        if (!list.includes(id)) {
            list.push(id);
        }
        selectedItems[type] = list;

        // 8. Cập nhật lại
        selectedGeoEl.attr("data-value", JSON.stringify(selectedItems));
    }


    function removeSelectedRegion(region) {
        const selectedGeoEl = $(`${offcanvasFormSelector} #selectedGeoLocations`);
        let selectedItems = {};
        try {
            selectedItems = JSON.parse(selectedGeoEl.attr("data-value")) || {};
        } catch (e) {
            console.warn("data-value parse error, reset to empty object");
            selectedItems = {};
        }

        const type = region.region_level;
        const id = region.region_id;

        const regionMap = {};
        const buildRegionMap = (regions) => {
            regions.forEach(r => {
                regionMap[r.region_id] = r;
                if (Array.isArray(r.children)) buildRegionMap(r.children);
            });
        };
        buildRegionMap(window.targeting.location || []);

        const getParent = (r) => r?.parent_id ? regionMap[r.parent_id] : null;
        const getCountry = (r) => {
            let cur = r;
            while (cur && cur.region_level !== "COUNTRY") {
                cur = getParent(cur);
            }
            return cur;
        };

        const fullRegion = regionMap[region.region_id];
        if (!fullRegion) return;

        // Step 1: remove region from selectedItems[type]
        const list = selectedItems[type] || [];
        const index = list.indexOf(id);
        if (index !== -1) {
            list.splice(index, 1);
            selectedItems[type] = list;
        }

        // Step 2: nếu parent đang được chọn thì xoá nó → thêm lại toàn bộ con (trừ cái đang gỡ)
        const parent = getParent(fullRegion);
        if (parent) {
            const parentList = selectedItems[parent.region_level] || [];
            const parentIndex = parentList.indexOf(parent.region_id);
            if (parentIndex !== -1) {
                // Xoá parent
                parentList.splice(parentIndex, 1);
                selectedItems[parent.region_level] = parentList;

                // Thêm tất cả con của parent (trừ cái đang bị remove)
                const children = parent.children || [];
                const childIds = children.map(c => c.region_id).filter(cid => cid !== id);

                const childType = fullRegion.region_level;
                const existing = selectedItems[childType] || [];
                const merged = [...new Set([...existing, ...childIds])];

                selectedItems[childType] = merged;
            }
        }

        // Step 3: tương tự nếu COUNTRY được chọn → tách COUNTRY ra và add lại toàn bộ PROVINCE/CITY
        const country = getCountry(fullRegion);
        if (country && country.region_id !== parent?.region_id) {
            const countryList = selectedItems["COUNTRY"] || [];
            const cIndex = countryList.indexOf(country.region_id);
            if (cIndex !== -1) {
                countryList.splice(cIndex, 1);
                selectedItems["COUNTRY"] = countryList;

                // Đệ quy lấy tất cả các region cấp dưới COUNTRY
                const collectChildren = (node, level, skipId) => {
                    let result = [];
                    if (!node || !node.children) return result;
                    node.children.forEach(child => {
                        if (child.region_id !== skipId) {
                            result.push({ id: child.region_id, level: child.region_level });
                            result = result.concat(collectChildren(regionMap[child.region_id], level + 1, skipId));
                        }
                    });
                    return result;
                };

                const allSubRegions = collectChildren(country, 1, id);
                allSubRegions.forEach(({ id, level }) => {
                    const arr = Array.isArray(selectedItems[level]) ? selectedItems[level] : [];
                    if (!arr.includes(id)) {
                        arr.push(id);
                        selectedItems[level] = arr;
                    }
                });
            }
        }

        // Step 4: Update lại value
        selectedGeoEl.attr("data-value", JSON.stringify(selectedItems));
    }


    function filterDuplicateLocation(selectedItems) {
        const countries = selectedItems[TARGETING_GEO_LOCATION.COUNTRIES] || [];
        const cities = selectedItems[TARGETING_GEO_LOCATION.CITIES] || [];
        if (countries.length && cities.length) {
            cities.forEach((city) => {
                const index = countries.indexOf(city.country);
                if (index !== -1) countries.splice(index, 1);
            });
            selectedItems[TARGETING_GEO_LOCATION.COUNTRIES] = countries;
        }
    }

    function executiveRemoveItemSelectedGeoLocationsInput(idItem) {
        let selectedItems = JSON.parse(inputSearchLocation.attr("data-value")) || {};
        const countriesSelected = selectedItems[TARGETING_GEO_LOCATION.COUNTRIES] || [];
        const countryGroupsSelected = selectedItems[TARGETING_GEO_LOCATION.COUNTRY_GROUP] || [];
        const citiesSelected = selectedItems[TARGETING_GEO_LOCATION.CITIES] || [];

        const removeItem = (array, item) => {
            const index = array.indexOf(item);
            if (index > -1) array.splice(index, 1);
        };

        removeItem(countriesSelected, idItem);
        removeItem(countryGroupsSelected, idItem);
        const cityIndex = citiesSelected.findIndex((item) => item.key === idItem);
        if (cityIndex > -1) citiesSelected.splice(cityIndex, 1);

        selectedItems[TARGETING_GEO_LOCATION.COUNTRIES] = countriesSelected;
        selectedItems[TARGETING_GEO_LOCATION.COUNTRY_GROUP] = countryGroupsSelected;
        selectedItems[TARGETING_GEO_LOCATION.CITIES] = citiesSelected;

        inputSearchLocation.attr("data-value", JSON.stringify(selectedItems));
        if (!Object.keys(selectedItems).length || (!citiesSelected.length && !countriesSelected.length)) {
            $(`${offcanvasFormSelector} ${resultsSelectorPath}`).hide();
        }
    }

    function removeSelectedItem(idItem, groupKey) {
        const groupItemEl = $(`${offcanvasFormSelector} ${resultsSelectorPath} .group-item[data-value="${groupKey}"] .selected-item`);
        if (!groupItemEl.length) {
            $(`${offcanvasFormSelector} ${resultsSelectorPath} .group-item[data-value="${groupKey}"]`).remove();
        }
        $(`${offcanvasFormSelector} ${popoverLocationPath} input[value="${idItem}"]`).prop("checked", false);
        executiveRemoveItemSelectedGeoLocationsInput(idItem);

        const elSelectedItems = $(`${resultsSelectorPath}`);
        if (!elSelectedItems.html().trim()) {
            elSelectedItems.hide();
        }
    }

    function handleGetCountryGroup() {
        const groupCountryExist = window?.targeting?.location;
        if (groupCountryExist) {
            initRegionTree(groupCountryExist);
            renderItemSelectedRegion();
            return;
        }
        getCountryGroup((results) => {
            if (!results) {
                $tabCountries.append(noResultHTML);
                return;
            }
            window.targeting = window.targeting || {};
            window.targeting.location = results;
            initRegionTree(results);
            renderItemSelectedRegion();
            isExistListCountry = true;
        });
    }

    function onSearch() {
        const query = inputSearchLocation.val();
        const typeSearch = $(`${offcanvasFormSelector} #searchLocationsCountriesTab button.active`).attr("data-bs-target");
        if (typeSearch === "#content_tab_sub_cities") {
            if (!query) return;
            handleSearchCitySubCities(query, (results) => {
                $tabSubCities.empty();
                if (!results.length) {
                    $tabSubCities.append(noResultHTML);
                    return;
                }
                results.forEach((item) => {
                    $tabSubCities.append(renderSelectCitySubCitiesItem(item));
                    $tabSubCities.find(`li[data-id="${item.key}"]`).attr("data-city", JSON.stringify(item));
                });
            });
        } else {
            // renderLocation(null, query);
        }
    }

    // Update targeting from adset
    function updateValuSelectedLocation(targeting) {
        inputSearchLocation.attr("data-value", JSON.stringify(targeting || { countries: ["VN"] }));
        if (isExistListCountry) {
            renderItemSelected();
        }
    }

    /**
     * Handle Excercutive Placement to Request API
     */
    function handleExcecutiveTargetingToRequest(requestData) {
        for (const [targetingField, targetingValue] of Object.entries(requestData.targeting)) {
            if ([TARGETING_TYPES.AGE_MIN, TARGETING_TYPES.AGE_MAX].includes(targetingField)) {
                requestData.targeting[targetingField] = parseInt(targetingValue);
            }

            if (TARGETING_TYPES.AGE_RANGE === targetingField) {
                requestData.targeting[targetingField] = convertStringToIntArray(targetingValue);
            }

            if (TARGETING_TYPES.GENDERS === targetingField) {
                const gender = targetingValue?.[0] ?? "";
                if (gender === "") {
                    delete requestData.targeting[targetingField];
                    continue;
                }

                requestData.targeting[targetingField] = convertStringToIntArray(targetingValue);
            }

            if (TARGETING_TYPES.EXCLUDED_CUSTOM_AUDIENCES === targetingField) {
                const selected = JSON.parse(document.querySelector("#excludeSelectedAudiences").dataset.value ?? "[]");
                const dataConvert = Object.values(selected).flat();
                requestData.targeting[targetingField] = Object.values(dataConvert).flat();
            }

            if (TARGETING_TYPES.CUSTOM_AUDIENCES === targetingField) {
                const selected = JSON.parse(document.querySelector("#selectedAudiences").dataset.value ?? "[]");
                const dataConvert = Object.values(selected).flat();
                requestData.targeting[targetingField] = Object.values(dataConvert).flat();
                if (!requestData.targeting[targetingField].length) {
                    delete requestData.targeting[targetingField];
                    requestData.targeting["targeting_automation"] = {
                        advantage_audience: 1,
                    };
                } else {
                    delete requestData.targeting["targeting_automation"];
                }
            }

            if (TARGETING_TYPES.LOCALES === targetingField) {
                requestData.targeting[targetingField] = targetingValue.map((i) => parseInt(i));
            }

            if (TARGETING_TYPES.GEO_LOCATIONS === targetingField) {
                requestData.targeting[targetingField] = { ...JSON.parse(document.querySelector("#selectedGeoLocations").dataset.value), location_types: ["home", "recent"] };
                if (requestData.targeting[targetingField]["cities"]) {
                    const cities = [];
                    requestData.targeting[targetingField]["cities"].forEach((c) => {
                        cities.push({
                            key: c.key,
                            radius: 20,
                            distance_unit: "kilometer",
                        });
                    });
                    requestData.targeting[targetingField]["cities"] = cities;
                }
            }

            if (TARGETING_TYPES.FLEXIBLE_SPEC === targetingField) {
                let interests = $('input[name="targeting[flexible_spec][]"]').attr("data-value");
                if (interests) {
                    interests = JSON.parse(interests);
                    requestData.targeting[targetingField] = [interests];
                }
            }

            if (TARGETING_TYPES.DEVICE_PLATFORMS === targetingField) {
                if (!Array.isArray(targetingValue)) {
                    requestData.targeting[targetingField] = [targetingValue];
                }
            }
        }
        return requestData;
    }

    /**
     * Validation Targeting
     */
    function checkValidationTargeting(requestData) {
        if (requestData?.targeting?.targeting_automation?.advantage_audience === 1) {
            if (requestData?.targeting?.age_max < 65) {
                return {
                    status: false,
                    message: "With ad sets that use Advantage+ audience, the maximum age audience control can't be set to lower than 65. ",
                };
            }
        }
        return {
            status: true,
            message: null,
        };
    }

    function renderRegions(regions, level = 0) {
        return regions.map(region => {
            const hasChildren = region.children && region.children.length > 0;

            function escapeHTMLAttr(str) {
                return str
                    .replace(/&/g, "&amp;")
                    .replace(/'/g, "&#39;")
                    .replace(/"/g, "&quot;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;");
            }

            return `
            <div class="region-item" data-name="${region.region_name.toLowerCase()}">
                <div class="d-flex align-items-center gap-2">
                    <span class="toggle-icon d-inline-block" style="width: 20px; cursor: pointer;">
                        ${hasChildren ? '<i class="bx bx-chevron-right fs-20"></i>' : ''}
                    </span>
                    <div class="form-check form-check-success flex-grow-1 mb-2">
                        <input class="form-check-input region-checkbox" type="checkbox" id="${region.region_id}" data-country-code="${region.country_code}" data-region='${escapeHTMLAttr(JSON.stringify(region))}'>
                        <label class="form-check-label region-name" for="${region.region_id}">
                            ${region.region_name}
                        </label>
                    </div>
                    <small class="text-muted ms-2">${region.region_level.toLowerCase()}</small>
                </div>
    
                ${hasChildren ? `
                    <div class="region-children ms-4 mt-1" style="display: none;">
                        ${renderRegions(region.children, level + 1)}
                    </div>
                ` : ''}
            </div>
            `;
        }).join('');
    }

    function bindToggleExpand() {
        $(document).off('click', '.toggle-icon');

        $(document).on('click', '.toggle-icon', function () {
            const $icon = $(this).find('i');
            const $children = $(this).closest('.region-item').children('.region-children');

            $children.slideToggle(150);
            $icon.toggleClass('bx-chevron-right bx-chevron-down');
        });
    }

    function bindSearchFilter() {
        $('#search').on('input', function () {
            const val = $(this).val().toLowerCase();

            $('.region-item').each(function () {
                const $item = $(this);
                const name = $item.data('name');

                if (name.includes(val)) {
                    $item.show();
                    $item.parents('.region-children').show();
                    $item.find('.toggle-icon i').removeClass('bx-chevron-right').addClass('bx-chevron-down');
                } else {
                    $item.hide();
                }
            });
        });
    }

    function bindCheckboxCascade() {
        $(document).on('change', '.region-checkbox', function () {
            const $item = $(this).closest('.region-item');
            const isChecked = $(this).is(':checked');

            $item.find('.region-children .region-checkbox').prop('checked', isChecked);
        });
    }

    function initRegionTree(results) {
        $('#region-container').html(renderRegions(results));
        bindToggleExpand();
        bindSearchFilter();
        bindCheckboxCascade();
    }

    // Init Event
    function init() {
        // $(inputSearchLocation).on("input", onSearch);
        handleGetCountryGroup();

        $(inputSearchLocation).on("focus", () => $(popoverLocationPath).show());

        $(document).on("click", (e) => {
            if (!$(e.target).closest(inputSearchLocation).length && !$(e.target).closest($(popoverLocationPath)).length) {
                $(popoverLocationPath).hide();
            }
        });

        $(inputSearchLocation).val("");

        $("#region-container").on('change', '.form-check-input[type="checkbox"]', function () {
            const region = $(this).data("region");

            if (!region || !region.region_id || !region.region_level) return;

            if ($(this).is(':checked')) {
                addSelectedRegion(region);
            } else {
                removeSelectedRegion(region);
            }

            const selectedGeoEl = $(`${offcanvasFormSelector} #selectedGeoLocations`);
            const selectedItems = JSON.parse(selectedGeoEl.attr("data-value")) || {};
            window.dispatchEvent(new CustomEvent('regionSelectionUpdated', {
                detail: {
                    selectedCountryCodes: getCountryCodesFromSelected(),
                    selectedRegionItems: selectedItems
                }
            }));
            renderItemSelectedRegion();
        });


        // $(`${offcanvasFormSelector} #searchSearchLocationsPopover #content_countries`).on("click", ".country-item, .country-group-item", function () {
        //     const eClass = this.className;
        //     const value = this.value;

        //     if (!$(this).is(":checked")) {
        //         $(`${offcanvasFormSelector} ${resultsSelectorPath} .selected-item[data-id="${value}"]`).remove();
        //         removeSelectedItem(value, value);
        //         return;
        //     }
        //     if (eClass.includes("country-item")) {
        //         addSelected(TARGETING_GEO_LOCATION.COUNTRIES, value);
        //     }
        //     if (eClass.includes("country-group-item")) {
        //         if (value === TARGETING_GEO_LOCATION.FREE_TRADE_AREAS) {
        //             addSelected(TARGETING_GEO_LOCATION.COUNTRY_GROUP, countryGroupCountries.regions.free_trade_areas.country_codes);
        //             return;
        //         }
        //         if (value === TARGETING_GEO_LOCATION.APP_STORE_REGIONS) {
        //             addSelected(TARGETING_GEO_LOCATION.COUNTRY_GROUP, countryGroupCountries.regions.app_store_regions.country_codes);
        //             return;
        //         }
        //         addSelected(TARGETING_GEO_LOCATION.COUNTRY_GROUP, [value]);
        //     }

        //     renderItemSelected();
        // });

        // $(`${offcanvasFormSelector} #searchSearchLocationsPopover #content_sub_cities`).on("click", ".city", function () {
        //     inputSearchLocation.val("");
        //     addSelected(TARGETING_GEO_LOCATION.CITIES, JSON.parse(this.dataset.city));
        //     renderItemSelected();
        // });

        $(`${offcanvasFormSelector} ${resultsSelectorPath}`).on("click", ".btn-remove-item", function () {
            const $item = $(this).closest('.selected-item');
            const regionId = $item.data('id');

            if (!regionId) return;

            const regionMap = {};
            (function buildRegionMap(regions) {
                regions.forEach(r => {
                    regionMap[r.region_id] = r;
                    if (Array.isArray(r.children)) buildRegionMap(r.children);
                });
            })(window?.targeting?.location || []);

            const region = regionMap[regionId];
            if (!region) return;

            removeSelectedRegion(region);

            $(`#region-container .form-check-input[id="${regionId}"]`).prop("checked", false);

            const selectedGeoEl = $(`${offcanvasFormSelector} #selectedGeoLocations`);
            const selectedItems = JSON.parse(selectedGeoEl.attr("data-value")) || {};
            window.dispatchEvent(new CustomEvent('regionSelectionUpdated', {
                detail: {
                    selectedCountryCodes: getCountryCodesFromSelected(),
                    selectedRegionItems: selectedItems
                }
            }));

            renderItemSelectedRegion();
        });
    }


    function getCountryCodesFromSelected() {
        const selectedGeoEl = $("#selectedGeoLocations");
        let selectedItems = {};

        try {
            selectedItems = JSON.parse(selectedGeoEl.attr("data-value")) || {};
        } catch (e) {
            console.warn("Invalid data-value format in #selectedGeoLocations");
            return [];
        }

        // Build regionMap
        const regionMap = {};
        const buildRegionMap = (regions) => {
            regions.forEach(r => {
                regionMap[r.region_id] = r;
                if (Array.isArray(r.children)) {
                    buildRegionMap(r.children);
                }
            });
        };
        buildRegionMap(window?.targeting?.location || []);

        const getParent = (r) => r?.parent_id ? regionMap[r.parent_id] : null;
        const getCountry = (r) => {
            let cur = r;
            while (cur && cur.region_level !== "COUNTRY") {
                cur = getParent(cur);
            }
            return cur;
        };

        // Duyệt tất cả region hiện đang được chọn để gom theo country
        const countryToRegionsMap = {};

        Object.values(selectedItems).forEach(itemList => {
            itemList.forEach(item => {
                const regionId = typeof item === "object" ? item.id : item;
                const region = regionMap[regionId];
                if (!region) return;

                const country = region.region_level === "COUNTRY" ? region : getCountry(region);
                if (country?.country_code) {
                    if (!countryToRegionsMap[country.country_code]) {
                        countryToRegionsMap[country.country_code] = new Set();
                    }
                    countryToRegionsMap[country.country_code].add(region.region_id);
                }
            });
        });

        return Object.entries(countryToRegionsMap)
            .filter(([_, regionSet]) => regionSet.size > 0)
            .map(([countryCode]) => countryCode);
    }

    function renderDataFromAdgroup(data) {

    }



    return {
        updateValuSelectedLocation,
        handleExcecutiveTargetingToRequest,
        checkValidationTargeting,
        init,
        getCountryCodesFromSelected,
    };
})();
