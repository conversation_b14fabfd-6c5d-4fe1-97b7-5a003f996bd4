import {
  OUTCO<PERSON>_REACH,
  OUTCOME_TRAFFIC,
  OUTCOME_PRODUCT_SALES,
} from "/static/js/tiktok/constants/campaign.js";

export default (() => {
  let isInit = false;
  const ID_ADGOUP_FORM = "#adgroupForm";
  // const $optionPlacementByOutcomeObjective = $(
  //   "#optionPlacementByOutcomeObjective"
  // );

  const optionPlacementByOutcomeObjectiveID =
    "#optionPlacementByOutcomeObjective"
    ;
  const optionPlacementAdvancedSettingID = "#placementAdvancedSettingsContent";
  const $outcomeObjectiveEl = $("#outcomeObjective");
  const outcomeObjectiveID = "#outcomeObjective";
  /**
   * Outcome Objective OnChange
   */
  function outcomeObjectiveOnChange() {

    $(outcomeObjectiveID).on("change", function () {
      const outcomeObjective = $(this).val();
      if (outcomeObjective) {
        renderOptionByOutcomeObjectiveUI(outcomeObjective);
      }
    });
  }

  /**
   * Render the placement options based on the selected outcome objective.
   */
  function renderOptionByOutcomeObjectiveUI(outcomeObjective) {
    const $optionPlacementByOutcomeObjective = $(optionPlacementByOutcomeObjectiveID);
    const $optionPlacementAdvancedSetting = $(optionPlacementAdvancedSettingID);
    switch (outcomeObjective) {
      case OUTCOME_REACH:
        $optionPlacementByOutcomeObjective.empty().html($("#optionPlacementHtmlByREACH").html());
        $optionPlacementAdvancedSetting.empty().html($("#placementAdvancedSettingsContent_REACH").html());
        addEventForPlacement();
        break;
      case OUTCOME_TRAFFIC:
        $optionPlacementByOutcomeObjective
          .empty()
          .html($("#optionPlacementHtmlByTRAFFIC").html());
        $optionPlacementAdvancedSetting.empty().html($("#placementAdvancedSettingsContent_TRAFFIC").html());
        addEventForPlacement();
        break;
      case OUTCOME_PRODUCT_SALES:
        $optionPlacementByOutcomeObjective
          .empty()
          .html($("#optionPlacementHtmlBySALES").html());
        $optionPlacementAdvancedSetting.empty().html($("#placementAdvancedSettingsContent_PRODUCT_SALES").html());
        addEventForPlacement();
        break;
    }
  }

  function addEventForPlacement() {
    const $automacticPlacement = $("#automaticPlacement");
    const $manualPlacement = $("#manualPlacement");
    const $placementSelection = $("#placementSelection");

    $automacticPlacement.on("change", function () {
      if ($(this).is(":checked")) {
        $placementSelection.hide(150);
      } else {
        $placementSelection.show(200);
      }
    });

    $manualPlacement.on("change", function () {
      if ($(this).is(":checked")) {
        $placementSelection.show(200);
      } else {
        $placementSelection.hide(150);
      }
    });
  }

  /**
   * Initialize the placement UI.
   */
  function init() {
    outcomeObjectiveOnChange();
  }
  return {
    init,
  };
})();

