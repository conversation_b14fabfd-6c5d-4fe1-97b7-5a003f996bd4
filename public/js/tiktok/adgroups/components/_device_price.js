export default (() => {
    const selectors = {
        minPrice: "#minPrice",
        maxPrice: "#maxPrice",
        error: "#priceError",
        specificRange: "#specificRange",
        optionAny: "#optionPriceAny",
        optionSpecific: "#optionPriceSpecific"
    };

    function init() {
        const $minPrice = $(selectors.minPrice);
        const $maxPrice = $(selectors.maxPrice);
        const $error = $(selectors.error);
        const $range = $(selectors.specificRange);
        const $optionAny = $(selectors.optionAny);
        const $optionSpecific = $(selectors.optionSpecific);

        if (
            !$minPrice.length ||
            !$maxPrice.length ||
            !$error.length ||
            !$range.length ||
            !$optionAny.length ||
            !$optionSpecific.length
        ) {
            return; // Không đủ phần tử DOM
        }

        const minOptions = [0, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950, 1000];
        const maxOptions = [];
        for (let i = 50; i <= 1000; i += 50) maxOptions.push(i);
        maxOptions.push("1000+");

        function renderSelectOptions($select, options, defaultValue) {
            $select.empty();
            options.forEach((value) => {
                $select.append(`<option value="${value}" ${value == defaultValue ? "selected" : ""}>${value}</option>`);
            });
        }

        renderSelectOptions($minPrice, minOptions, 0);
        renderSelectOptions($maxPrice, maxOptions, 50);

        function validatePrices() {
            const minVal = parseInt($minPrice.val());
            const maxVal = $maxPrice.val() === "1000+" ? 1001 : parseInt($maxPrice.val());

            if (minVal >= maxVal) {
                $error.text("Please select a valid minimum price.").show();
                $minPrice[0].setCustomValidity("Invalid");
                $maxPrice[0].setCustomValidity("Invalid");
            } else {
                $error.text("").hide();
                $minPrice[0].setCustomValidity("");
                $maxPrice[0].setCustomValidity("");
            }
        }

        function onChangeTypePrice() {
            if ($(this).is(":checked") && $(this).val() === "specific") {
                $minPrice.val(0);
                $maxPrice.val(100);
                $range.show();
                $error.hide();
                validatePrices();
            } else {
                $range.hide();
                $error.hide();
                $minPrice[0].setCustomValidity("");
                $maxPrice[0].setCustomValidity("");
            }
        }

        $minPrice.on("change", validatePrices);
        $maxPrice.on("change", validatePrices);
        $optionAny.on("change", onChangeTypePrice);
        $optionSpecific.on("change", onChangeTypePrice);
    }

    return {
        init
    };
})();



