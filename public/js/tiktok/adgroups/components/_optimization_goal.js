import { OPTIMIZATION_GOAL } from "/static/js/tiktok/constants/adgroup.js";
import { OUTCOME_REACH, OUTCOME_TRAFFIC, OUTCOME_PRODUCT_SALES } from "/static/js/tiktok/constants/campaign.js";

export default (() => {
    let isInit = false;
    const $select2OptimizationGoal = $("#optimizationGoal");

    // console.log(data);
    // /**
    //  * Initialize Optimization Goal Select2
    //  */
    function renderOptimizationSelect2(data) {
        const objective = getObjectiveFromPromotionType(data.shopping_ads_type); // e.g., LIVE → OUTCOME_LIVE_SHOPPING
        const currentValue = data.optimization_goal;
        const filtered = OPTIMIZATION_GOAL.filter((i) => i.supported.includes(objective));

        $select2OptimizationGoal.empty();

        // Nếu chỉ có 1 lựa chọn
        if (filtered.length === 1) {
            $select2OptimizationGoal.prop("disabled", true);
        } else {
            $select2OptimizationGoal.prop("disabled", false);
        }

        // Set giá trị nếu có từ API
        if (currentValue) {
            $select2OptimizationGoal.val(currentValue);
        }

        $select2OptimizationGoal.select2({
            dropdownCssClass: "select2-container-optimization-goal tiktok-custom",
            data: filtered,
            minimumResultsForSearch: Infinity,
            templateResult: function (item) {
                if (item?.hidden) return null;

                return $(`
                <div class="form-check form-radio-primary select-option-radio">
                    <input
                        class="form-check-input"
                        type="radio"
                        name="optimization_goal"
                        value="${item.value}"
                        id="optimization_goal-${item.value}"
                        ${item.value === $select2OptimizationGoal.val() ? "checked" : ""}
                    />
                    <label class="form-check-label" for="optimization_goal-${item.value}">
                        ${item.title}<br/>
                        <i class="text-hint mt-2">${item.description}</i>
                    </label>
                </div>
            `);
            },
            templateSelection: function (item) {
                return item.title || item.text;
            },
        });

        if (currentValue) {
            $select2OptimizationGoal.val(currentValue).trigger("change");
        }
    }

    // /**
    //  *  Map shopping_ads_type to objective
    //  */
    function getObjectiveFromPromotionType(promotionType) {
        const map = {
            PRODUCT: OUTCOME_PRODUCT_SALES,
            LIVE: OUTCOME_LIVE_SHOPPING,
            VIDEO: OUTCOME_VIDEO_SHOPPING,
        };
        return map[promotionType] || OUTCOME_PRODUCT_SALES;
    }

    /**
     * Render the frequency cap UI based on the selected optimization goal.
     */
    function renderRequencyCapUI() {
        if ($select2OptimizationGoal.val() === OUTCOME_REACH) {
            $("#optionByOptimizationGoal").show().html($("#tiktokAdgroupFrequencyCapHtml").html());
            addEventForFrequencyCap();
        } else {
            $("#optionByOptimizationGoal").hide();
        }
    }

    function addEventForFrequencyCap() {
        $("input[name='frequency_cap']").on("change", function () {
            if ($(this).val() === "custom" && $(this).prop("checked")) {
                $("#frequencyCapInputControl").show(200);
            } else {
                $("#frequencyCapInputControl").hide(100);
            }
        });
    }

    function init() {

        if (!isInit) {
            isInit = true;

            renderOptimizationSelect2();
        }

        renderRequencyCapUI();
    }

    return {
        init,
    };
})();
