import { O<PERSON><PERSON><PERSON>_REACH, <PERSON>UTCO<PERSON>_TRAFFIC, OUTCOME_VIDEO_VIEWS } from "/static/js/tiktok/constants/campaign.js";

export const ADGROUP_OPERATION_STATUS_OFF = "DISABLE",
    ADGROUP_OPERATION_STATUS_ON = "ENABLE";

export const OPTIMIZATION_LOCATION_WEBSITE = "WEBSITE",
    OPTIMIZATION_LOCATION_APP = "APP",
    OPTIMIZATION_LOCATION_WEBSITE_LEAD = "WEBSITE_LEAD",
    OPTIMIZATION_LOCATION_INSTANT_FORM = "INSTANT_FORM",
    OPTIMIZATION_LOCATION_TIKTOK_DIRECT_MESSAGES = "TIKTOK_DIRECT_MESSAGES",
    OPTIMIZATION_LOCATION_INSTANT_MESSAGING_APPS = "INSTANT_MESSAGING_APPS";

export const ADGROUP_BUDGET_MODE_DAY = "BUDGET_MODE_DAY",
    ADGROUP_BUDGET_MODE_TOTAL = "BUDGET_MODE_TOTAL",
    ADGROUP_BUDGET_MODE_INFINITE = "BUDGET_MODE_INFINITE",
    ADGROUP_BUDGET_MODE_DYNAMIC_DAILY_BUDGET = "BUDGET_MODE_DYNAMIC_DAILY_BUDGET";
export const ADGROUP_BUDGET_TITLE = {
    [ADGROUP_BUDGET_MODE_DAY]: "Daily",
    [ADGROUP_BUDGET_MODE_TOTAL]: "Total",
    [ADGROUP_BUDGET_MODE_INFINITE]: "Unlimited",
    [ADGROUP_BUDGET_MODE_DYNAMIC_DAILY_BUDGET]: "Daily, Campaign budget optimization ",
};

export const OPTIMIZATION_GOAL = [
    {
        title: "Reach",
        value: "REACH",
        disabled: false,
        description: "Show your ad to the maximum number of people.",
        status: true,
        id: "REACH",
        supported: ["OUTCOME_REACH"],
    },
    {
        title: "Click",
        value: "CLICK",
        disabled: false,
        description: "Deliver your ads to the people most likely to click.",
        status: true,
        id: "CLICK",
        supported: ["OUTCOME_TRAFFIC", "OUTCOME_PRODUCT_SALES", "OUTCOME_VIDEO_SHOPPING", "OUTCOME_LIVE_SHOPPING"],
    },
    {
        title: "Landing Page View",
        value: "TRAFFIC_LANDING_PAGE_VIEW",
        disabled: false,
        description: "Optimize for people who are most likely to load your landing page.",
        status: true,
        id: "TRAFFIC_LANDING_PAGE_VIEW",
        supported: ["OUTCOME_TRAFFIC"],
    },
    {
        title: "Conversion (Initiate Checkout)",
        value: "CONVERT",
        disabled: false,
        description: "Optimize for people likely to initiate checkout.",
        status: true,
        id: "CONVERT_INITIATE_ORDER",
        supported: ["OUTCOME_PRODUCT_SALES", "OUTCOME_VIDEO_SHOPPING", "OUTCOME_LIVE_SHOPPING"],
    },
    {
        title: "Conversion (Purchase)",
        value: "CONVERT",
        disabled: false,
        description: "Optimize for people likely to purchase.",
        status: true,
        id: "CONVERT_PURCHASE",
        supported: ["OUTCOME_PRODUCT_SALES", "OUTCOME_VIDEO_SHOPPING", "OUTCOME_LIVE_SHOPPING"],
    },
    {
        title: "Value (Gross Revenue)",
        value: "VALUE",
        disabled: false,
        description: "Optimize for people likely to generate the most revenue.",
        status: true,
        id: "VALUE",
        supported: ["OUTCOME_PRODUCT_SALES", "OUTCOME_VIDEO_SHOPPING", "OUTCOME_LIVE_SHOPPING"],
    },
    {
        title: "6-second views (Focused View)",
        value: "ENGAGED_VIEW",
        disabled: false,
        description: "Optimize for 6-second views or early ad engagement.",
        status: true,
        id: "ENGAGED_VIEW",
        supported: ["OUTCOME_VIDEO_VIEWS"],
    },
    {
        title: "15-second views (Focused View)",
        value: "ENGAGED_VIEW_FIFTEEN",
        disabled: false,
        description: "Optimize for 15-second views or early ad engagement (max campaign duration: 30 days).",
        status: true,
        id: "ENGAGED_VIEW_FIFTEEN",
        supported: ["OUTCOME_VIDEO_VIEWS"],
    },
    {
        title: "Follower Growth",
        value: "FOLLOWERS",
        disabled: false,
        description: "Grow your TikTok followers by targeting engaged users.",
        status: true,
        id: "FOLLOWERS",
        supported: ["OUTCOME_ENGAGEMENT"],
    },
    {
        title: "Profile Visits",
        value: "PAGE_VISIT",
        disabled: false,
        description: "Drive traffic to your TikTok profile or hashtag page.",
        status: true,
        id: "PAGE_VISIT",
        supported: ["OUTCOME_ENGAGEMENT"],
    },
    {
        title: "Live Retention",
        value: "MT_LIVE_ROOM",
        disabled: false,
        description: "Optimize for keeping viewers longer during livestreams.",
        status: true,
        id: "MT_LIVE_ROOM",
        supported: ["OUTCOME_LIVE_SHOPPING"],
    },
    {
        title: "Product Clicks in Live",
        value: "PRODUCT_CLICK_IN_LIVE",
        disabled: false,
        description: "Optimize for product interactions during livestreams.",
        status: true,
        id: "PRODUCT_CLICK_IN_LIVE",
        supported: ["OUTCOME_LIVE_SHOPPING"],
    },
    {
        title: "Initiate Conversation",
        value: "CONVERSATION",
        disabled: false,
        description: "Get people to start conversations with your brand via message.",
        status: true,
        id: "CONVERSATION",
        supported: ["OUTCOME_CONVERSATION"],
    },
    {
        title: "Preferred Leads",
        value: "PREFERRED_LEAD",
        disabled: false,
        description: "Optimize for high-quality leads likely to convert.",
        status: true,
        id: "PREFERRED_LEAD",
        supported: ["OUTCOME_LEADS"],
    },
];
