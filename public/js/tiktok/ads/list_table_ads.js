import { getCookie } from "/static/js/common/helpers.js";
import { TIKTOK_API_URL } from "/static/js/common/httpService.js";
import { PATHS } from "/static/js/tiktok/constants/routes.js";

import { onUpdateSelectedRowTableToURL, onDefaultTable, onSelectAllRow, getCustomColumn } from "/static/js/tiktok/list-table-ads/helper.js";
import { defaultConfigTableTiktok, HandlerError, getHeaderTable, renderFooterTableByMetric, getCustomFieldTable } from "/static/js/tiktok/list-table-ads/config-table.js";
import { AD_OPERATION_STATUS_ON } from "/static/js/tiktok/ads/off-canvas/constant.js";
import { STATUS_ENUMS, ENTITY_TYPES } from "/static/js/tiktok/constants/status.js";
import { DATETIME_FORMAT_TYPES } from "/static/js/constants/common-constant.js";
// import { customFields } from "/static/js/tiktok/ads/ad_config_table.js";

export default (() => {
    // export function CampaignList() {
    // const htmlSelectedCamp = $("#teamplate_camp_btn_selected").html();
    let isFristLoad = true;
    const nameAdgroupSelected = $("#selected_adgroup_name");
    const nameAdSeltected = $("#selected_ad_name");
    const ID_TABLE = "data-table-ad";
    const tableSelector = "table#data-table-ad";
    const tableElRef = $(tableSelector);
    let campaignIdSelected = [];
    let adgroupIdSelected = [];
    const headerTable = ["Off/On", "Name", "Status"];
    const eventManager = {
        renderFooter: null,
    };
    let customFields = [];
    let datatable;
    const filterData = {
        filter: { search: "", page: 1, start: 0, time: null, ad_id: "", campaign_ids: "", adgroup_ids: "", nameUser: "", userId: "", advertiserId: "", clientId: "" },
    };
    // const filterData = { filter: { search: "", page: 1, start: 0, time: null, campaignId: "", nameUser: "", userId: "", advertiser_id: "7218127051814092801", clientId: "" } };

    /**
     * Init Table p
     */
    const initTable = function () {
        tableElRef.html(getHeaderTable(headerTable));
        datatable = tableElRef.DataTable({
            ...defaultConfigTableTiktok({ displayStart: filterData.filter.start }, "Ads"),
            createdRow: function (row, data, dataIndex) {
                $(row).attr("campaign-id", `${data?.campaign_id}`);
                $(row).attr("adset-id", `${data?.adgroup_id}`);
                $(row).attr("id", `${data?.ad_id}`);
            },
            columns: [
                {
                    data: null,
                    orderable: false,
                    searchable: false,
                    width: "20px",
                    className: "align-middle",
                    render: function (data, type, row) {
                        return `<div class="form-check" >
                                    <input class="form-check-input row-select" type="checkbox" name="chk_child" value="${row.ad_id}">
                                </div>`;
                    },
                },
                {
                    orderable: false,
                    width: "30px",
                    className: "align-middle",
                    data: function (row) {
                        return `
                        <div class="form-check form-switch form-check-inline" dir="ltr">
                            <input
                                type="checkbox"
                                class="form-check-input form-switch-md"
                                id="switch-check-${row.ad_id}"
                                ${row.operation_status === AD_OPERATION_STATUS_ON ? "checked" : ""} />
                        </div>
                    `;
                    },
                },
                {
                    title: "Name",
                    name: "ad_name",
                    render: function (data, type, row) {
                        return `
                        <div class="name-blk ">
                            <a href="#" class="text-nowrap-3" title="${row.ad_name}">${row.ad_name}</a>
                            <div class="action-blk">
                                <button class="btn p-0 me-1" hidden><i class="ri-bar-chart-box-line"></i> Charts</button>                         
                                <button
                                    class="btn p-0 me-1 btn-edit-ad btn-small"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#OffCanvasCampAdgroupAd"
                                    aria-controls="OffCanvasCampAdgroupAd"><i class="ri-pencil-fill"></i> Edit</button>                         
                                 <button class="btn p-0 me-1" hidden><i class="ri-file-copy-2-fill"></i> Duplicate</button>                         
                                 <button class="btn p-0 me-1" hidden><i class="ri-pushpin-fill"></i> Pin</button>                         
                            </div>
                        </div>
                    `;
                    },
                },
                {
                    title: "Status",
                    name: "secondary_status",
                    className: "align-middle",
                    render: function (data, type, row) {
                        const status = STATUS_ENUMS[ENTITY_TYPES.AD]?.[row.secondary_status];
                        if (status) {
                            return `<i class="${status.iconClass + " " + status.textColorClass} align-middle"></i> ${status.text}`;
                        }
                        return `<i class="ri-question-circle-fill text-warning align-middle"></i>  row.secondary_status}`;
                    },
                },
                ...getCustomColumn(customFields, "AD"),
            ],
            ajax: onAjaxTable,
        });

        initAddEventForTable();

        // if (isFristLoad) {
        // }

        tableElRef.off("preDraw.dt", eventManager.renderFooter);
        eventManager.renderFooter = renderFooterTableByMetric(tableElRef, datatable, "ads", customFields, "AD");
    };

    async function getQueryParamAsArray(key = "selected_campaign_ids", params) {
        if (!key) {
            throw new Error("Query parameter key is required.");
        }

        // Check for the parameter in the passed `params` object
        if (params?.params?.[key]) {
            return params.params[key].split(",");
        }

        // Fallback to the current URL
        const url = new URL(window.location.href);
        const paramValue = url.searchParams.get(key);

        return paramValue ? paramValue.split(",") : [];
    }

    /**
     * Get campaignIdsSelected form URL
     */
    function getCampaignIdsSelected(params) {
        if (!params) {
            const url = new URL(window.location.href);
            const campIds = url.searchParams.get("selected_campaign_ids");
            if (campIds) {
                return campIds.split(",");
            } else return [];
        }
        let ids = [];
        if (params?.params?.selected_campaign_ids) {
            ids = params.params.selected_campaign_ids.split(",");
        }
        return ids;
    }

    /**
     * Get adGroupIdsSelected form URL
     */
    function getAdgroupIdsSelected(params) {
        if (!params) {
            const url = new URL(window.location.href);
            const adgroupIds = url.searchParams.get("selected_adgroup_ids");

            if (adgroupIds) {
                return adgroupIds.split(",");
            } else return [];
        }
        let ids = [];
        if (params?.params?.selected_adgroup_ids) {
            ids = params.params.selected_adgroup_ids.split(",");
        }
        return ids;
    }
    /**
     * Update filter table
     */
    function updateFilterTable() {
        let currentPage = getParamURL("page");
        filterData.filter.page = currentPage || 1;
        currentPage = currentPage ? parseInt(currentPage, 10) : 1;
        filterData.filter.start = (currentPage - 1) * 10;

        const datePicker = $("#filter-search-datetime").data("daterangepicker");
        const start = datePicker.startDate.format(DATETIME_FORMAT_TYPES.UTC);
        const end = datePicker.endDate.format(DATETIME_FORMAT_TYPES.UTC);

        filterData.filter.time = [start, end];

        filterData.filter.campaign_ids = getCampaignIdsSelected();
        filterData.filter.adgroup_ids = getAdgroupIdsSelected();
    }

    /**
     * Ajax config
     */
    function onAjaxTable(data, callback) {
        // const userInfo = getUserInfo();
        // if (isFristLoad && userInfo?.role_name == "ADMIN") {
        //     isFristLoad = false;
        //     return callback({
        //         data: [],
        //         recordsTotal: 0,
        //         recordsFiltered: 0,
        //     });
        // }

        updateFilterTable();
        $.ajax({
            url: TIKTOK_API_URL.ADS,
            type: "POST",
            contentType: "application/x-www-form-urlencoded",
            headers: {
                "X-CSRF-Token": getCookie("csrf_"), // Hàm lấy CSRF token
            },
            data: {
                ...data,
                ...filterData.filter,
            },
            success: function (response) {
                callback({
                    data: response.data,
                    recordsTotal: response.recordsTotal, // Tổng số bản ghi
                    recordsFiltered: response.recordsFiltered, // Số bản ghi sau khi filter
                });
            },
            error: function (xhr, status, error) {
                HandlerError(xhr, status, error, ID_TABLE);
            },
        });
    }

    /**
     * Init Add Event For Table
     */
    function initAddEventForTable() {
        onDefaultTable(tableElRef, datatable, "selected_ad_ids", onSelectRowChange);

        // On Select All
        onSelectAllRow(tableElRef, datatable, onSelectRowChange);

        /**
         * Edit ads
         */
        tableElRef.on("click", "tr td button.btn-edit-ad", function () {
            const tr = $(this).closest("tr");
            const id = tr.attr("id");
            const campId = tr.attr("campaign-id");
            const adsetId = tr.attr("adset-id");
            removeParamURL("edit_campaign_id");
            removeParamURL("edit_adgroup_id");
            removeParamURL("edit_ad_id");
            window.offcanvasOriginURL = window.location.pathname + window.location.search;

            if (id) {
                const router = new Navigo("/");
                router.navigate(`${PATHS.ad.edit}?edit_ad_id=${id.replace("row_", "")}&${getCurrentQuery()}`);
            }
        });
    }

    /**
     * On Select Row Change
     */
    const onSelectRowChange = () => {
        const nameAdSeltected = $("#selected_ad_name");
        const AdSelected = $("#ad_selected");
        const selects = datatable.rows({ selected: true }).data().toArray();
        onUpdateSelectedRowTableToURL(selects, "selected_ad_ids", "ad_id");
        if (!selects || selects.length === 0) {
            nameAdSeltected.text("Ads");
            return AdSelected.addClass("d-none");
        }
        AdSelected.contents()
            .filter(function () {
                return this.nodeType === Node.TEXT_NODE && this.nodeValue.trim() !== "";
            })
            .first()
            .replaceWith(selects.length + " selected");

        nameAdSeltected.text(`Ads for  ${selects.length} Ads`);
        AdSelected.removeClass("d-none");
    };

    /**
     * On Close Campaign select
     */
    const onCloseSelected = () => {
        $("#ad_selected #ad-selected-close").on("click", () => {
            datatable.rows({ page: "current" }).deselect();
            datatable.$("input.row-select").prop("checked", false); // Uncheck checkboxes
        });
    };

    /**
     * On Change select user filter
     */
    const onSelectUserFilter = (data) => {
        filterData.filter.clientId = data?.clientId || "";
        filterData.filter.advertiserId = data?.advertiserId || "";

        if (!datatable) return;
        datatable.ajax.reload();
    };

    /**
     * Is Campaign Selected Change
     */
    function compareArrays(a, b) {
        if (a.length !== b.length) {
            return true;
        }
        for (let i = 0; i < a.length; i++) {
            if (a[i] !== b[i]) {
                return true;
            }
        }
        return false;
    }

    /**
     * Reload Data table
     */
    const onActiveCampaignTab = () => {
        document.addEventListener("adTabActive", async (event) => {
            if (event.detail.init) {
                if (event.detail.isReloadPage) {
                    updateFilterTable();
                } else {
                    filterData.filter.page = 1;
                    filterData.filter.start = 0;
                    setParamURL("page", 1);
                    setTimeout(() => setParamURL("page", 1), 50);
                }
                initAd();
            } else {
                const ids = await getQueryParamAsArray("selected_campaign_ids", event.detail.params);
                const adGroupIds = await getQueryParamAsArray("selected_adgroup_ids", event.detail.params);
                if (compareArrays(ids, campaignIdSelected) || compareArrays(adGroupIds, adgroupIdSelected)) {
                    campaignIdSelected = ids;
                    adgroupIdSelected = adGroupIds;
                    datatable.rows({ page: "current" }).deselect();
                    datatable.$("input.row-select").prop("checked", false);
                    // datatable.ajax.reload(function () {
                    //     // tableElRef.find("tbody tr").removeClass("selected");
                    //     // onSelectRowChange();
                    //     onCloseSelected();
                    // }, false);
                    setTimeout(() => {
                        // onDefaultTable(tableElRef, datatable, "selected_ad_ids", onSelectRowChange);
                        removeParamURL("selected_ad_ids");
                        datatable.ajax.reload();
                    });
                }
                setTimeout(() => {
                    const page = datatable.page.info();
                    setParamURL("page", page.page + 1);
                });
            }
        });
    };

    /**
     * Filter table change
     */
    const initOnChangeFilterTable = (event) => {
        document.addEventListener("onUpdateFilterChange", function (event) {
            if ($(".tab-pane.active#ad").length > 0) {
                const { start, end, search, id } = event.detail;
                filterData.filter.time = [start, end];
                if (!datatable) return;

                if (!!start && !!end && $('#navTabTableAds li > a[data-tab="ads"].active').length > 0) {
                    datatable.ajax.reload();
                }

                if (search || search === "") {
                    filterData.filter.search = search;
                    filterData.filter.ad_id = "";
                    datatable.ajax.reload();
                }
                if (id) {
                    filterData.filter.search = "";
                    filterData.filter.ad_id = id;
                    datatable.ajax.reload();
                }
            }
        });
    };

    /**
     * Change config column table
     */
    async function inChangeConfigColumnTable() {
        document.addEventListener("onChangeConfigColumnTable", async function (event) {
            if ($(".tab-pane.active#ad").length > 0) {
                datatable.destroy();
                $(`#${ID_TABLE}`).empty();
                customFields = await getCustomFieldTable();
                initTable();
            }
        });
    }

    async function initAd() {
        customFields = await getCustomFieldTable();
        initTable();
        onCloseSelected();
    }

    function init() {
        isFristLoad = false;
        inChangeConfigColumnTable();
        onActiveCampaignTab();
        initOnChangeFilterTable();
    }

    $().ready(function () {
        init();
    });

    return {
        onSelectUserFilter,
        datatable,
    };
    // }
})();
