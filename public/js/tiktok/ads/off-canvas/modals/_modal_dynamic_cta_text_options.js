import { TEXT_OPTIONS } from "/static/js/tiktok/ads/off-canvas/constant.js"
export default (() => {
    let isInitializedModal = false;
    const modalDynamicCta = $("#modal_dynamic_cta_text_options");
    const modalDynamicCtaTextList = modalDynamicCta.find(".modal-dynamic-cta__text-options .list-group");

    function renderTextOptionList() {
        modalDynamicCtaTextList.empty();

        TEXT_OPTIONS.forEach(opt => {
            const $label = $(`
                <label class="list-group-item form-check-success">
                    <input class="form-check-input checkbox-success me-1" type="checkbox" value="${opt.value}" ${opt.checked ? 'checked' : ''}>
                    ${opt.label}
                </label>
            `);
            modalDynamicCtaTextList.append($label);
        });
    }

    function init() {
        if (!isInitializedModal) {
            isInitializedModal = true;
            renderTextOptionList();
        }
    }

    return {
        init
    }
})()