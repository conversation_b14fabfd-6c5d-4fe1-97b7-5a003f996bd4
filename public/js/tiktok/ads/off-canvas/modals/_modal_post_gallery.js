export default (() => {
    const postGalleryModal = $("#modal_post_gallery");
    const postGalleryAccountInfo = postGalleryModal.find(".modal_post_gallery_account_info");
    const postGalleryContainer = postGalleryModal.find(".modal_post_gallery_gallery_container");
    const postGallery = postGalleryModal.find(".gallery");

    const postGalleryCounter = postGalleryModal.find(".gallery-selected");
    const postGalleryTotalPost = postGalleryModal.find(".gallery-total");

    let isInitializedPostGallery = false;
    const customData = [
        {
            id: "1",
            name: "<PERSON><PERSON><PERSON> v<PERSON>y hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "2",
            name: "<PERSON><PERSON><PERSON> v<PERSON><PERSON> hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "3",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "4",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "5",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "6",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "7",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "8",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "9",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "10",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "11",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "12",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "13",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "14",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "15",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "16",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "17",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
        {
            id: "18",
            name: "Chiếc váy hem thể thiếu cho dịp 20/10 lần này🥰🥰 #naryclothing #trendy #damlua ",
            length: 18,
            height: 1920,
            width: 1080,
            image: "/static/images/tiktok/test_video_thumbnail.png",
            video: "/static/images/tiktok/test_video.mp4",
        },
    ];

    function formatDuration(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${String(mins).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
    }

    function truncateText(text, maxLength) {
        return text.length > maxLength ? text.slice(0, maxLength - 1) + "…" : text;
    }

    function handleUpdateCountAndTotal() {
        const total = customData.length;
        const count = postGalleryModal.find('[name="selectedItems"]:checked').length;

        postGalleryCounter.text(count);
        postGalleryTotalPost.text(total);
    }

    function renderPostGallery() {
        postGallery.empty();

        customData.forEach((item) => {
            const html = `
              <div class="gallery-item" data-id="${item.id}">
              <label class="w-100">
                <div class="gallery-item-thumb position-relative">
                  <img src="${item.image}" alt="${item.name}" class="gallery-image">
                  <video class="gallery-video" muted preload="metadata" style="display: none;">
                      <source src="${item.video}" type="video/mp4">
                  </video>
                  <div class="gallery-item-meta">
                    <span class="resolution">${item.width}x${item.height}</span>
                    <span class="duration">${formatDuration(item.length)}</span>
                  </div>
                  <div class="gallery-item-checkbox">
                    <input type="checkbox" name="selectedItems" id="${item.id}" />
                  </div>
                </div>
                <div class="gallery-item-name" title="${item.name}">${truncateText(item.name, 30)}</div>
              </label>
            </div>
              `;
            postGallery.append(html);
        });

        handleUpdateCountAndTotal();
    }

    function onSelectPost() {
        postGalleryModal.on("change", '[name="selectedItems"]', function (e) {
            handleUpdateCountAndTotal();
        });
    }

    function initPostGallery() {
        $(document).on("show.bs.modal", "#modal_post_gallery", function () {
            renderPostGallery();
        });
    }

    function onInteractAPost() {
        const hoverTimers = {};
        const activeItems = new Set();

        postGalleryModal.on("mouseenter", ".gallery-item", function () {
            const $item = $(this);
            const postId = $item.data("id");

            if (activeItems.has(postId)) return;

            hoverTimers[postId] = setTimeout(() => {
                handleHoverPost($item);
                activeItems.add(postId);
            }, 500);
        });

        postGalleryModal.on("mouseleave", ".gallery-item", function () {
            const $item = $(this);
            const postId = $item.data("id");

            if (hoverTimers[postId]) {
                clearTimeout(hoverTimers[postId]);
                delete hoverTimers[postId];
            }

            if (activeItems.has(postId)) {
                handleLeavePost($item);
                activeItems.delete(postId);
            }
        });
    }

    function handleHoverPost($item) {
        const $video = $item.find("video.gallery-video").get(0);
        const $img = $item.find("img.gallery-image");

        if ($video) {
            $img.hide();
            $video.style.display = "block";
            $video.currentTime = 0;
            $video.play();
        }
    }

    function handleLeavePost($item) {
        const $video = $item.find("video.gallery-video").get(0);
        const $img = $item.find("img.gallery-image");

        if ($video) {
            $video.pause();
            $video.style.display = "none";
            $img.show();
        }
    }

    function init() {
        if (!isInitializedPostGallery) {
            initPostGallery();
            onSelectPost();
            onInteractAPost();
            isInitializedPostGallery = true;
        }
    }
    return {
        init,
    };
})();
