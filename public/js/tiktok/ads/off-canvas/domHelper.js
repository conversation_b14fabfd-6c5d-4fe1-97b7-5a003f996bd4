export function getAdConfigFromRuleConfig(ruleConfig) {
    const adForm = $("#ad-form");
    const outcomeObjective = adForm.find("#outcomeObjective").val();
    const shopAdsType = adForm.find("#shopAdsType").val();
    const optimizationLocation = adForm.find("#optimizationLocation").val();

    if (!ruleConfig || !outcomeObjective) return undefined;

    const outcomeRule = ruleConfig[outcomeObjective];
    if (!outcomeRule) return undefined;

    // Ưu tiên SHOP_ADS_TYPE nếu có
    if (shopAdsType && outcomeRule.SHOP_ADS_TYPE?.[shopAdsType]) {
        return outcomeRule.SHOP_ADS_TYPE[shopAdsType];
    }

    // Tiếp theo OPTIMIZATION_LOCATION nếu có
    if (optimizationLocation && outcomeRule.OPTIMIZATION_LOCATION?.[optimizationLocation]) {
        return outcomeRule.OPTIMIZATION_LOCATION[optimizationLocation];
    }

    // Nếu rule ở cấp outcome
    return outcomeRule;
}

export function showIfValid($el, condition) {
    if (condition) {
        $el.show(220);
    } else {
        $el.hide(220);
    }
}

export function showLabelsByIds(ids, selectorPrefix = '') {
    const $labels = $("#ad-form").find(".ad_component_ad_details").find(`${selectorPrefix} label`);
    $labels.hide();
    ids.forEach(id => $("#ad-form").find(".ad_component_ad_details").find(`label[for="${id}"]`).show());
}
