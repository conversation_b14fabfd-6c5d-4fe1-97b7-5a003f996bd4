import { success<PERSON>lert, errorAlert, loader } from "/static/js/common/helpers.js";
import adNameModule from "/static/js/tiktok/ads/off-canvas/components/_ad_name.js";
import identityModule from "/static/js/tiktok/ads/off-canvas/components/_identity.js";
import adDetailsModule from "/static/js/tiktok/ads/off-canvas/components/_ad_details.js";
import postGalleryModule from "/static/js/tiktok/ads/off-canvas/modals/_modal_post_gallery.js";
import dynamicCtaModule from "/static/js/tiktok/ads/off-canvas/modals/_modal_dynamic_cta_text_options.js";
import trackingModule from "/static/js/tiktok/ads/off-canvas/components/_tracking.js";
import addCreativeModule from "/static/js/tiktok/ads/off-canvas/modals/_modal_add_creative.js";
import productDetailsModule from "/static/js/tiktok/ads/off-canvas/components/_product_details.js";
import policyNotificationModule from "/static/js/tiktok/ads/off-canvas/components/_policy_notification.js";
import adPreviewModule from "/static/js/tiktok/ads/off-canvas/components/_ad_preview.js";

export function initAdOffCanvasCpn({ ad = {} }) {
    adDetailsModule.initAdOffCanvas({ ad });
    identityModule.initAdOffCanvas({ ad });
}

document.addEventListener("DOMContentLoaded", () => {
    addCreativeModule.init();
    postGalleryModule.init();
    dynamicCtaModule.init();
});

export async function renderAdOffCanvasContent() {
    try {
        loader();
        const adId = getParamURL("edit_ad_id");
        if (!adId) throw new Error("Thiếu ad_id");

        // Fetch ad content
        const adResponse = await fetch("/dsp/tiktok/api/ad/edit", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            body: JSON.stringify({ ad_id: adId }),
        });
        if (!adResponse.ok) throw new Error("Lỗi khi fetch ad");

        const adRes = await adResponse.json();
        document.getElementById("tiktok-offcanvas-content").innerHTML = adRes.html;
        initAdOffCanvasCpn({ ad: adRes.data });
        console.log("adRes.left_menu: ", adRes.left_menu);
        const sidebar = document.getElementById("menu-camp-adgroup-ad-left-side-bar");
        console.log("🚀 ~ renderAdOffCanvasContent ~ sidebar:", sidebar)
        if (sidebar) sidebar.innerHTML = adRes.left_menu;
    } catch (error) {
        console.error("Không thể tải dữ liệu:", error);
    } finally {
        loader(false);
    }
}
