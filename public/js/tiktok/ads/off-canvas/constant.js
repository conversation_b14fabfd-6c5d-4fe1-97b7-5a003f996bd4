export const CTA_OPTIONS = {
    LEARN_MORE: {
        value: "LEARN_MORE",
        text: "Learn more",
    },
    DOWNLOAD_NOW: {
        value: "DOWNLOAD_NOW",
        text: "Download",
    },
    SHOP_NOW: {
        value: "SHOP_NOW",
        text: "Shop now",
    },
    SIGN_UP: {
        value: "SIGN_UP",
        text: "Sign up",
    },
    CONTACT_US: {
        value: "CONTACT_US",
        text: "Contact us",
    },
    APPLY_NOW: {
        value: "APPLY_NOW",
        text: "Apply now",
    },
    BOOK_NOW: {
        value: "BOOK_NOW",
        text: "Book now",
    },
    PLAY_GAME_CTA: {
        value: "PLAY_GAME_CTA",
        text: "Play game",
    },
    WATCH_NOW: {
        value: "WATCH_NOW",
        text: "Watch now",
    },
    READ_MORE: {
        value: "READ_MORE",
        text: "Read more",
    },
    VIEW_NOW: {
        value: "VIEW_NOW",
        text: "View now",
    },
    GET_QUOTE: {
        value: "GET_QUOTE",
        text: "Get quote",
    },
    ORDER_NOW: {
        value: "ORDER_NOW",
        text: "Order now",
    },
    INSTALL_NOW: {
        value: "INSTALL_NOW",
        text: "Install now",
    },
    GET_SHOWTIMES: {
        value: "GET_SHOWTIMES",
        text: "Get showtimes",
    },
    LISTEN_NOW: {
        value: "LISTEN_NOW",
        text: "Listen now",
    },
    INTERESTED: {
        value: "INTERESTED",
        text: "Interested",
    },
    SUBSCRIBE: {
        value: "SUBSCRIBE",
        text: "Subscribe",
    },
    GET_TICKETS_NOW: {
        value: "GET_TICKETS_NOW",
        text: "Get tickets now",
    },
    EXPERIENCE_NOW: {
        value: "EXPERIENCE_NOW",
        text: "Experience now",
    },
    PREORDER_NOW: {
        value: "PREORDER_NOW",
        text: "Pre-order now",
    },
    VISIT_STORE: {
        value: "VISIT_STORE",
        text: "Visit store",
    },
};

export const TEXT_OPTIONS = [
    { value: "interested", label: "Interested", checked: true },
    { value: "visit_store", label: "Visit store", checked: true },
    { value: "watch_now", label: "Watch now", checked: true },
    { value: "sign_up", label: "Sign up", checked: true },
    { value: "listen_now", label: "Listen now", checked: true },
    { value: "order_now", label: "Order now", checked: true },
    { value: "book_up", label: "Book up", checked: true },
    { value: "shop_now", label: "Shop now", checked: true },
    { value: "play_game", label: "Play game", checked: false },
    { value: "learn_more", label: "Learn more", checked: false },
    { value: "download", label: "Download", checked: false },
    { value: "contact_us", label: "Contact us", checked: false },
    { value: "apply_now", label: "Apply now", checked: false },
    { value: "read_more", label: "Read more", checked: false },
    { value: "view_now", label: "View now", checked: false },
    { value: "get_quote", label: "Get quote", checked: false },
    { value: "install_now", label: "Install now", checked: false },
    { value: "get_showtimes", label: "Get showtimes", checked: false },
    { value: "subscribe", label: "Subscribe", checked: false },
    { value: "get_tickets_now", label: "Get tickets now", checked: false },
    { value: "experience_now", label: "Experience now", checked: false },
    { value: "preorder_now", label: "Pre-order now", checked: false },
];

//OBJECTIVE
export const OUTCOME_REACH = "REACH",
    OUTCOME_CLICK = "CLICK",
    OUTCOME_COMMUNITY_INTERACTION = "COMMUNITY_INTERACTION",
    OUTCOME_APP_PROMOTION = "APP_PROMOTION",
    OUTCOME_LEAD_GENERATION = "LEAD_GENERATION",
    OUTCOME_VIDEO_VIEWS = "VIDEO_VIEWS",
    OUTCOME_TRAFFIC = "TRAFFIC",
    OUTCOME_SALES = "PRODUCT_SALES";

//SHOP ADS TYPE
export const VIDEO_SHOPPING_ADS = "VIDEO",
    LIVE_SHOPPING_ADS = "LIVE",
    PRODUCT_SHOPPING_ADS = "PRODUCT_SHOPPING_ADS";

//OPTIMIZATION_LOCATION
export const OPTIMIZATION_LOCATION_WEBSITE = "WEBSITE",
    OPTIMIZATION_LOCATION_APP = "APP";

export const AD_IDENTITY_RULE = {
    [OUTCOME_SALES]: {
        SHOP_ADS_TYPE: {
            [VIDEO_SHOPPING_ADS]: {
                visible: false,
            },
            [LIVE_SHOPPING_ADS]: {
                visible: false,
            },
            [PRODUCT_SHOPPING_ADS]: {
                visible: false,
            },
        },
    },
    [OUTCOME_VIDEO_VIEWS]: {
        visible: true,
    },
    [OUTCOME_TRAFFIC]: {
        OPTIMIZATION_LOCATION: {
            [OPTIMIZATION_LOCATION_WEBSITE]: {
                visible: false,
            },
            [OPTIMIZATION_LOCATION_APP]: {
                visible: false,
            },
        },
    },
    [OUTCOME_REACH]: {
        visible: true,
    },
    [OUTCOME_CLICK]: {},
    [OUTCOME_COMMUNITY_INTERACTION]: {},
    [OUTCOME_APP_PROMOTION]: {},
    [OUTCOME_LEAD_GENERATION]: {},
};

export const AD_PRODUCT_DETAILS_RULE = {
    [OUTCOME_SALES]: {
        SHOP_ADS_TYPE: {
            [VIDEO_SHOPPING_ADS]: {
                visible: false,
            },
            [LIVE_SHOPPING_ADS]: {
                visible: false,
            },
            [PRODUCT_SHOPPING_ADS]: {
                visible: true,
            },
        },
    },
    [OUTCOME_VIDEO_VIEWS]: {
        visible: false,
    },
    [OUTCOME_TRAFFIC]: {
        OPTIMIZATION_LOCATION: {
            [OPTIMIZATION_LOCATION_WEBSITE]: {
                visible: false,
            },
            [OPTIMIZATION_LOCATION_APP]: {
                visible: false,
            },
        },
    },
    [OUTCOME_REACH]: {
        visible: false,
    },
    [OUTCOME_CLICK]: {},
    [OUTCOME_COMMUNITY_INTERACTION]: {},
    [OUTCOME_APP_PROMOTION]: {},
    [OUTCOME_LEAD_GENERATION]: {},
};

//AD_FORMATS

export const AD_FORMATS_SINGLE_VIDEO = "ad-format-single-video",
    AD_FORMATS_CAROUSEL_IMAGE = "ad-format-carousel-images",
    AD_FORMATS_REALTIME_LIVE = "ad-format-realtime-live";

export const AD_DETAILS_VIDEO_SHOPPING = "ad-details-video-shopping",
    AD_DETAILS_LIVE_SHOPPNG = "ad-details-live-shopping",
    AD_DETAILS_PRODUCT_SET = "ad-details-product-set",
    AD_DETAILS_SETUP = "ad-details-setup";

export const AD_CREATIVE_DEFAULT = "ad-creative-default",
    AD_CREATIVE_POST = "ad-creative-post",
    AD_CREATIVE_VIDEO = "ad-creative-post";

export const AD_DETAILS_CALL_TO_ACTION_DEFAULT = "default-cta",
    AD_DETAILS_CALL_TO_ACTION_TOGGLE = "toggle-cta";

export const AD_DETAILS_DESTINATION_WEBSITE = "destination-type-website-btn",
    AD_DETAILS_DESTINATION_TIKTOK_PAGE = "destination-type-tiktok-instant-page-btn",
    AD_DETAILS_DESTINATION_APP = "destination-type-app-btn";

export const AD_DETAILS_DISPLAY_CONFIRMATION_ON_TIKTOK_AND_AFFILITIES = "displayed_on_tiktok_branded_or_affiliated_platforms",
    AD_DETAILS_CONTAINS_AI_GENERATED_CONTENT = "contains_ai_generated_content";

export const AD_DETAILS_RULE = {
    [OUTCOME_SALES]: {
        SHOP_ADS_TYPE: {
            [VIDEO_SHOPPING_ADS]: {
                visible: true,
                type: AD_DETAILS_VIDEO_SHOPPING,
                components: {},
            },
            [LIVE_SHOPPING_ADS]: {
                visible: true,
                type: AD_DETAILS_LIVE_SHOPPNG,
                components: {
                    AD_FORMAT: [AD_FORMATS_SINGLE_VIDEO, AD_FORMATS_REALTIME_LIVE],
                    AD_CREATIVE: [AD_CREATIVE_VIDEO],
                },
            },
            [PRODUCT_SHOPPING_ADS]: {
                visible: true,
                type: AD_DETAILS_PRODUCT_SET,
                components: {},
            },
        },
    },
    [OUTCOME_VIDEO_VIEWS]: {
        visible: true,
        type: AD_DETAILS_SETUP,
        components: {
            AD_FORMAT: [AD_FORMATS_SINGLE_VIDEO],
            AD_CREATIVE: [AD_CREATIVE_VIDEO],
            CALL_TO_ACTION: [AD_DETAILS_CALL_TO_ACTION_TOGGLE],
            DESTINATION: [AD_DETAILS_DESTINATION_WEBSITE],
            DISPLAY_CONFIRMATION: [AD_DETAILS_DISPLAY_CONFIRMATION_ON_TIKTOK_AND_AFFILITIES],
        },
    },
    [OUTCOME_TRAFFIC]: {
        OPTIMIZATION_LOCATION: {
            [OPTIMIZATION_LOCATION_WEBSITE]: {
                visible: true,
                type: AD_DETAILS_SETUP,
                components: {
                    AD_CREATIVE: [AD_CREATIVE_DEFAULT],
                    CALL_TO_ACTION: [AD_DETAILS_CALL_TO_ACTION_DEFAULT],
                    DESTINATION: [AD_DETAILS_DESTINATION_WEBSITE, AD_DETAILS_DESTINATION_TIKTOK_PAGE],
                    DISPLAY_CONFIRMATION: [AD_DETAILS_DISPLAY_CONFIRMATION_ON_TIKTOK_AND_AFFILITIES],
                },
            },
            [OPTIMIZATION_LOCATION_APP]: {
                visible: true,
                type: AD_DETAILS_SETUP,
                components: {
                    AD_CREATIVE: [AD_CREATIVE_DEFAULT],
                    CALL_TO_ACTION: [AD_DETAILS_CALL_TO_ACTION_DEFAULT],
                    DESTINATION: [AD_DETAILS_DESTINATION_APP],
                    DISPLAY_CONFIRMATION: [AD_DETAILS_DISPLAY_CONFIRMATION_ON_TIKTOK_AND_AFFILITIES],
                },
            },
        },
    },
    [OUTCOME_REACH]: {
        visible: true,
        type: AD_DETAILS_SETUP,
        components: {
            AD_FORMAT: [AD_FORMATS_SINGLE_VIDEO, AD_FORMATS_CAROUSEL_IMAGE],
            AD_CREATIVE: [AD_CREATIVE_POST],
            CALL_TO_ACTION: [AD_DETAILS_CALL_TO_ACTION_TOGGLE],
            DESTINATION: [AD_DETAILS_DESTINATION_WEBSITE, AD_DETAILS_DESTINATION_TIKTOK_PAGE],
            DISPLAY_CONFIRMATION: [AD_DETAILS_DISPLAY_CONFIRMATION_ON_TIKTOK_AND_AFFILITIES],
        },
    },
    [OUTCOME_CLICK]: {},
    [OUTCOME_COMMUNITY_INTERACTION]: {},
    [OUTCOME_APP_PROMOTION]: {},
    [OUTCOME_LEAD_GENERATION]: {},
};

export const AD_TRACKING_RULE = {
    [OUTCOME_SALES]: {
        SHOP_ADS_TYPE: {
            [VIDEO_SHOPPING_ADS]: {
                visible: false,
            },
            [LIVE_SHOPPING_ADS]: {
                visible: false,
            },
            [PRODUCT_SHOPPING_ADS]: {
                visible: false,
            },
        },
    },
    [OUTCOME_VIDEO_VIEWS]: {
        visible: true,
    },
    [OUTCOME_TRAFFIC]: {
        OPTIMIZATION_LOCATION: {
            [OPTIMIZATION_LOCATION_WEBSITE]: {
                visible: true,
            },
            [OPTIMIZATION_LOCATION_APP]: {
                visible: true,
            },
        },
    },
    [OUTCOME_REACH]: {
        visible: true,
    },
    [OUTCOME_CLICK]: {},
    [OUTCOME_COMMUNITY_INTERACTION]: {},
    [OUTCOME_APP_PROMOTION]: {},
    [OUTCOME_LEAD_GENERATION]: {},
};

export const AD_POLICY_NOTIFICATION = {
    [OUTCOME_SALES]: {
        SHOP_ADS_TYPE: {
            [VIDEO_SHOPPING_ADS]: {
                visible: false,
            },
            [LIVE_SHOPPING_ADS]: {
                visible: false,
            },
            [PRODUCT_SHOPPING_ADS]: {
                visible: false,
            },
        },
    },
    [OUTCOME_VIDEO_VIEWS]: {
        visible: true,
    },
    [OUTCOME_TRAFFIC]: {
        OPTIMIZATION_LOCATION: {
            [OPTIMIZATION_LOCATION_WEBSITE]: {
                visible: true,
            },
            [OPTIMIZATION_LOCATION_APP]: {
                visible: true,
            },
        },
    },
    [OUTCOME_REACH]: {
        visible: true,
    },
    [OUTCOME_CLICK]: {},
    [OUTCOME_COMMUNITY_INTERACTION]: {},
    [OUTCOME_APP_PROMOTION]: {},
    [OUTCOME_LEAD_GENERATION]: {},
};

export const AD_OPERATION_STATUS_OFF = "DISABLE",
    AD_OPERATION_STATUS_ON = "ENABLE";


export const AD_FORMAT_SINGLE_IMAGE = "SINGLE_IMAGE",
    AD_FORMAT_SINGLE_VIDEO = "SINGLE_VIDEO",
    AD_FORMAT_LIVE_CONTENT = "LIVE_CONTENT",
    AD_FORMAT_CAROUSEL_ADS = "CAROUSEL_ADS",
    AD_FORMAT_CATALOG_CAROUSEL = "CATALOG_CAROUSEL";


export const AD_PREVIEW_IN_FEED = "IN_FEED";
export const AD_PREVIEW_SEARCH_FEED = "SEARCH_FEED";
export const AD_PREVIEW_TIKTOK_LITE = "TIKTOK_LITE"

export const AD_PREVIEW_FORMAT = {
    [AD_PREVIEW_IN_FEED]: {
        value: AD_PREVIEW_IN_FEED,
        text: "In feed"
    },
    [AD_PREVIEW_SEARCH_FEED]: {
        value: AD_PREVIEW_SEARCH_FEED,
        text: "Search feed"
    },
    [AD_PREVIEW_TIKTOK_LITE]: {
        value: AD_PREVIEW_TIKTOK_LITE,
        text: "TikTok Lite"
    }
}