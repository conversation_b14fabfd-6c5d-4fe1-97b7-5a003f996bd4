import { AD_PREVIEW_FORMAT } from "/static/js/tiktok/ads/off-canvas/constant.js"
export default (() => {
    const adForm = $("#ad-form");
    const adPreview = adForm.find(".ad-component__ad-preview")
    const adPreviewSelect = adPreview.find("#ad-preview__placement-select")
    const adPreviewResult = adPreview.find(".ad-preview__result")

    function renderDataAdPreview(ad) {
        adPreviewSelect.hide()
        if (ad.adgroup.tiktok_subplacements) {
            adPreviewSelect.show();
            adPreviewSelect.empty();
            const placements = Array.from(new Set(ad.adgroup.tiktok_subplacements));
            placements.forEach(placements => {
                const option = AD_PREVIEW_FORMAT[placements];
                if (option) {
                    adPreviewSelect.append(`<option value="${option.value}">${option.text}</option>`);
                }
            })
        }
    }
    return {
        renderDataAdPreview,
    }
})()