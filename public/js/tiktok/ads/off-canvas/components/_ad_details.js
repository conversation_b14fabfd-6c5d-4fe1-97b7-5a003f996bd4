import {
    CTA_OPTIONS,
    // Rule
    AD_DETAILS_RULE,
    // Ad detail types
    AD_DETAILS_VIDEO_SHOPPING,
    AD_DETAILS_LIVE_SHOPPNG,
    AD_DETAILS_PRODUCT_SET,
    AD_DETAILS_SETUP,
    //Ad formats
    AD_FORMAT_SINGLE_IMAGE,
    AD_FORMAT_SINGLE_VIDEO,
    AD_FORMAT_LIVE_CONTENT,
    AD_FORMAT_CAROUSEL_ADS,
    AD_FORMAT_CATALOG_CAROUSEL,
    // Ad creative types
    AD_CREATIVE_DEFAULT,
    AD_CREATIVE_POST,
    AD_CREATIVE_VIDEO,
    // Call to action types
    AD_DETAILS_CALL_TO_ACTION_DEFAULT,
    AD_DETAILS_CALL_TO_ACTION_TOGGLE,
    // Destination types
    AD_DETAILS_DESTINATION_WEBSITE,
    AD_DETAILS_DESTINATION_TIKTOK_PAGE,
    AD_DETAILS_DESTINATION_APP,
    // Display confirmation types
    AD_DETAILS_DISPLAY_CONFIRMATION_ON_TIKTOK_AND_AFFILITIES,
} from "/static/js/tiktok/ads/off-canvas/constant.js";
import { getAdConfigFromRuleConfig, showIfValid, showLabelsByIds } from "/static/js/tiktok/ads/off-canvas/domHelper.js";
export default (() => {
    let isIniatializedAdDetails = false;

    const adForm = $("#ad-form");
    const adDetails = adForm.find(".ad_component_ad_details");

    const adDetailsAdFormatComponent = adDetails.find(".ad_details_ad_format");
    const adDetailsAdFormatBtns = adDetails.find('.btn-check[name="ad-format-btn"]');

    const adDetailsType = adDetails.find(".ad-details-type");

    const adDetailsLivestreamInfo = adDetails.find(".ad-details-livestream-info");
    const adDetailsLivestreamContent = adDetails.find(".ad-details-live-shopping");

    const adCreativeComponent = adDetails.find(".ad-creative-cpn");
    const adCreativeTypes = adDetails.find(".ad_component_ad_creative");
    const adCreativeMedia = adDetails.find(".ad-creative-select-result");
    const adCreativeText = adDetails.find("#ad-creative-text");
    const adCreativeCtaComponent = adDetails.find(".ad-creative-cta");
    const adCreativeDestinationCtaComponent = adDetails.find("#ad-details-cta-edit");

    const adDetailsCallToActionToggle = adDetails.find("#ad_creative_cta_toggle");
    const adDetailsCallToActionSetup = adDetails.find(".ad-creative-cta-setup");
    const adDetailsCallToActionType = adDetails.find('[name="cta-type"]');
    const adDetailsCallToActionDynamicContent = adDetails.find('[name="cta-type-dynamic-content"]');
    const adDetailsCallToActionStandardContent = adDetails.find('[name="cta-type-standard-content"]');
    const adDetailsCallToActionSelect = adDetails.find('[name="cta-type-select"]');
    const adDetailsCallToActionDefault = adDetails.find("#cta-select");
    const adDetailsDestinationOptions = $('.destination-types input[name="destination-type-btn"]');
    const adDetailsDestinationWebsiteUrl = adDetails.find('[name="destination_website_url"]');


    const adDetailsDestinationComponent = adDetails.find(".destination-cpn");
    const adDetailsDestinationSetUp = adDetails.find(".destination_setup");
    const adDetailsDestinationWebsiteContent = adDetailsDestinationSetUp.find(".destination_website_content");
    const adDetailsDestinationDeeplinkToggle = adDetailsDestinationSetUp.find("#destination_url_deeplink_first_toggle");
    const adDetailsDestinationDeeplinkInputContainer = adDetailsDestinationSetUp.find(".destination_deeplink_input");
    const adDetailsDestinationTiktokInstantPageContent = adDetailsDestinationSetUp.find(".destination_tiktok_instant_page_content");
    const adDetailsDestinationAppContent = adDetailsDestinationSetUp.find(".destination_app_content");

    const adDetailsDisplayConfirmationComponent = adDetails.find(".display_confirmation_cpn");

    function initCallToActionSelect() {
        //     adDetailsCallToActionDefault.empty();
        //     console.log(adDetailsCallToActionDefault);
        //     Object.values(CTA_OPTIONS).forEach((cta) => {
        //         const option = $('<option>', {
        //             value: cta.value,
        //             text: cta.text
        //         });
        //         adDetailsCallToActionDefault.append(option);
        //     });

        //     adDetailsCallToActionDefault.select2({
        //         placeholder: 'Select CTA',
        //         width: '100%',
        //         allowClear: true,
        //         multiple: true,
        //         closeOnSelect: false,
        //     });
        // }
        // Render Choices
        adDetailsCallToActionDefault.empty();
        const data = Object.values(CTA_OPTIONS).map((cta) => {
            return {
                value: cta.value,
                label: cta.text,
                id: cta.value,
                selected: cta.value === "learn_more",
            };
        });

        const choices = new Choices(adDetailsCallToActionDefault[0], {
            shouldSort: false,
            allowHTML: true,
            removeItemButton: true,
            renderSelectedChoices: "auto",
            placeholder: true,
            searchEnabled: true,
            classNames: {
                containerOuter: "choices tiktok-custom",
            },
            choices: data,
        });
    }

    function onToggleCallToAction() {
        adDetailsCallToActionToggle
            .on("change", function () {
                const isCallToAction = $(this).is(":checked");
                toggleDestination(isCallToAction);

                isCallToAction ? adDetailsCallToActionSetup.removeClass("d-none") : adDetailsCallToActionSetup.addClass("d-none");
            })
            .trigger("change");
    }

    function onChangeAdFormat() {
        adDetailsAdFormatBtns.on("change", function () {
            if ($(this).is(":checked")) {
                switch ($(this).val()) {
                    case AD_FORMAT_SINGLE_IMAGE: {
                        toggleLiveStreamContent(false);
                        toggleAdCreativeContent(true);
                        break;
                    }
                    case AD_FORMAT_SINGLE_VIDEO: {
                        toggleLiveStreamContent(false);
                        toggleAdCreativeContent(true);
                        break;
                    }
                    case AD_FORMAT_LIVE_CONTENT: {
                        toggleLiveStreamContent(true);
                        toggleAdCreativeContent(false);
                        break;
                    }
                    case AD_FORMAT_CAROUSEL_ADS: {
                        toggleLiveStreamContent(false);
                        toggleAdCreativeContent(false);
                        break;
                    }
                }
            }
        })
    }

    function toggleLiveStreamContent(status) {
        adDetailsLivestreamInfo.toggle(status);
        adDetailsLivestreamContent.toggle(status);
    }

    function toggleAdCreativeContent(status) {
        adCreativeComponent.toggle(status);
    }

    function onChangeCallToActionType() {
        $(document).on("change", '[name="cta-type"]', function (e) {
            if ($(this).is(":checked")) {
                const ctaType = $(this).val();
                $('[name="cta-type-dynamic-content"]').toggle(ctaType === "dynamic");
                $('[name="cta-type-standard-content"]').toggle(ctaType === "standard");
            }
        });
        $('[name="cta-type"]').trigger("change");
    }

    function toggleDestination(enabled) {
        adDetailsDestinationOptions.prop("disabled", !enabled);

        if (enabled) {
            let anyChecked = false;

            adDetailsDestinationOptions.each(function () {
                const label = $(`label[for="${this.id}"]`);

                label.removeClass("disabled text-muted");

                if (!label.is(":visible")) {
                    if (this.checked) {
                        this.checked = false;
                    }
                } else if (this.checked) {
                    anyChecked = true;
                }
            });

            if (!anyChecked) {
                const firstVisibleInput = adDetailsDestinationOptions.toArray().find((input) => {
                    const label = $(`label[for="${input.id}"]`);
                    return label.length && label.css("display") !== "none";
                });

                if (firstVisibleInput) {
                    adDetailsDestinationOptions.prop("checked", false);
                    firstVisibleInput.checked = true;
                    $(firstVisibleInput).trigger("change");
                }
            } else {
                const selected = adDetailsDestinationOptions.filter(":checked").get(0);
                if (selected) {
                    $(selected).trigger("change");
                }
            }
        } else {
            adDetailsDestinationOptions.each(function () {
                this.checked = false;
                const label = $(`label[for="${this.id}"]`);
                label.addClass("disabled text-muted");
            });

            adDetailsDestinationSetUp.hide();
            adDetailsDestinationWebsiteContent.hide();
            adDetailsDestinationTiktokInstantPageContent.hide();
        }
    }

    function onChangeDestinationType() {
        const typeToSelectorMap = {
            website: ".destination_website_content",
            tiktok_instant_page: ".destination_tiktok_instant_page_content",
            app: ".destination_app_content",
        };

        $(".destination-types input[name='destination-type-btn']").on("change", function () {
            const destinationValue = $(this).val();

            $(".destination_setup").toggle(!!destinationValue);
            Object.values(typeToSelectorMap).forEach(selector => $(selector).hide());

            const targetSelector = typeToSelectorMap[destinationValue];
            if (targetSelector) {
                $(targetSelector).show(220);
            }
        });
    }


    function onToggleDestinationDeeplink() {
        $(document).on('change', '#destination_url_deeplink_first_toggle', function (e) {
            const isDeeplink = $(this).is(":checked");
            if (isDeeplink) {
                $(".destination_deeplink_input").removeClass("d-none").show(220);
            } else {
                $(".destination_deeplink_input").hide(220).addClass("d-none");
            }
        })
        $("#destination_url_deeplink_first_toggle").trigger("change");
    }

    function onToggleDestinationDeeplinkApp() {
        const $toggle = $("#destination_url_deeplink_first_app_toggle");
        const $appDetails = $(".destination-app-details");
        const $fallbackSelect = $appDetails.find(".fallback-type-select");
        const $fallbackUrl = $appDetails.find(".destination-fallback-url");

        function updateVisibility() {
            const isEnabled = $toggle.prop("checked");

            if (isEnabled) {
                $appDetails.show(220);
            } else {
                $appDetails.hide();
                return;
            }

            const selected = $fallbackSelect.val();
            if (selected === "website") {
                $fallbackUrl.show(220);
            } else {
                $fallbackUrl.hide();
            }
        }

        $toggle.on("change", updateVisibility);
        $fallbackSelect.on("change", updateVisibility);

        updateVisibility();
    }

    function renderCalToActionSelect({ selected = null }) {
        const $select = $('select[name="cta-type-select"]');
        if (!$select.length) return;

        $select.empty();
        Object.values(CTA_OPTIONS).forEach((cta) => {
            const option = $("<option>", {
                value: cta.value,
                text: cta.text,
            });
            $select.append(option);
        });

        if (selected != null) {
            $select.val(selected);
        }
    }

    function updateAdDetailsCpnVisibility() {
        const config = getAdConfigFromRuleConfig(AD_DETAILS_RULE);
        if (!config) return;

        updateAdFormatVisibility(config.components?.AD_FORMAT);
        updateAdDetailsTypeVisibility(config.type);
        updateAdCreativeVisibility(config.components?.AD_CREATIVE);
        updateDestinationVisibility(config.components?.DESTINATION);
        updateCtaVisibility(config.components?.CALL_TO_ACTION);
        updateDisplayConfirmationVisibility(config.components?.DISPLAY_CONFIRMATION);
    }

    // --- Individual component visibility ---
    function updateAdFormatVisibility(validFormats) {
        showIfValid(adDetailsAdFormatComponent, validFormats?.length);

        if (!validFormats?.length) return;

        adDetailsAdFormatBtns.each(function () {
            const $input = $(this);
            const $label = adDetails.find(`label[for="${$input.attr("id")}"]`);
            $label.toggle(validFormats.includes($input.attr("id")));
        });

        const $visibleChecked = adDetailsAdFormatBtns.filter(":checked:visible");
        if ($visibleChecked.length === 0) {
            const fallbackId = validFormats[0];
            const $fallbackInput = $(`#${fallbackId}`);
            if ($fallbackInput.length) {
                $fallbackInput.prop("checked", true);
                adDetails.find(`label[for="${fallbackId}"]`).click();
            }
        }
    }

    function updateAdDetailsTypeVisibility(type) {
        adDetailsType.hide();
        adCreativeComponent.show();
        adDetailsDestinationComponent.show();
        adDetailsDisplayConfirmationComponent.show();
        adDetailsLivestreamInfo.hide();

        if (!type) return;

        adDetails.find(`.ad-details-type.${type}`).show();

        if (type === AD_DETAILS_LIVE_SHOPPNG) {
            adDetails.find(`.ad-details-type.ad-details-setup`).show();
            adCreativeComponent.hide();
            adDetailsDestinationComponent.hide();
            adDetailsDisplayConfirmationComponent.hide();
            adDetailsLivestreamInfo.show();
        }
    }

    function updateAdCreativeVisibility(adCreativeType) {
        adCreativeComponent.hide();
        adCreativeTypes.hide();
        adCreativeMedia.hide();

        if (!adCreativeType) return;

        adCreativeComponent.show();
        adDetails.find(`.ad_component_ad_creative.${adCreativeType}`).show();
    }

    function updateCtaVisibility(ctaType) {
        adCreativeCtaComponent.hide();

        if (!ctaType) return;

        adDetails.find(`.ad-creative-cta.${ctaType}`).show();
        if (AD_DETAILS_CALL_TO_ACTION_DEFAULT.includes(ctaType)) {
            toggleDestination(true);
        }
    }

    function updateDestinationVisibility(destinations) {
        showIfValid(adDetailsDestinationComponent, destinations?.length);

        if (!Array.isArray(destinations) || destinations.length === 0) return;

        showLabelsByIds(destinations, ".destination-types");

        const $checked = adDetails.find('.destination-types input[type="radio"]:checked');
        const $visibleChecked = $checked.filter(function () {
            return $(this).closest("label").is(":visible");
        });

        if ($visibleChecked.length === 0) {
            adDetails.find('.destination-types input[type="radio"]').each(function () {
                if ($(this).closest("label").is(":visible")) {
                    $(this).prop("checked", true);
                    return false;
                }
            });
        }
    }

    function updateDisplayConfirmationVisibility(confirmations) {
        const $wrapper = adDetails.find(".display_confirmation_cpn");
        const $options = $wrapper.find(".form-check");

        $options.hide();
        if (!Array.isArray(confirmations) || confirmations.length === 0) {
            $wrapper.hide();
            return;
        }

        confirmations.forEach((key) => $wrapper.find(`.${key}`).show());
        $wrapper.toggle($wrapper.find(".form-check:visible").length > 0);
    }

    function renderDataAdDetail(ad) {
        if (!ad) return;
        console.log("ad: ", ad);
        renderAdFormat(ad.ad_format);
        renderAdCreative(ad);
    }

    function renderAdFormat(adFormat) {
        adDetailsAdFormatBtns.each(function () {
            const $btn = $(this);
            const id = $btn.attr("id");
            const shouldShow = $btn.val().trim() === adFormat.trim();

            adForm.find(`label[for="${id}"]`).toggle(shouldShow);
        });

        const selectedBtn = Array.from(adDetailsAdFormatBtns).find(
            (btn) => btn.value.trim() === adFormat.trim()
        );

        if (selectedBtn) {
            $(selectedBtn).prop("checked", true).trigger("change");
        }
    }

    function renderAdCreative(ad) {
        // Ad creadtive media
        adCreativeTypes.hide();
        adCreativeMedia.show();
        // Text
        adCreativeText.val(ad.ad_text || "");

        // Call-to-Action
        adCreativeDestinationCtaComponent.toggle(false);

        if (ad.call_to_action) {
            adDetailsCallToActionToggle.prop("checked", true).trigger("change");
            adDetailsCallToActionType.val("standard").prop("checked", true).trigger("change");
            adDetailsCallToActionSelect.val(ad.call_to_action).trigger("change");
            if (ad.call_to_action.includes("WATCH_LIVE")) {
                adCreativeDestinationCtaComponent.toggle(true);
                adCreativeDestinationCtaComponent.find("#ad_call_to_action_edit").text(ad.call_to_action);
            }
        }
        // Destination
        if (ad.landing_page_url) {
            adDetailsDestinationOptions.each(function () {
                const $option = $(this);
                const isWebsite = $option.val() === "website";

                $option.prop("disabled", !isWebsite);

                if (isWebsite) {
                    adDetailsDestinationWebsiteUrl.val(ad.landing_page_url);
                }
            });
        }
    }



    function initAdOffCanvas({ ad = {} }) {
        //Init call to action after innerHTML
        onChangeCallToActionType();
        renderCalToActionSelect({ selected: ad?.call_to_action || null });
        //Init destination after innerHTML
        onToggleDestinationDeeplink();
        onToggleDestinationDeeplinkApp();
        onChangeDestinationType();
    }

    function init() {
        if (!isIniatializedAdDetails) {
            isIniatializedAdDetails = true;
            onChangeAdFormat();
            onChangeCallToActionType();
            onToggleCallToAction();
            onChangeDestinationType();
            onToggleDestinationDeeplink();
            onToggleDestinationDeeplinkApp();
            renderCalToActionSelect({ selected: null });
            initCallToActionSelect();
        }
    }

    return {
        init,
        updateAdDetailsCpnVisibility,
        renderDataAdDetail,
        initAdOffCanvas,
    };
})();
