import Select2JsComponent from "/static/js/components/select2js.js";
import { getAdConfigFromRuleConfig } from "/static/js/tiktok/ads/off-canvas/domHelper.js"
import { AD_PRODUCT_DETAILS_RULE } from "/static/js/tiktok/ads/off-canvas/constant.js"
export default (() => {
    const adForm = $("#ad-form");
    const productDetailsComponent = adForm.find(".ad_component_product_details")
    
    function updateProductDetailsCpnVisibility() {
        const config = getAdConfigFromRuleConfig(AD_PRODUCT_DETAILS_RULE);
        if (!config) return;
        productDetailsComponent.toggle(config.visible);
    }

    function init() {

    }
    return {
        init,
        updateProductDetailsCpnVisibility,
    }
})()