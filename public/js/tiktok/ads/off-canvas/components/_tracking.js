import { getAdConfigFromRuleConfig } from "/static/js/tiktok/ads/off-canvas/domHelper.js"
import { AD_TRACKING_RULE } from "/static/js/tiktok/ads/off-canvas/constant.js"

export default (() => {
    const adForm = $("#ad-form");
    const tracking = adForm.find(".ad-component_tracking");
    const trackingEditEventTrackingBtn = tracking.find("#editEventTrackingBtn");
    const trackingEventBriefInfo = tracking.find(".tiktok-events-tracking-blk");
    const trackingEventTrackingDetails = tracking.find(".tracking-event-tracking");

    function onClickEditTrackingEvents() {
        trackingEditEventTrackingBtn.on("click", function () {
            trackingEventBriefInfo.addClass("d-none");
            trackingEventTrackingDetails.show();
        });
    }

    function updateTrackingCpnVisibility() {
        const config = getAdConfigFromRuleConfig(AD_TRACKING_RULE);
        if (!config) return;
        tracking.toggle(config.visible);
    }

    function init() {
        onClickEditTrackingEvents();
    }

    return {
        init,
        updateTrackingCpnVisibility,
    }
})()