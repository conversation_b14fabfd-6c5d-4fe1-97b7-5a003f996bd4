import { getAdConfigFromRuleConfig } from "/static/js/tiktok/ads/off-canvas/domHelper.js"
import { AD_IDENTITY_RULE } from "/static/js/tiktok/ads/off-canvas/constant.js"
export default (() => {
    const adForm = $("#ad-form");
    const identityComponent = adForm.find(".ad_component_identity");
    const modalPostAuthorization = $("#modal_authorize_tiktok_post");
    const postAuthorizedByAccountSelect = adForm.find('[name="post_authorized_select"]');

    const postAuthorizationCarousel = modalPostAuthorization.find("#post_authorization_carousel");
    const postAuthorizationCarouselPrevBtn = modalPostAuthorization.find("#carousel-prev");
    const postAuthorizationCarouselNextBtn = modalPostAuthorization.find("#carousel-next");
    const postAuthorizationCarouselIndicator = modalPostAuthorization.find("#carousel-indicator");

    let identityInitiated = false;
    function initPostAuthorizedByAccountSelect() {
        const customData = [
            {
                id: "1",
                avatar: "/static/images/users/avatar-1.jpg",
                name: "Nary clothing",
                description: "Authorized posts only",
            },
            {
                id: "2",
                avatar: "/static/images/users/avatar-2.jpg",
                name: "Nga nè",
                description: "Authorized posts only",
            },
            {
                id: "3",
                avatar: "/static/images/users/avatar-3.jpg",
                name: "Baobeiiii",
                description: "Authorized posts only",
            },
        ];

        const $select = $('[name="post_authorized_select"]')
        $select.empty();
        customData.forEach((item) => {
            const option = new Option(item.name, item.id, false, false);
            $(option).attr("data-avatar", item.avatar);
            $(option).attr("data-description", item.description);
            $select.append(option);
        });

        $select.select2({
            placeholder: "Select an account...",
            templateResult: formatOption,
            templateSelection: formatSelected,
            width: "100%",
            language: {
                searching: function () {
                    return "Search by name";
                }
            },
        });

        function formatOption(option) {
            if (!option.id) return option.text;

            const $option = $(option.element);
            const avatar = $option.data("avatar");
            const description = $option.data("description");

            return $(`
                <div class="d-flex align-items-center gap-2">
                <img src="${avatar}" class="rounded-circle" width="40" height="40" />
                <div>
                    <div>${option.text}</div>
                    <small class="text-muted">${description}</small>
                </div>
                </div>
            `);
        }

        function formatSelected(option) {
            if (!option.id) return option.text;

            const $option = $(option.element);
            const avatar = $option.data("avatar");

            return $(`
                <div class="d-flex align-items-center gap-2">
                <img src="${avatar}" class="rounded-circle" width="20" height="20" />
                <span>${option.text}</span>
                </div>
            `);
        }


    }

    function initPostAuthorizationCarouselControls() {
        const $carousel = postAuthorizationCarousel;
        const totalItems = $carousel.find(".carousel-item").length;
        const $indicator = postAuthorizationCarouselIndicator;
        let currentIndex = 1;

        function updateIndicator(index) {
            $indicator.text(`${index} / ${totalItems}`);
        }

        $carousel.on("slid.bs.carousel", function (e) {
            currentIndex = $(e.relatedTarget).index() + 1;
            updateIndicator(currentIndex);
        });

        postAuthorizationCarouselPrevBtn.on("click", function () {
            $carousel.carousel("prev");
        });

        postAuthorizationCarouselNextBtn.on("click", function () {
            $carousel.carousel("next");
        });

        updateIndicator(currentIndex);
    }

    function updateIdentityCpnVisibility() {
        const config = getAdConfigFromRuleConfig(AD_IDENTITY_RULE);
        if (!config) return;
        identityComponent.toggle(config.visible);
    }

    function init() {
        if (!identityInitiated) {
            initPostAuthorizedByAccountSelect();
            initPostAuthorizationCarouselControls();
            identityInitiated = true
        }
    }

    function initAdOffCanvas() {
        initPostAuthorizedByAccountSelect();
    }

    return {
        init,
        updateIdentityCpnVisibility,
        initAdOffCanvas,
    };
})();
