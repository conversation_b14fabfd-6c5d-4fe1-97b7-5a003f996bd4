import { AD_POLICY_NOTIFICATION } from "/static/js/tiktok/ads/off-canvas/constant.js"
import { getAdConfigFromRuleConfig } from "/static/js/tiktok/ads/off-canvas/domHelper.js"
export default (() => {
    const adForm = $("#ad-form");
    const policyCpn = adForm.find(".ad-component__policy");

    function updatePolicyNotificationVisiblity() {
        const config = getAdConfigFromRuleConfig(AD_POLICY_NOTIFICATION);
        if (!config) return;
        policyCpn.toggle(config.visible);
    }

    function init() {

    }


    return {
        init,
        updatePolicyNotificationVisiblity,
    }
})()