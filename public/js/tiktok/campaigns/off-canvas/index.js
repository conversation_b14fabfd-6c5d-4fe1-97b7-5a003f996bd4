import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
export default (() => {
    function init() { }

    async function renderCampaignOffCanvasContent() {
        try {
            loader();
            const campaignId = getParamURL("edit_campaign_id");
            if (!campaignId) throw new Error("Thiếu campaign_id");

            const campaignEditRes = await fetch("/dsp/tiktok/api/campaign/edit", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: JSON.stringify({ campaign_id: campaignId }),
            });

            if (!campaignEditRes.ok) throw new Error("Lỗi khi fetch campaign edit");

            const campaignEditData = await campaignEditRes.json();
            const data = campaignEditData.data;

            const offcanvas = document.getElementById("tiktok-offcanvas-content");
            if (offcanvas) {
                offcanvas.innerHTML = campaignEditData.html;
                $(offcanvas).find("input, select").prop("disabled", true);
            }

            const campaignDetailRes = await fetch("/dsp/tiktok/api/campaign/detail", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                },
                body: JSON.stringify({ campaign_id: data?.campaign_id, active_id: data?.campaign_id }),
            });

            if (!campaignDetailRes.ok) throw new Error("Lỗi khi fetch campaign detail");

            const campaignDetailData = await campaignDetailRes.json();
            const sidebar = document.getElementById("menu-camp-adgroup-ad-left-side-bar");
            if (sidebar) sidebar.innerHTML = campaignDetailData.html;
        } catch (error) {
            console.error("Không thể tải dữ liệu chiến dịch:", error);
        } finally {
            loader(false);
        }
    }

    return {
        init,
        renderCampaignOffCanvasContent,
    };
})();
