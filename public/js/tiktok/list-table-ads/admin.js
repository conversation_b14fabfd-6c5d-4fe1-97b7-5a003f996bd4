

import CampaignList from "/static/js/tiktok/campaigns/list_table_campaigns.js";
import AdgroupList from "/static/js/tiktok/adgroups/list_table_adgroups.js";
import AdList from "/static/js/tiktok/ads/list_table_ads.js"
export default (() => {
    let isInit = true;
    const ID_CIENTS = "#clientsChoices";
    const ID_ADVERTISER = "#advertiserChoices";
    let dataItems = [];
    let dataAdvertiser = [];


    function getOptionData() {
        $(ID_CIENTS)
            .find("option")
            .each(function (index) {
                dataItems.push({
                    value: $(this).val(),
                    label: $(this).data("label"),
                    disabled: !index,
                    selected: !index,
                    customProperties: {
                        icon: $(this).data("icon"),
                    },
                });
            });
        $(ID_CIENTS).empty();
        return dataItems;
    }

    function getOptionAdvertisersData() {
        $(ID_ADVERTISER)
            .find("option")
            .each(function (index) {
                dataAdvertiser.push({
                    value: $(this).val(),
                    label: $(this).data("label"),
                    disabled: !index,
                    selected: !index,
                },
                );
            });
        return dataAdvertiser;
    }

    /**
     * Template user option
     */
    function getOptionTemplate(template) {
        return {
            item: ({ classNames }, data) => {
                return template(`
                  <div data-ok class="${classNames.item} ${data.highlighted ? classNames.highlightedState : classNames.itemSelectable}" data-item data-id="${data.id
                    }"  data-value="${data.value}">
                      ${data.label}
                      <button type="button" class="${classNames.button} btn-remove-selected" data-value="${data.value}" aria-label="Remove item: ${data.label
                    }" data-button="">Remove item</button>
                  </div>
              `);
            },
            choice: ({ classNames }, data) => {
                return template(`
                <div class="${classNames.item} ${classNames.itemChoice} ${data.disabled ? classNames.itemDisabled : classNames.itemSelectable}" data-select-text="${this.config.itemSelectText
                    }" data-choice ${data.disabled ? 'data-choice-disabled aria-disabled="true"' : "data-choice-selectable"} data-id="${data.id}" data-value="${data.value}" ${data.groupId > 0 ? 'role="treeitem"' : 'role="option"'
                    } >                 
                   <div class="d-flex align-items-center gap-2">
                     ${data.customProperties?.icon
                        ? ` <img class="avatar-xs rounded" src="${data.customProperties.icon}" />
                        <div>
                        <span class="fw-semibold">${data.label}</span><br>
                        <span>ID: ${data.value}</span>
                        </div>`
                        : `<span class="fw-semibold">${data.label}</span>`
                    }
                   </div>
                </div>
              `);
            },
        };
    }

    /**
     * Render User Info Profile
     */
    async function renderClientsListSelectionUI() {
        let choices = new Choices(ID_CIENTS, {
            shouldSort: false,
            allowHTML: true,
            removeItemButton: true,
            renderSelectedChoices: "auto",
            wrap: false,
            placeholder: true,
            searchEnabled: true,
            callbackOnCreateTemplates: getOptionTemplate,
            searchPlaceholderValue: "Search...",
            choices: getOptionData(),
        });
    }

    /**
     * Render Advertiser Profile
     */
    async function renderAdvertiserListSelectionUI() {
        let choices = new Choices(ID_ADVERTISER, {
            shouldSort: true,
            allowHTML: true,
            removeItemButton: true,
            renderSelectedChoices: "auto",
            placeholder: true,
            searchEnabled: true,
            searchPlaceholderValue: "Search...",
            callbackOnCreateTemplates: getOptionTemplate,
        });
    }

    /**
     * Render User Info Profile
     */
    function onChangeSelectClient() {
        $(ID_CIENTS).on("change", function () {
            CampaignList.onSelectUserFilter({ clientId: this.value, advertiserId: $(ID_ADVERTISER).val() });
            AdgroupList.onSelectUserFilter({ clientId: this.value, advertiserId: $(ID_ADVERTISER).val() });
            AdList.onSelectUserFilter({ clientId: this.value, advertiserId: $(ID_ADVERTISER).val() });
        });
    }

    /**
     * On Change Select Add Account
     */
    function onChangeSelectAddAccount() {
        $(ID_ADVERTISER).on("change", function () {
            CampaignList.onSelectUserFilter({ clientId: $(ID_CIENTS).val(), advertiserId: this.value });
            AdgroupList.onSelectUserFilter({ clientId: $(ID_CIENTS).val(), advertiserId: this.value });
            AdList.onSelectUserFilter({ clientId: $(ID_CIENTS).val(), advertiserId: this.value });
        });
    }

    /**
     * Init User
     */
    function init() {
        const user = getUserInfo();
        if (isInit && user && user.role_name === "ADMIN") {
            renderClientsListSelectionUI();
            renderAdvertiserListSelectionUI();
            onChangeSelectClient();
            onChangeSelectAddAccount();
            isInit = false;
        }
    }
    // return {
    //     init,
    // };

    $().ready(function () {
        init();
    });
})();
