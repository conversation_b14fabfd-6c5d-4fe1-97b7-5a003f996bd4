import { getHeaders } from "/static/js/common/auth.js";

export default (() => {
    var advertiser_dt;
    const searchRoleAll = "All";

    var templateCheck = "";
    if ($("#list_checks_btn").length) {
        templateCheck = $("#list_checks_btn").html();
    }

    function init() {
        advertiser_dt = $("#advertiser_table").DataTable({
            lengthMenu: [
                [10, 50, 100, 200, -1],
                [10, 50, 100, 200, "All"],
            ],
            scrollY: "58vh",
            scrollX: true,
            // fixedColumns: {
            //     leftColumns: 3,
            // },
            order: [[1, "asc"]],
            processing: true,
            lengthChange: true,
            pageLength: 10,
            paging: true,
            dom: "<'row'<'col-sm-12'tr>>" + "<'row p-2'<'col-sm-6 col-md-3'l><'col-sm-6 col-md-5'i><'col-sm-12 col-md-4 d-flex justify-content-end'p>>",
            columns: [
                {
                    data: null,
                    orderable: false,
                    width: "50px",
                    render: function (data, type, row, meta) {
                        return processCheckDataTable(row.advertiser_id);
                    },
                },
                {
                    data: "name",
                    title: "Name",
                    render: function (data, type, row) {
                        if (type === "filter") return data;
                        return ` 
                        <div class="d-flex flex-column align-items-left gap-1">
                            ${data}
                        </div>`;
                    },
                },
                {
                    data: "advertiser_id",
                    title: "Advertiser ID",
                    // width: "100px",
                    className: "text-start",
                    searchable: true,
                    render: function (data, type, row, meta) {
                        if (type === "display") {
                            return `
                            <div class="d-flex">
                                ${data}
                            </div>`;
                        }
                        return data;
                    },
                },

                {
                    data: "status",
                    title: "Status",
                    searchable: true,
                    render: function (data, type, row) {
                        return $(`#${data}`).html();
                    },
                },
            ],
            initComplete: function () {
                if (!templateCheck) {
                    advertiser_dt.columns(0).visible(false);
                }
            },
            ajax: {
                url: "/dsp/tiktok/api/advertiser/list",
                dataType: "json",
                type: "POST",
                headers: getHeaders(),
                dataSrc: function (response) {
                    if (!response.data) {
                        return [];
                    }
                    return response.data;
                },
            },
        });
    }

    function processCheckDataTable(id) {
        var str = templateCheck.replace(/{id}/g, id);
        return str;
    }

    document.addEventListener("DOMContentLoaded", function (event) {
        init();
    });
})();
