import updateTikResource from "/static/js/admins/clients/update_tik_resource.js";

let presetColumnChoice;
const ID_PRESET_COLUMN_CHOICES_TIK = "#tiktokPresetColumnTable";
let dataItems = [];
let presetColumnOfClientTik = [];
const getPresetColumnChoiceConfigTik = {
    shouldSort: false,
    allowHTML: true,
    // maxItemCount: 20,
    removeItemButton: true,
    renderSelectedChoices: "auto",
    placeholder: true,
    placeholderValue: "--- Choose preset ---",
    searchEnabled: true,
    callbackOnCreateTemplates: getOptionTemplate,
};

function getPresetColOptionDataTik() {
    const clientId = updateTikResource.clientId;
    const checkDisableOp = (clientId, listClient) => {
        if (typeof listClient !== "string" || !listClient.trim()) return false;

        const clients = listClient
            .split(",")
            .map((id) => id.trim())
            .filter(Boolean);
        if (clients.length > 0) {
        }
        return clients.length > 0 && !clients.includes(clientId);
    };

    const getAdaccountOfClient = (id, listClient, clientId) => {
        if (listClient && listClient.includes(clientId)) {
            presetColumnOfClientTik.push(id);
        }
    };

    $(ID_PRESET_COLUMN_CHOICES_TIK)
        .find("option")
        .each(function (index) {
            getAdaccountOfClient($(this).val(), $(this).data("client-ids"), clientId);
            dataItems.push({
                value: $(this).val(),
                label: $(this).data("label"),
                disabled: !index,
                selected: $(this).data("client-ids")?.includes(clientId),
                customProperties: {
                    icon: $(this).data("icon"),
                    client_ids: $(this).data("client-ids"),
                },
            });
        });
    $(ID_PRESET_COLUMN_CHOICES_TIK).empty();

    return dataItems;
}

function getOptionTemplate(template) {
    return {
        item: ({ classNames }, data) => {
            return template(`
                <div class="${classNames.item} ${data.highlighted ? classNames.highlightedState : classNames.itemSelectable}" data-value="${data.value}">
                    ${data.label}
                    <button type="button" class="${classNames.button} btn-remove-preset-column-selected" data-value="${data.value}" aria-label="Remove item: ${
                data.label
            }" data-button="">Remove item</button>
                </div>
            `);
        },
        choice: ({ classNames }, data) => {
            return template(`
              <div class="${classNames.item} ${classNames.itemChoice} ${data.disabled ? classNames.itemDisabled : classNames.itemSelectable}" data-select-text="${
                this.config.itemSelectText
            }" data-choice ${data.disabled ? 'data-choice-disabled aria-disabled="true"' : "data-choice-selectable"} data-id="${data.id}" data-value="${data.value}" ${
                data.groupId > 0 ? 'role="treeitem"' : 'role="option"'
            }>
            ${data.label}    
              </div>
            `);
        },
    };
}

const intEventHandler = () => {
    $(ID_PRESET_COLUMN_CHOICES_TIK).on("change", (event) => {
        if (event.target.value) {
            presetColumnChoice.enable();
        }
    });

    //Custom event remove Pages Selected
    $(ID_PRESET_COLUMN_CHOICES_TIK)
        .parent(".choices__inner")
        .on("click", ".btn-remove-preset-column-selected", (event) => {
            event.stopPropagation();
            presetColumnChoice.removeActiveItemsByValue(event.target.dataset.value);
        });
};

const setPresetColumnChoiceTik = (choice) => {
    presetColumnChoice = choice;
};

const getAdaccountOfClient = () => {
    return presetColumnOfClientTik;
};

$(document).ready(function () {
    intEventHandler();
});

export { getPresetColumnChoiceConfigTik, ID_PRESET_COLUMN_CHOICES_TIK, setPresetColumnChoiceTik, getPresetColOptionDataTik, presetColumnOfClientTik };
