import updateTikResource from "/static/js/admins/clients/update_tik_resource.js";

let adAccountChoice;
const ID_ADACCOUNT_CHOICES_TIK = "#tiktokAdAccountChoices";
let dataItems = [];
let adaccOfClientTik = [];
const getAdAccountChoiceConfigTik = {
    shouldSort: false,
    allowHTML: true,
    maxItemCount: 1,
    removeItemButton: true,
    renderSelectedChoices: "auto",
    placeholder: true,
    placeholderValue: "--- Choose AdAccount ---",
    searchEnabled: true,
    callbackOnCreateTemplates: getOptionTemplate,
};

function getAdaccountOptionDataTik() {
    const clientId = updateTikResource.clientId;
    const checkDisableOp = (clientId, listClient) => {
        if (typeof listClient !== "string" || !listClient.trim()) return false;

        const clients = listClient
            .split(",")
            .map((id) => id.trim())
            .filter(Boolean);
        if (clients.length > 0) {
        }
        return clients.length > 0 && !clients.includes(clientId);
    };

    const getAdaccountOfClient = (accountId, listClient, clientId) => {
        if (listClient && listClient.includes(clientId)) {
            adaccOfClientTik.push(accountId);
        }
    };

    $(ID_ADACCOUNT_CHOICES_TIK)
        .find("option")
        .each(function (index) {
            getAdaccountOfClient($(this).val(), $(this).data("client-ids"), clientId);
            dataItems.push({
                value: $(this).val(),
                label: $(this).data("label"),
                disabled: !index || checkDisableOp(clientId, $(this).data("client-ids")),
                selected: $(this).data("client-ids")?.includes(clientId),
                customProperties: {
                    icon: $(this).data("icon"),
                    client_ids: $(this).data("client-ids"),
                },
            });
        });
    $(ID_ADACCOUNT_CHOICES_TIK).empty();

    return dataItems;
}

function getOptionTemplate(template) {
    return {
        item: ({ classNames }, data) => {
            return template(`
                <div class="${classNames.item} ${data.highlighted ? classNames.highlightedState : classNames.itemSelectable}" data-value="${data.value}">
                    ${data.label}
                    <button type="button" class="${classNames.button} btn-remove-adaccount-selected" data-value="${data.value}" aria-label="Remove item: ${
                data.label
            }" data-button="">Remove item</button>
                </div>
            `);
        },
        choice: ({ classNames }, data) => {
            return template(`
              <div class="${classNames.item} ${classNames.itemChoice} ${data.disabled ? classNames.itemDisabled : classNames.itemSelectable}" data-select-text="${
                this.config.itemSelectText
            }" data-choice ${data.disabled ? 'data-choice-disabled aria-disabled="true"' : "data-choice-selectable"} data-id="${data.id}" data-value="${data.value}" ${
                data.groupId > 0 ? 'role="treeitem"' : 'role="option"'
            }>
            ${data.label}    
              </div>
            `);
        },
    };
}

const intEventHandler = () => {
    $(ID_ADACCOUNT_CHOICES_TIK).on("change", (event) => {
        if (event.target.value) {
            adAccountChoice.enable();
        }
    });

    //Custom event remove Pages Selected
    $(ID_ADACCOUNT_CHOICES_TIK)
        .parent(".choices__inner")
        .on("click", ".btn-remove-adaccount-selected", (event) => {
            event.stopPropagation();
            adAccountChoice.removeActiveItemsByValue(event.target.dataset.value);
        });
};

const setAdaccountChoiceTik = (choice) => {
    adAccountChoice = choice;
};

const getAdaccountOfClient = () => {
    return adaccOfClientTik;
};

$(document).ready(function () {
    intEventHandler();
});

export { getAdAccountChoiceConfigTik, ID_ADACCOUNT_CHOICES_TIK, setAdaccountChoiceTik, getAdaccountOptionDataTik, adaccOfClientTik };
