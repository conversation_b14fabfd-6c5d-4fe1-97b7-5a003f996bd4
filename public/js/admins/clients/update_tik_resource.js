import clientService from "/static/js/services/admins/clientService.js";
import userClient from "/static/js/admins/clients/list_user_of_client.js";
import { adaccOfClientTik as adaccount_ids } from "/static/js/admins/clients/tik_adaccount.js";
import { presetColumnOfClientTik as preset_column_ids } from "/static/js/admins/clients/tik_preset_column.js";
import Alert from "/static/js/components/alert.js";

export default (() => {
    const ID_FORM = "#frm_edit_tiktok_client";
    const clientId = $(`${ID_FORM} input[type="hidden"][name="client_ids[]"]`).val();
    let isUpdate = 0;
    const page_ids = [],
        pixel_ids = [],
        catalogue_ids = [];

    function parseErrorMessage({ jqXHR }) {
        try {
            const res = JSON.parse(jqXHR?.responseText || "{}");
            return res?.details?.msg?.join(", ") || "Client Connect Tiktok Resource Fail!";
        } catch {
            return "Client Connect Tiktok Resource Fail!";
        }
    }

    function buildPayload(formData) {
        let json = formDataToNestedJson(formData);
        delete json["search_terms"];
        const userData = userClient.getDatatable().rows().data().toArray();
        const list_user_ids = userData.map((i) => i._id);

        return { ...json, list_user_ids };
    }

    function mergeDiff(A, B) {
        const setA = new Set(A);
        return B.filter((item) => !setA.has(item));
    }

    function getDisconnectData(payloadForm) {
        const disConnectData = {};
        const importedData = { adaccount_ids, preset_column_ids, page_ids, pixel_ids, catalogue_ids };
        const keys = ["adaccount_ids", "page_ids", "pixel_ids", "catalogue_ids", "preset_column_ids"];

        for (const key of keys) {
            const original = importedData[key];
            if (Array.isArray(original) && original.length > 0) {
                const formValue = payloadForm[key];
                if (!Array.isArray(formValue) || formValue.length === 0) {
                    disConnectData[key] = [...original];
                } else {
                    disConnectData[key] = mergeDiff(formValue, original);
                }
                if (!disConnectData[key].length) delete disConnectData[key];
            }
        }

        return disConnectData;
    }
    function init() {
        // Onclick submit Form
        $(ID_FORM).on("submit", async function (e) {
            e.preventDefault();
            if (isUpdate) return;

            isUpdate = 1;
            try {
                const formData = new FormData(this);
                const jsonPayload = buildPayload(formData);
                console.log(" 🚀 ~ jsonPayload:", jsonPayload);
                const disconnect = getDisconnectData(jsonPayload);
                if (Object.keys(disconnect).length !== 0 && disconnect.constructor == Object) {
                    jsonPayload["disconnect"] = disconnect;
                }
                const res = await clientService.updateTikByJson(jsonPayload);
            } catch (err) {
                console.error(" 🚀 ~ err:", err);
                const msg = parseErrorMessage(err);
                Alert.error(msg);
            } finally {
                isUpdate = 0;
            }
        });
    }
    $(document).ready(() => {
        init();
    });

    return {
        clientId,
        ID_FORM_UPDATE_TIK_RESOURCE: ID_FORM,
    };
})();
