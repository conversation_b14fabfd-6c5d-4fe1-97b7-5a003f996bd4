{{ define "menu_sidebar_admin"}}
{{$permissions := .authPermission}}
{{$userInfo := .userInfo}}
{{$roleAdmin := .roleAdmin}}

{{ if hasPermissions $permissions "/admins/permissions/list-GET" "/admins/roles/list-GET" "/admins/users/list-GET"
"/admins/billings/list-GET" "/admins/clients/list-GET" "/admins/clients/profile-GET"
}}
<li class="nav-item" id="sidebar_Admins">
    <a href="#sidebarAdmins" class="nav-link menu-link" data-bs-toggle="collapse" role="button" aria-expanded="true"
        aria-controls="sidebarAdmins">
        <img class="icon-expand" src="/static/img/admin_text.svg" height="20px">
        <img class="icon-less" src="/static/img/admin_icon.svg" height="20px">
        <span class="item-text">Admins</span>
    </a>
    <div class="menu-dropdown collapse" id="sidebarAdmins">
        <ul class="nav nav-sm flex-column">
            {{ if hasPermission $permissions "/admins/clients/list-GET" }}
            <li class="nav-item">
                <a href="{{site}}/admins/clients/list" class="nav-link" data-key="t-users"><i class="ri-vip-crown-2-line"></i>Clients</a>
            </li>
            {{end}}

            {{ if hasPermission $permissions "/admins/users/list-GET" }}
            <li class="nav-item">
                <a href="{{site}}/admins/users/list" class="nav-link" data-key="t-users"><i class=" ri-user-settings-line"></i> Users</a>
            </li>
            {{end}}

            {{ if hasPermission $permissions "/admins/billings/list-GET" }}
            <li class="nav-item">
                <a href="{{site}}/admins/billings/list" class="nav-link" data-key="t-billings"><i class="ri-bank-card-line"></i>
                    Billings</a>
            </li>
            {{end}}

            {{ if hasPermission $permissions "/admins/roles/list-GET" }}
            <li class="nav-item">
                <a href="{{site}}/admins/roles/list" class="nav-link" data-key="t-roles"><i class=" ri-shield-user-line"></i> Roles</a>
            </li>
            {{end}}

            {{ if hasPermission $permissions "/admins/permissions/list-GET" }}
            <li class="nav-item">
                <a href="{{site}}/admins/permissions/list" class="nav-link" data-key="t-permissions"><i class="ri-lock-2-line "></i>
                    Permissions</a>
            </li>
            {{end}}


            {{ if and $permissions (hasPermission $permissions "/admins/clients/profile-GET") }}
            <li class="nav-item">
                <a href="{{site}}/admins/clients/profile/{{$userInfo.ClientId.Hex}}" class="nav-link" data-key="t-users"><i
                        class="ri-vip-crown-2-line"></i>Client info</a>
            </li>
            {{end}}

            {{ if and $permissions (hasPermission $permissions "/admins/users/user-profile-GET") }}
            <li class="nav-item">
                <a href="{{site}}/admins/users/profile/{{$userInfo.UserId.Hex}}" class="nav-link" data-key="t-users"><i
                        class="ri-account-box-line"></i>Profile</a>
            </li>
            {{end}}
        </ul>
    </div>
</li>
{{end}}

{{end}}