{{template "layout_header"}}
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Admins</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{site}}/admins/roles/list">Roles</a></li>
                    <li class="breadcrumb-item active">Permission</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div class="row mb-3">
    <div class="col-lg-3">
        <div class="card">
            <div class="file-manager-sidebar">
                <div class="p-3 d-flex flex-column h-100">
                    <div class="mb-3">
                        <h5 class="mb-0 fw-semibold fs-16">Filter</h5>
                    </div>
                    <div class="mx-n4 px-4 file-menu-sidebar-scroll" style="min-height: 300px;" data-simplebar>
                        <ul class="list-unstyled file-manager-menu">
                            {{if .permissionGroupModule}}
                            {{range $kGroup, $group := .permissionGroupModule}}

                            <li data-group="{{$group.Group}}">
                                <a data-bs-toggle="collapse" href="#collapse{{$group.Group}}" role="button" aria-expanded="true"
                                    aria-controls="collapse{{$group.Group}}">
                                    <span class="file-list-link text-capitalize text-primary">{{$group.Group}}</span>
                                </a>
                                <div class="collapse {{if eq $kGroup 0}}show{{end}}" id="collapse{{.Group}}">
                                    <ul class="sub-menu list-unstyled">
                                        {{range $kModule, $module := .ModulesByGroup}}
                                        <li>
                                            <a href="#!"
                                                class="text-capitalize module-link {{if and (eq $kModule 0) (eq $kGroup 0)}} active {{end}}"
                                                data-module="{{$module}}">{{$module}}</a>
                                        </li>
                                        {{end}}
                                    </ul>
                                </div>
                            </li>
                            {{end}}
                            {{end}}
                        </ul>
                    </div>
                </div>
            </div>
        </div>



    </div>
    <div class="col-lg-9">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center justify-content-between">
                    <div class="col-10">
                        <ul class="nav nav-tabs-custom card-header-tabs border-bottom-0" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link fw-semibold active" data-bs-toggle="tab" href="#info" role="tab" aria-selected="false">
                                    Permission setting
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-2 text-end">
                        <input class="form-check-input mx-2" type="checkbox" id="checkAll" value="1">
                        <span>Select All</span>
                    </div>

                </div>
            </div>
            <div class="card-body">
                <div class="tab-content text-muted">
                    <div class="tab-pane active" id="info" role="tabpanel">

                        <div class="table-responsive table-card mb-1">
                            <table class="table table-striped table-hover" id="permissionTable">
                                <thead class="table-light text-muted">
                                    <tr>
                                        <th class="col-1" style="text-align: center;">No.</th>
                                        <th class="col-4" style="text-align: left;">Action</th>
                                        <th class="col-2" style="text-align: center;">Action access</th>
                                        <th style="text-align: left;">API</th>
                                    </tr>
                                </thead>
                                <tbody id="permissions_dt">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-12">
            <div class="hstack gap-2 justify-content-end">
                <input type="hidden" id="id_update" value="{{.detail.ID.Hex}}">
                <a href="{{site}}/admins/roles/list" class="btn btn-soft-success">Cancel</a>
            </div>
        </div>
    </div>
</div>

{{template "layout_footer"}}
<script src="{{site}}/static/themes/js/pages/form-input-spin.init.js" type="text/javascript"></script>
<script type="module" src="{{site}}/static/js/admins/roles/permission.js"></script>