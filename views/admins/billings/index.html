{{template "layout_header" .}}

<style type="text/css">
    .billings-list-table__filter .choices {
        flex-grow: 1 !important;
    }

    .billings-list-table__filter .choices__list--dropdown {
        z-index: 10;
    }
</style>

<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
            <h4 class="mb-sm-0">Billings</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="javascript: void(0);">Billings</a></li>
                    <li class="breadcrumb-item active">List</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-xxl-12">
        <div class="card" id="customerList">
            <div class="card-header">
                <div class="row g-3 billings-list-table__filter">
                    <div class="col-sm-3">
                        <div class="search-box">
                            <input
                                id="searchRole"
                                type="search"
                                name="search"
                                class="form-control search billings-list-table__filter-field"
                                aria-label="Search for..."
                                placeholder="Search for..." />
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>

                    {{if .isSystemRoleGroup}}
                        <div class="col-sm-3">
                            <div class="d-flex gap-2 align-items-center" style="position: relative;z-index: 5;">
                                <label class="text-muted fw-medium m-0">Client:</label>
                                <div class="flex-1">
                                    <select
                                        name="clientId"
                                        class="form-select billings-list-table__filter-field"
                                        id="clients-choices"
                                        aria-label="Choose Client">
                                        <option value="" data-label="--- Choose Client ---"></option>
                                        {{if .clients}}
                                            {{range .clients}}
                                                <option data-label="{{.Name}}" data-icon="{{site}}/static/{{.Logo}}" value="{{.ID.Hex}}">
                                                    {{.Name}}
                                                </option>
                                            {{end}}
                                        {{end}}
                                    </select>
                                </div>
                            </div>
                        </div>
                    {{end}}

                    <div class="col-sm-3 d-flex flex-row gap-2 align-items-center">
                        <label class="text-muted flex-shrink-0 mb-0" aria-label="Status">Status:</label>
                        <select
                            name="status"
                            id="search_status"
                            class="form-control mb-0 billings-list-table__filter-field"
                            aria-label="Select Status"
                            data-choices
                            data-choices-search-false
                            data-choices-sorting-false>
                            <option value="">All</option>
                            {{range $key, $status := .statuses}}
                            <option {{if eq $status.Prefix $.defaultStatus}} selected {{end}} value="{{ $status.Prefix }}">{{ $status.Title
                                }}</option>
                            {{end}}
                        </select>
                    </div>
                    <div class="col-sm-auto ms-auto">
                        <div class="hstack gap-2">
                            <button class="btn btn-danger" id="remove-actions" data-url="{{site}}/admins/billings/api/delete"
                                data-bs-toggle="modal" data-bs-target="#delete_modal" style="display: none;">
                                <i class="ri-delete-bin-2-line"></i>
                            </button>

                            <a href="{{site}}/admins/billings/create" class="btn btn-secondary add-btn"><i class="mdi mdi-plus"></i> Top Up
                                Balance</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div>
                    <div class="table-responsive table-card mb-1">
                        <table data-ajax-url="{{site}}/api/admins/billings/list-datatable" class="table align-middle m-0 nowrap w-100"
                            id="billings-table">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th data-column-name="checkbox" style="width: 30px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select-all-records" value="1">
                                        </div>
                                    </th>
                                    <th data-column-name="action" data-key="t-action">Action</th>

                                    {{ range $column := .columns }}
                                        <th data-column-name="{{ $column.Prefix }}">{{ $column.Title }}</th>
                                    {{ end }}
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{template "layout_footer"}}

<script type="module" src="{{site}}/static/js/admins/billings/list.js"></script>