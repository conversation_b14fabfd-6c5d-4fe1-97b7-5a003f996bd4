{{template "layout_header" .}}
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">BILLING DETAIL - EDIT</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{site}}/admins/billings/list">Billings List</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-lg-12 p-4">
        <div class="card ribbon-box border shadow-none mb-lg-0 right">
            <div class="card-body text-muted">
                <span class="ribbon-three ribbon-three-success"><span class="w-100">APPROVAL</span></span>
                <div class="my-2">
                    <div class="hstack gap-3 flex-wrap justify-content-between">
{{/*                        <div class="text-muted">Top up by:*/}}
{{/*                            <span class="text-body fw-medium"></span>*/}}
{{/*                        </div>*/}}
{{/*                        <div class="vr"></div>*/}}
                        <div class="text-muted">Top up at:
                            <span class="text-body fw-medium">{{ .billing.CreatedAt }}</span>
                        </div>

                        {{if .isAdmin}}
                            {{if .isChecking}}
                                <div style="margin-right: 100px;">
                                    <button
                                        type="button"
                                        id="confirm-complete-btn"
                                        data-bill-number="{{ .billing.BillNumber }}"
                                        data-id="{{ .billing.ID }}"
                                        class="btn btn-success">Confirm Complete</button>
                                </div>
                            {{end}}
                        {{end}}
                    </div>

                    <div class="row mt-4">
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Client:</p>
                                <h5 class="fs-17 mb-0">{{ .billing.ClientName }}</h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Title:</p>
                                <h5 class="fs-17 mb-0">{{ .billing.Title }}</h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Description:</p>
                                <h5 class="fs-17 mb-0">{{ .billing.Description }}</h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Billing Type:</p>
                                <h5 class="fs-17 mb-0">{{ .billing.BillingType }}</h5>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <div>
                                    <p class="text-muted fw-medium mb-1">Bill Number:</p>
                                    <h5 class="fs-17 mb-0">{{ .billing.BillNumber }}</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Amount:</p>
                                <h5 class="fs-17 text-success mb-0">
                                    <span
                                        class="format-currency"
                                        data-currency="{{ .billing.Currency }}"
                                        data-amount="{{ .billing.Amount }}">{{ .billing.Amount}}</span>
                                </h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Balance:</p>
                                <h5 class="fs-17 text-success mb-0">
                                    <span
                                        class="format-currency"
                                        data-currency="{{ .billing.Currency }}"
                                        data-amount="{{ .billing.Balance }}">{{ .billing.Balance}}</span>
                                </h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Currency:</p>
                                <h5 class="fs-17 mb-0">{{ .billing.Currency }}</h5>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Is Active:</p>
                                <h5 class="fs-17 mb-0">{{ toHtml .activeLabelHtml }}</h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Status:</p>
                                <h5 class="fs-17 mb-0" id="status-label-html">{{ toHtml .statusLabelHtml }}</h5>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <div>
                                    <p class="text-muted fw-medium mb-1">Billing Date:</p>
                                    <h5 class="fs-17 mb-0 text-primary">{{ .billing.BillingDate }}</h5>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-sm-6">
                            <div class="p-2 border border-dashed rounded text-center">
                                <p class="text-muted fw-medium mb-1">Service Fee:</p>
                                <h5 class="fs-13 mb-0">
                                    {{ if .billing.ServiceFee }}
                                        Management: {{ .billing.ServiceFee.ManagementFee }}% |
                                        Other: {{ .billing.ServiceFee.OtherFee }}% |
                                        Platform: {{ .billing.ServiceFee.PlatformFee }}% |
                                        Technical: {{ .billing.ServiceFee.TechnicalFee }}%
                                    {{ else }}
                                        <span>---</span>
                                    {{ end }}
                                </h5>
                            </div>
                        </div>
                    </div>

                    {{if .isSystemRoleGroup}}
                        <div class="row mt-4">
                            <div class="col-lg-3 col-sm-3">
                                <div class="p-2 border border-dashed rounded text-center h-100">
                                    <p class="text-muted fw-medium mb-1">Notes:</p>
                                    {{if isEmptyString .billing.Notes}}
                                        <span>---</span>
                                    {{else}}
                                        <h5 class="fs-17 mb-0">{{ .billing.Notes }}</h5>
                                    {{end}}
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-3">
                                <div class="p-2 border border-dashed rounded text-center h-100">
                                    <p class="text-muted fw-medium mb-1">IS IPN:</p>
                                    <h5 class="fs-17 mb-0">{{ toHtml .ipnLabelHtml }}</h5>
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-3">
                                <div class="p-2 border border-dashed rounded text-center h-100">
                                    <p class="text-muted fw-medium mb-1">Approved By:</p>
                                    {{if isEmptyString .billing.ApprovedUser}}
                                        <span>---</span>
                                    {{else}}
                                        <h5 class="fs-17 mb-0">{{ .billing.ApprovedUser }}</h5>
                                    {{end}}
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-3">
                                <div class="p-2 border border-dashed rounded text-center h-100">
                                    <p class="text-muted fw-medium mb-1">Approved At:</p>
                                    {{if isEmptyString .billing.ApprovedAt}}
                                        <span>---</span>
                                    {{else}}
                                        <h5 class="fs-17 mb-0">{{ .billing.ApprovedAt }}</h5>
                                    {{end}}
                                </div>
                            </div>
                        </div>
                    {{end}}
                </div>

                <div class="row mt-3">
                    <div class="col-xxl-12">
                        <div class="card">
                            <div class="card-body">
                                <div>
                                    <div class="form-check d-none">
                                        <input class="form-check-input" type="checkbox" id="select-all-records" value="1">
                                    </div>
                                    <div class="table-responsive table-card mb-1">
                                        <table
                                            data-ajax-url="{{site}}/api/admins/billings/detail/list-datatable"
                                            data-id="{{ .billing.ID }}"
                                            data-client-id="{{ .billing.MClientID }}"
                                            data-bill-number="{{ .billing.BillNumber }}"
                                            data-currency="{{ .billing.Currency }}"
                                            class="table align-middle m-0 nowrap w-100"
                                            id="billing-detail-table">
                                            <thead class="table-light text-muted">
                                                <tr class="text-uppercase">
                                                    <th data-column-name="id">N.0</th>
                                                    <th data-column-name="adAccount">Ad account</th>
                                                    <th data-column-name="billNumber">Bill number</th>
                                                    <th data-column-name="amount">Amount</th>
                                                    <th data-column-name="balance">Balance</th>
                                                    <th data-column-name="notes">Notes</th>
                                                    <th data-column-name="billingDate">Billing date</th>
                                                    <th data-column-name="isActive">Is active</th>

                                                    {{if .isSystemRoleGroup}}
                                                        {{if .isAdmin}}
                                                            <th data-column-name="fullDropdownStatus">Status</th>
                                                        {{else}}
                                                            <th data-column-name="dropdownStatus">Status</th>
                                                        {{end}}

                                                        <th data-column-name="approvedUser">Approved by</th>
                                                    {{else}}
                                                        <th data-column-name="status">Status</th>
                                                    {{end}}
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{template "layout_footer"}}
<script src="{{site}}/static/themes/js/pages/form-input-spin.init.js" type="text/javascript"></script>
<script type="module" src="{{site}}/static/js/admins/billings/edit.js"></script>