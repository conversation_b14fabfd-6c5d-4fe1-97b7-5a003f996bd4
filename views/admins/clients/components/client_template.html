{{define "tenplate_client"}}
<script id="list_checks_btn" type="text/x-handlebars-template">
        <div class="form-check-all">
            <input class="form-check-input" type="checkbox" name="chk_child" value="{id}">
        </div>
    </script>

<script id="action_bar_dropdown" type="text/x-handlebars-template">
        <div class="dropdown">
            <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="ri-more-fill"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li >
                    <a class="dropdown-item item_edit edit-item-btn" href="{{site}}/admins/clients/edit/{id}">
                        <i class="ri-pencil-fill align-bottom"></i> Edit
                    </a>
                </li>    
                <li >
                    <div class=" dropdown-item item_delete" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" aria-label="Delete" data-bs-original-title="Delete">
                        <a id="delete-item-btn" href="#modal_delete" class="text-danger" data-id="{id}" data-bs-toggle="modal">
                            <i class="ri-delete-bin-5-fill align-bottom"></i> Remove
                        </a>
                    </div>
                </li>   
            </ul>
        </div>
    </script>

<script id="actions_datatable" type="text/x-handlebars-template">
        <div class="d-flex gap-2">
            <a href="{{site}}/admins/clients/edit/{id}"
                class="btn btn-soft-secondary btn-icon waves-effect waves-light"><i
                    class=" ri-edit-line"></i></a>
            <button type="button" title="Delete this Client" id="btn_open_modal_remove_client"  data-bs-toggle="modal" data-bs-target="#modal_confirm_remove_client"
                class="btn btn-soft-danger btn-icon waves-effect waves-light"  data-id="{id}">
                <i class="ri-delete-bin-5-fill align-bottom"></i>
            </button>
     
        </div>
        </script>
<script id="status_only_active" type="text/x-handlebars-template">
        <span class="badge badge-gradient-success text-uppercase">Active</span>
    </script>

<script id="status_active_datatable" type="text/x-handlebars-template">
        <span class="badge badge-gradient-success text-uppercase" data-id="{id}" data-status="1" data-bs-toggle="dropdown" aria-expanded="false">Active</span>
        <ul class="dropdown-menu">
            <!-- {{range $k, $v := .status}} 
                {{if ne $k 1}}
                <li><a class="dropdown-item text-capitalize change_status" href="javascript:void(0);" data-status="{{$k}}">{{$v}}</a></li>
                {{end}}
            {{end}} -->
        </ul>   
    </script>
list_checks_btn
<script id="status_inactive_datatable" type="text/x-handlebars-template">
        <span class="badge badge-gradient-info text-uppercase">Inactive</span>
    </script>

<script id="status_deleted_datatable" type="text/x-handlebars-template">
        <span class="badge badge-gradient-dark text-uppercase">Deleted</span>
    </script>

<script id="status_archive_datatable" type="text/x-handlebars-template">
        <span class="badge badge-gradient-warning text-uppercase">Archive</span>
    </script>

<script id="image_datatable" type="text/x-handlebars-template">
        <div class="d-flex align-items-center">
            <div class="flex-shrink-0">
                <img src="{{site}}{image}" alt="{name}" class="avatar-xs rounded-circle">
            </div>
            <div class="flex-grow-1 ms-2 name">
                <span>{name}</span><br>
                <small class="text-muted">{email}</small>
            </div>        
        </div>
    </script>

<script id="permission_datatable" type="text/x-handlebars-template">
        <i class="ri-shield-user-line"></i>
    </script>

{{end}}