{{template "layout_header" .}}
{{$detail := .detail}}
{{$users := .users}}
{{$users := .show.users}}
{{$productDefaultImg := .productDefaultImg}}
{{$authPermission := .authPermission}}
{{$isAdmin := .isAdmin}}
{{$userInfo := .userInfo}}
{{ $clientId := .userInfo.clientId }}

<link rel="stylesheet" href="{{site}}/static/css/pages/admins/client/edit_client.css">

<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0" data-key="t-users">Client</h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{site}}/admins/clients/list" data-key="t-users">Clients</a></li>
                    <li class="breadcrumb-item active" data-key="t-edit">Edit</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-3">
        <div class="card">
            <div class="card-body p-4">
                <div class="text-center">
                    <div class="profile-user position-relative d-inline-block mx-auto  mb-4">
                        <img src="{{site}}{{.detail.Logo}}" id="customer-img"
                            class="rounded-circle avatar-xl img-thumbnail user-profile-image show_img" alt="{{.detail.Name}}">
                        {{if hasPermission $authPermission "admins.client.api.update"}}
                        <div class="avatar-xs p-0 rounded-circle profile-photo-edit">
                            <input class="form-control d-none" name="image" value="" id="customer-image-input" type="file"
                                accept="image/png, image/gif, image/jpeg">
                            <label for="customer-image-input" class="mb-0" data-bs-toggle="tooltip" data-bs-placement="right" title=""
                                data-bs-original-title="Select Image">
                                <div class="avatar-xs cursor-pointer">
                                    <div class="ckfinder_modal avatar-title bg-light border rounded-circle text-muted">
                                        <i class="ri-image-fill"></i>
                                    </div>
                                </div>
                            </label>

                        </div>
                        {{end}}
                    </div>
                    <h5 class="fs-17 mb-1">{{.detail.Name}}</h5>
                </div>
            </div>
        </div>

        {{if hasPermission $authPermission "admins.client.api.update"}}

        <div class="card">
            <div class="card-body">
                <div class="mb-3">
                    {{if .detail.UserCreated}}
                    <div class="p-2">
                        <i class="ri-user-3-line"></i><strong> Creator: </strong> <span>{{.detail.UserCreated.FullName}}</span>
                    </div>
                    {{end}}

                    <div class="p-2">
                        <i class="las la-calendar"></i><strong> Created: </strong> <span>{{timeFormatDate .detail.CreatedAt}}</span>
                    </div>

                    {{if .detail.UserUpdated}}
                    <div class="p-2">
                        <i class="ri-user-3-line"></i><strong> Updater: </strong> <span>{{if
                            .detail.UserUpdated.FullName}}{{.detail.UserUpdated.FullName}}{{else}}-{{end}}</span>
                    </div>
                    {{end}}

                    <div class="p-2 mb-3">
                        <i class="las la-calendar"></i><strong> Updated: </strong> <span>{{timeFormatDate .detail.UpdatedAt}}</span>
                    </div>
                </div>
            </div>
        </div>
        {{end}}
    </div>
    <!--end col-->
    <div class="col-lg-9">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs-custom rounded card-header-tabs border-bottom-0" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#clientDetails" role="tab">
                            <i class="fas fa-home"></i>
                            Info Details
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#connectFacebook" role="tab">
                            <img class="icon-less" src="/static/img/meta_icon.svg" height="20px">
                            Facebook
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#connectTiktok" role="tab">
                            <img class="icon-less" src="/static/img/tiktok-icon.svg" height="20px">
                            Tiktok
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#usersClient" role="tab">
                            <!-- <i class="ri-account-box-line fs-20" height="20px"></i> -->
                            <span> Users of Client</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body p-4">
                <div class="tab-content">
                    <!-- Client Info -->
                    <div class="tab-pane active" id="clientDetails" role="tabpanel">
                        <form class="tablelist-form" method="Put" id="frm_edit_client" action="{{site}}/api/admins/clients/update"
                            enctype="multipart/form-data" autocomplete="off">
                            <fieldset {{if not (hasPermission $authPermission "admins.clients.api.update" )}} disabled {{end}}>
                                <input type="hidden" name="id" value="{{$detail.ID.Hex}}">
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="name" class="form-label">Name <span
                                                        style="color: red; font-weight: bold">*</span></label>
                                                <input type="text" name="name" value="{{$detail.Name}}" class="form-control" id="name"
                                                    placeholder="Name" value="" required="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="company" class="form-label">Company<span
                                                        style="color: red; font-weight: bold">*</span></label>
                                                <input type="text" name="company" value="{{$detail.Company}}" class="form-control"
                                                    id="company" placeholder="Company" value="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="brand" class="form-label">Brand<span
                                                        style="color: red; font-weight: bold">*</span></label>
                                                <input type="text" name="brand" value="{{if $detail.Brand}} 
                                                {{joinStrings $detail.Brand " ,"}}{{end}}" class="form-control" id="brand"
                                                    placeholder="Brand">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="position" class="form-label">Position</label>
                                                <input type="text" name="position" value="{{$detail.Position}}" class="form-control"
                                                    id="position" placeholder="Position" value="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="email" class="form-label">Email<span
                                                        style="color: red; font-weight: bold">*</span></label>
                                                <input type="email" name="email" value="{{$detail.Email}}" class="form-control" id="email"
                                                    placeholder="Email" value="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">Phone</label>
                                                <input type="text" name="phone" value="{{$detail.Phone}}" class="form-control" id="phone"
                                                    placeholder="Phone" value="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="domain_input" class="form-label">Domain</label>
                                                <input type="text" name="domain" value="{{$detail.Domain}}" class="form-control"
                                                    id="domain_input" placeholder="Domain" value="">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="address" class="form-label">Address</label>
                                                <input type="text" name="address" value="{{$detail.Address}}" class="form-control"
                                                    id="address" placeholder="Address" value="">
                                            </div>
                                        </div>
                                    </div>

                                    {{if hasPermission $authPermission "admins.client.api.update"}}
                                    <div class="col-lg-6">
                                        <div class="mb-3">
                                            <label class="form-label">Status
                                                <span class="text-danger">*</span>
                                            </label>
                                            <div class="mt-4 ms-2 mt-lg-0">
                                                {{range $k, $v := .statusCreate}}
                                                <div class="form-check form-check-inline {{if eq $k 1}} form-radio-secondary {{end}}">
                                                    <input class="form-check-input" type="radio" name="status" id="status_{{$v}}"
                                                        value="{{$v}}" {{if eq $k $detail.Status}} checked {{end}}>
                                                    <label class="form-check-label text-capitalize" style="line-height: 2.2;"
                                                        for="status_{{$v}}">{{$v}}</label>
                                                </div>
                                                {{end}}
                                            </div>
                                        </div>
                                    </div>
                                    {{end}}
                                </div>
                                {{ if hasPermission $authPermission "admins.client.api.update" }}
                                <div class="modal-footer" style="display: block;">
                                    <div class="hstack gap-2 justify-content-end">
                                        <a href="javascript:history.back()" class="btn btn-light">Close</a>
                                        <button type="submit" class="btn btn-success" id="add-btn">Save</button>
                                    </div>
                                </div>
                                {{end}}
                            </fieldset>
                        </form>
                    </div>

                    <!-- Connect Facebook -->
                    <div class="tab-pane" id="connectFacebook" role="tabpanel">
                        <form class="tablelist-form" method="Put" id="frm_edit_facebook_client"
                            action="{{site}}/api/admins/clients/facebook/update" enctype="multipart/form-data" autocomplete="off">
                            <fieldset {{if not (hasPermission $authPermission "admins.clients.api.update_facebook_resource" )}} disabled
                                {{end}}>
                                <input type="hidden" name="client_ids[]" value="{{$detail.ID.Hex}}">
                                <div class="modal-body">
                                    <div class="row">

                                        <!-- AdAccount Select -->
                                        <div class="col-lg-6">
                                            <div class="mb-3">
                                                <label for="choices-single-no-search" class="form-label  text-primary">Ad
                                                    Account</label>
                                                <select multiple name="adaccount_ids[]" class="form-select" id="adAccountChoices"
                                                    aria-label="Default select example">
                                                    <option value="" data-label="--- Choose Ad Account ---">--- Choose Ad Account ---
                                                    </option>
                                                    {{if .adAccounts}}
                                                    {{range .adAccounts}}
                                                    {{if hasPermission $authPermission "admins.client.api.update_facebook_resource"}}
                                                    <option value="{{.AccountID}}" data-label="{{.Name}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}</option>
                                                    {{else if containsID .ClientIDs $clientId}}
                                                    <option value="{{.AccountID}}" data-label="{{.Name}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}</option>
                                                    {{end}}
                                                    {{end}}
                                                    {{end}}
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Page select -->
                                        <div class="col-lg-6">
                                            <div class="mb-3">
                                                <label for="choices-single-no-search" class="form-label text-primary">Pages</label>
                                                <select multiple name="page_ids[]" class="form-select" id="pagesChoices" data-client-id=""
                                                    aria-label="Default select example">
                                                    <option value="" data-label="--- Choose Pages ---">--- Choose Pages ---</option>
                                                    {{if .pages}}
                                                    {{range .pages}}
                                                    {{if hasPermission $authPermission "admins.client.api.update_facebook_resource"}}
                                                    <option data-label="{{.GlobalBrandPageName}}" data-icon="{{.Picture.Url}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}" value="{{.PageID}}">
                                                        {{.GlobalBrandPageName}}
                                                    </option>
                                                    {{else if containsID .ClientIDs $clientId}}
                                                    <option data-label="{{.GlobalBrandPageName}}" data-icon="{{.Picture.Url}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}" value="{{.PageID}}">
                                                        {{.GlobalBrandPageName}}
                                                    </option>
                                                    {{end}}
                                                    {{end}}
                                                    {{end}}
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <!-- Pixels select -->
                                        <div class="col-lg-6">
                                            <div class="mb-3">
                                                <label for="choices-single-no-search" class="form-label text-primary">Pixels</label>
                                                <select multiple name="pixel_ids[]" class="form-select" id="pixelsChoices"
                                                    aria-label="Default select example">
                                                    <option value="" data-label="--- Choose Pixels ---">--- Choose Pixels ---</option>
                                                    {{if .pixels}}
                                                    {{range .pixels}}
                                                    {{if hasPermission $authPermission "admins.client.api.update_facebook_resource"}}

                                                    <option data-label="{{.Name}}" value="{{.PixelID}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}
                                                    </option>
                                                    {{else if containsID .ClientIDs $clientId}}
                                                    <option data-label="{{.Name}}" value="{{.PixelID}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}
                                                    </option>
                                                    {{end}}
                                                    {{end}}
                                                    {{end}}
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Catalogue select -->
                                        <div class="col-lg-6">
                                            <div class="mb-3">
                                                <label for="choices-single-no-search" class="form-label text-primary">Catalouges</label>
                                                <select multiple name="catalogue_ids[]" class="form-select" id="cataloguesChoices"
                                                    aria-label="Default select example">
                                                    <option value="" data-label="--- Choose Catalogues ---">--- Choose Catalogues ---
                                                    </option>
                                                    {{if .catalogues}}
                                                    {{range .catalogues}}
                                                    {{if hasPermission $authPermission "admins.client.api.update_facebook_resource"}}
                                                    <option data-label="{{.Name}}" value="{{.ID}}" data-product-count="{{.ProductCount}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}"
                                                        data-icon="{{if .DefaultImageURL}} {{.DefaultImageURL}} {{else}} {{site}}/static/{{$productDefaultImg}} {{end}}">
                                                        {{.Name}}
                                                    </option>
                                                    {{else if containsID .ClientIDs $clientId}}
                                                    <option data-label="{{.Name}}" value="{{.ID}}" data-product-count="{{.ProductCount}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}"
                                                        data-icon="{{if .DefaultImageURL}} {{.DefaultImageURL}} {{else}} {{site}}/static/{{$productDefaultImg}} {{end}}">
                                                        {{.Name}}
                                                    </option>
                                                    {{end}}
                                                    {{end}}
                                                    {{end}}


                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {{ if hasPermission $authPermission "admins.client.api.update_facebook_resource" }}
                                <div class="modal-footer" style="display: block;">
                                    <div class="hstack gap-2 justify-content-end">
                                        <a href="javascript:history.back()" class="btn btn-light">Close</a>
                                        <button type="submit" class="btn btn-success" id="add-btn">Save</button>
                                    </div>
                                </div>
                                {{end}}
                            </fieldset>
                        </form>
                    </div>

                    <!-- Connect Tiktok -->
                    <div class="tab-pane " id="connectTiktok" role="tabpanel">
                        <form class="tablelist-form" method="Put" id="frm_edit_tiktok_client"
                            action="{{site}}/api/admins/clients/tiktok/update" enctype="multipart/form-data" autocomplete="off">
                            <fieldset {{if not (hasPermission $authPermission "admins.clients.api.update_tiktokresource" )}} disabled
                                {{end}}>
                                <input type="hidden" name="client_ids[]" value="{{$detail.ID.Hex}}">
                                <div class="modal-body">
                                    <div class="row">

                                        <!-- AdAccount Select -->
                                        <div class="col-lg-6">
                                            <div class="mb-3">
                                                <label for="choices-single-no-search" class="form-label  text-primary">Ad
                                                    Account</label>
                                                <select multiple name="adaccount_ids[]" class="form-select" id="tiktokAdAccountChoices"
                                                    aria-label="Default select example">
                                                    <option value="" data-label="--- Choose Ad Account ---">--- Choose Ad Account ---
                                                    </option>
                                                    {{if .tiktokAdvertisers}}
                                                    {{range .tiktokAdvertisers}}
                                                    {{if hasPermission $authPermission "admins.client.api.update_tiktok_resource"}}
                                                    <option value="{{.AdvertiserID}}" data-label="{{.Name}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}</option>
                                                    {{else if containsID .ClientIDs $clientId}}
                                                    <option value="{{.AdvertiserID}}" data-label="{{.Name}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}</option>
                                                    {{end}}
                                                    {{end}}
                                                    {{end}}
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Preset column for list Table Report -->
                                        <div class="col-lg-6">
                                            <div class="mb-3">
                                                <label for="choices-single-no-search" class="form-label  text-primary">Preset column for
                                                    Campaign Table</label>
                                                <select multiple name="preset_column_ids[]" class="form-select" id="tiktokPresetColumnTable"
                                                    aria-label="Default select example">
                                                    <option value="" data-label="--- Select item ---">--- Choose preset columns ---
                                                    </option>
                                                    {{if .presetColumns}}
                                                    {{range .presetColumns}}
                                                    {{if hasPermission $authPermission "admins.client.api.update_tiktok_resource"}}
                                                    <option value="{{.ID.Hex}}" data-label="{{.Name}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}</option>
                                                    {{else if containsID .ClientIDs $clientId}}
                                                    <option value="{{.ID.Hex}}" data-label="{{.Name}}"
                                                        data-client-ids="{{objectIDsToString .ClientIDs}}">
                                                        {{.Name}}</option>
                                                    {{end}}
                                                    {{end}}
                                                    {{end}}
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                {{ if hasPermission $authPermission "admins.client.api.update_tiktok_resource" }}
                                <div class="modal-footer" style="display: block;">
                                    <div class="hstack gap-2 justify-content-end">
                                        <a href="javascript:history.back()" class="btn btn-light">Close</a>
                                        <button type="submit" class="btn btn-success" id="add-btn">Save</button>
                                    </div>
                                </div>
                                {{end}}
                            </fieldset>
                        </form>

                    </div>
                    <!-- User -->
                    <div class="tab-pane" id="usersClient" role="tabpanel">

                        {{ if hasPermission $authPermission "admins.client.api.update" }}
                        <div class="d-flex  justify-content-between align-items-center">
                            <h4 class="card-title mb-0">Role: <span class="text-primary">Users Of Client</span></h4>
                            <div class="d-flex gap-2">
                                <a class="btn btn-success add-btn" href="{{site}}/admins/users/list#create">
                                    <span data-key="t-add-user"><i class="mdi mdi-plus"></i>Add User</span>
                                </a>
                                <a class="btn btn-secondary add-btn" href="{{site}}/admins/users/list">
                                    <span data-key="t-add-user"><i class="mdi mdi-plus"></i>List Users</span>
                                </a>
                            </div>
                        </div>
                        {{end}}

                        <table class="table table-hover align-middle m-0 {{if not $isAdmin}} client-ui {{end}}" id="user_table"
                            data-user-api="{{site}}/admins/users/edit">
                        </table>

                        <ul id="users-data" class="d-none">
                            {{if .users}}
                            {{range .users}}
                            <li data-id="{{.ID.Hex}}" data-email="{{.Email}}" data-name="{{.FullName}}" data-image="{{.Image}}"
                                data-client-id="{{.ClientID.Hex}}" data-status="{{.Status}}" data-role-name="{{.Role.RoleName}}"
                                data-role-id="{{.RoleID.Hex}}" data-site="{{site}}/static">
                                {{.FullName}}
                            </li>
                            {{end}}
                            {{end}}
                        </ul>


                        <!-- end card -->
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<!--end col-->
</div>

<script id="actions_user_datatable" type="text/x-handlebars-template">
    <div class="d-flex gap-2 actions-user-table">
        <a href="{{site}}/admins/users/edit/{id}"
            class="btn btn-soft-secondary btn-icon waves-effect waves-light"><i
                class=" ri-edit-line"></i></a>
        <button type="button" title="Disconnect this User" id="btn_open_modal_disconnect"  data-bs-toggle="modal" data-bs-target="#modal_confirm_disconnect_user"
            class="btn btn-soft-danger btn-icon waves-effect waves-light" data-user-id="{id}" data-client-id="{client_id}"><i
                class="ri-link-unlink-m"></i></button>
 
    </div>
    </script>


{{template "modal_confirm_disconnect_user".}}
{{template "layout_footer" .}}

<script type="module" src="{{site}}/static/js/admins/clients/edit.js"></script>