{{define "sidebar"}}
{{$permissions := .authPermission}}
<!-- ========== App Menu ========== -->
<div class="app-menu navbar-menu">
   <!-- LOGO -->
   <div class="navbar-brand-box">
      <!-- Dark Logo-->
      <a href="/admins" class="logo logo-dark">
         <span class="logo-sm">
            <img src="{{site}}/static/images/symbol-logo-dark.svg" alt="" style="height: 2.2em">
         </span>
         <span class="logo-lg">
            <img src="{{site}}/static/images/logo-dark.png" alt="" style="height: 5.5em">
         </span>
      </a>
      <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
         <i class="ri-record-circle-line"></i>
      </button>
   </div>

   <div id="scrollbar" class="h-100">
      <div class="container-fluid">
         <div id="two-column-menu"></div>
         <ul class="navbar-nav navbar-nav-custom" id="navbar-nav">

            {{ template "menu_sidebar_admin" .}}

            {{if hasPermissions $permissions "/dsp/facebook/api/campaigns/list-datatable-POST"}}
            <li class="nav-item">
               <a class="nav-link menu-link" href="#sidebarFacebook" data-bs-toggle="collapse" role="button" aria-expanded="true"
                  aria-controls="sidebarFacebook">
                  <img class="icon-expand" src="/static/img/facebook_text.svg" height="20px">
                  <img class="icon-less" src="/static/img/meta_icon.svg" height="20px">
                  <span class="item-text">Facebook</span>
               </a>
               <div class="collapse menu-dropdown" id="sidebarFacebook">
                  <ul class="nav nav-sm flex-column">
                     <!-- <li class="nav-item" id="adAccountSidebars">
                        <a href="{{site}}/dsp/facebook/ad-account/list" class="nav-link" data-key="t-adaccount">
                           <i class="ri-account-pin-circle-line"></i>
                           Ad Account </a>
                     </li> -->
                     <li class="nav-item">
                        <a href="{{site}}/dsp/facebook/campaigns/list" class="nav-link" data-key="t-campaigns">
                           <i class=" ri-folder-chart-2-line"></i>
                           Campaigns </a>
                     </li>
                     <li class="nav-item">
                        <a href="{{site}}/dsp/facebook/adsets/list" class="nav-link" data-key="t-assets">
                           <i class=" ri-folders-line"></i>
                           Ad sets </a>
                     </li>
                     <li class="nav-item">
                        <a href="{{site}}/dsp/facebook/ads/list" class="nav-link" data-key="t-ads">
                           <i class="ri-file-chart-2-line"></i>
                           Ads </a>
                     </li>
                     <li class="nav-item d-none">
                        <a href="{{site}}/report" class="nav-link" data-key="t-report">
                           <i class="ri-newspaper-line"></i>
                           Report Ads
                        </a>
                     </li>
                     {{if hasPermission $permissions "/dsp/facebook/audiences/list-table-GET"}}
                     <li class="nav-item">
                        <a href="{{site}}/dsp/facebook/audiences/list" class="nav-link" data-key="t-projects">
                           <i class=" ri-file-search-line"></i>
                           Audiences </a>
                     </li>
                     {{end}}
                  </ul>
               </div>
            </li>
            {{end}}

            {{if hasPermissions $permissions "/dsp/tiktok/api/campaign/list-table-POST"}}
            <li class="nav-item">
               <a class="nav-link menu-link" href="#sidebarTiktok" data-bs-toggle="collapse" role="button" aria-expanded="true"
                  aria-controls="sidebarTiktok">
                  <img class="icon-expand" src="/static/img/tiktok-icon.svg" height="22px">
                  <img class="icon-less" src="/static/img/tiktok-icon.svg" height="22px" style="min-width: 30px">
                  <span class="item-text">Tiktok</span>
               </a>
               <div class="collapse menu-dropdown" id="sidebarTiktok">
                  <ul class="nav nav-sm flex-column">
                     <li class="nav-item">
                        <a href="{{site}}/dsp/tiktok/campaign/list" class="nav-link" data-key="t-campaigns">
                           <i class=" ri-folder-chart-2-line"></i>
                           Campaigns </a>
                     </li>
                     <li class="nav-item">
                        <a href="{{site}}/dsp/tiktok/adgroup/list" class="nav-link" data-key="t-assets">
                           <i class=" ri-folders-line"></i>
                           Ad Group </a>
                     </li>
                     <li class="nav-item">
                        <a href="{{site}}/dsp/tiktok/ad/list" class="nav-link" data-key="t-ads">
                           <i class="ri-file-chart-2-line"></i>
                           Ads </a>
                     </li>
                     {{ if hasPermission $permissions "/dsp/tiktok/ad-accounts/list-GET"}}
                     <li class="nav-item">
                        <a href="{{site}}/dsp/tiktok/advertiser/list" class="nav-link" data-key="t-ads">
                           <i class=" ri-account-box-line"></i>
                           Advertisers </a>
                     </li>
                     {{end}}
                  </ul>
               </div>
            </li>
            {{end}}

            <li class="nav-item">
               <a href="#sidebarDV360" class="nav-link menu-link" data-bs-toggle="collapse" role="button" aria-expanded="true"
                  aria-controls="sidebarDV360">
                  <img class="icon-expand" src="/static/img/dv360_text.svg" height="20px">
                  <img class="icon-less" src="/static/img/dv360_icon.svg" height="20px">
                  <span class="item-text">Display & Video 360</span>
               </a>
               <div class="menu-dropdown collapse" id="sidebarDV360">
                  <ul class="nav nav-sm flex-column">
                     <li class="nav-item">
                        <a href="#sidebaradv" class="nav-link collapsed" data-bs-toggle="collapse" role="button" aria-expanded="false"
                           aria-controls="sidebaradv" data-key="t-role">
                           <i class="ri-advertisement-line"></i>
                           Advertiser</a>
                        <div class="collapse menu-dropdown" id="sidebaradv">
                           <ul class="nav nav-sm flex-column">
                              <li class="nav-item">
                                 <a href="https://dv360.networldsolutions.net//advertiser" class="nav-link" data-key="t-advertiser">
                                    <i class="ri-advertisement-line"></i>
                                    Advertiser</a>
                              </li>
                              <li class="nav-item">
                                 <a href="https://dv360.networldsolutions.net//advertiser-approval" class="nav-link"
                                    data-key="t-role-create">
                                    <i class=" ri-checkbox-multiple-line"></i>
                                    Waiting Approval</a>
                              </li>
                           </ul>
                        </div>
                     </li>
                     <li class="nav-item">
                        <a href="https://dv360.networldsolutions.net//dsp/dv360/campaigns" class="nav-link" data-key="t-dv360-camp">
                           <i class=" ri-folder-chart-2-line"></i>
                           Campaigns</a>
                     </li>
                     <li class="nav-item">
                        <a href="https://dv360.networldsolutions.net//dsp/dv360/creatives" class="nav-link" data-key="t-dv360-creatives">
                           <i class="ri-pencil-ruler-2-line"></i>
                           Creatives</a>
                     </li>
                     <li class="nav-item">
                        <a href="https://dv360.networldsolutions.net//dsp/dv360/reports" class="nav-link" data-key="t-dv360-creatives">
                           <i class="ri-newspaper-line"></i>
                           Reports</a>
                     </li>
                  </ul>

               </div>
            </li>
            
         </ul>
      </div>
      <!-- Sidebar -->
   </div>

   <div class="sidebar-background"></div>
</div>

{{end}}