{{template "layout_header" .}}
<div class="row">
    <div class="col-lg-12">
        <div class="card mb-0">
            <div class="card-header d-flex justify-content-between">
                <div class="flex-1">
                    <ul class="nav-tab-table-camp-adset-ad nav nav-tabs-custom nav_cus rounded card-header-tabs border-bottom-0"
                        role="tablist">
                        <li class="nav-item" style="width: auto;">
                            <a data-navigo-href="dsp/facebook/campaigns/list" href="#campaign"
                                data-reload="{{site}}/dsp/facebook/api/campaigns/reload"
                                class="nav-link d-flex justify-content-between align-items-center active" data-bs-toggle="tab"
                                data-tab="campaigns" role="tab">
                                <span class="d-flex align-items-center gap-1 fs-15">
                                    <i class="ri-input-method-line fs-18"></i> Campaigns
                                </span>
                                <button type="button" class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill ms-2"
                                    id="camp_selected">
                                    <i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="camp-selected-close"></i>
                                    0 selected
                                </button>
                            </a>
                        </li>
                        <li class="nav-item" style="width: auto;">
                            <a data-navigo-href="dsp/facebook/adsets/list" href="#adset"
                                data-reload="{{site}}/dsp/facebook/api/adsets/reload"
                                class="nav-link d-flex justify-content-between align-items-center" data-bs-toggle="tab" data-tab="adsets"
                                role="tab">
                                <span class="d-flex align-items-center gap-1 fs-15">
                                    <i class="ri-dashboard-line fs-18"></i> <span id="selected_adset_name">Ad set</span>
                                </span>
                                <button type="button" class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill  ms-2"
                                    id="adset_selected">
                                    <i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="adset-selected-close"></i>
                                    0 selected
                                </button>
                            </a>
                        </li>
                        <li class="nav-item" style="width: auto;">
                            <a data-navigo-href="dsp/facebook/ads/list" href="#ad" data-reload="{{site}}/dsp/facebook/api/ads/reload"
                                class="nav-link d-flex justify-content-between align-items-center" data-bs-toggle="tab" data-tab="ads"
                                role="tab">
                                <span class="d-flex align-items-center gap-1 fs-15">
                                    <i class="ri-advertisement-line fs-18"></i> <span id="selected_ad_name">Ads</span>
                                </span>
                                <button type="button" class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill  ms-2"
                                    id="ad_selected">
                                    <i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="ads-selected-close"></i>
                                    0 selected
                                </button>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="flex-shrink-0" id="filter-page">
                    <div id="filter-search-datetime">
                        <i class=" ri-calendar-2-line"></i>&nbsp <span></span> <i class="ri-arrow-down-s-fill"></i>
                    </div>
                </div>
            </div>
            <div class="card-body">

                <div class="campaigns-page">
                    <div class="card-title header-table mb-0 pb-2 d-flex align-items-center justify-content-between">
                        {{ template "header_table_left" .}}
                        {{ template "header_table_right" }}
                    </div>
                    <div class="tab-content">
                        <div class="tab-pane active" id="campaign" role="tabpanel">
                            <!-- Search campaign -->
                            <div class="input-group input-group-sm mb-2" style="width: 320px;margin-top: -25px;padding-bottom: 15px;">
                                <input type="text" class="form-control" id="valueSearchCampaign"
                                    placeholder="Search campaign by Name or Id">
                                <button class="btn btn-primary" type="button" id="btnSearchCampaign">Search <i
                                        class="ri-search-2-line align-middle"></i></button>
                            </div>
                            <!-- End Search campaign -->
                            <div class="table-responsive table-card">
                                <table id="data-table-campaign"
                                    class="table table-bordered table-cus table-hover table-striped align-middle table-nowrap"
                                    style="width: 100%;">

                                </table>
                            </div>
                        </div>
                        <div class="tab-pane" id="adset" role="tabpanel">
                            <div class="table-responsive table-card">
                                <table id="data-table-adset"
                                    class="table table-cus table-bordered table-hover table-striped align-middle table-nowrap"
                                    style="width: 100%;">

                                </table>
                            </div>
                        </div>
                        <div class="tab-pane" id="ad" role="tabpanel">
                            <div class="table-responsive table-card">
                                <table id="data-table-ad"
                                    class="table table-cus table-bordered table-hover table-striped align-middle table-nowrap"
                                    style="width: 100%;">

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{ template "modal_reload_camp" }}
            {{ template "component_camp_html" }}
        </div>
    </div>
</div>


<template id="titleFootTableHtml">
    <th colspan="2" class="dtfc-fixed-left" style="position: sticky;left: 0;background-color: white;">Results from {rowCount} {title}</th>
</template>

<template id="NullFootTableHtml">
    <th class="align-top">
        <div class=" d-flex flex-column align-items-end fw-normal">
            <span class="text-dark">-</span>
        </div>
    </th>
</template>
<template id="totalFootTableHtml">
    <th class="align-top">
        <div class=" d-flex flex-column align-items-end fw-normal">
            <span class="text-dark">{value}</span>
            <span class="text-muted fs-10">{titleTotal}</span>
        </div>
    </th>
</template>

{{template "modals_campaign_page"}}
{{template "offcanvas_camp_adset_ad_page"}}
{{template "layout_footer"}}
<script src="{{site}}/static/js/router/navigo.min.js"></script>
<script src="{{site}}/static/js/router/index.js" type="module" defer></script>
<script src="{{site}}/static/js/pages/ad/index.js" type="module" defer></script>
<!-- <script src="{{site}}/static/js/pages/campaigns/index.js" type="module" defer></script> -->