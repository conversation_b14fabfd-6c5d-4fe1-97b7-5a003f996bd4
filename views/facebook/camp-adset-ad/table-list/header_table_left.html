{{ define "header_table_left" }}
{{ $authPermission := .authPermission }}
<div class="header-table-left d-flex flex-wrap align-items-center gap-2" id="listTableLeftHeader">
    {{if hasPermission $authPermission "dsp/facebook/api/campaigns/create-POST"}}
    <button id="btn-create-camp-adset-ad" type="button"
        class="btn btn-info rounded btn-sm btn-animation waves-effect waves-light d-flex align-items-center gap-1" data-bs-toggle="modal"
        data-bs-target="#modal-create-campaign">
        <i class="ri-add-fill align-middle fs-16"></i> Create
    </button>

    <button class="btn btn-light btn-sm bg-gradient waves-effect" id="delete-item-btn" data-bs-target="" data-bs-toggle="modal">
        <i class="ri-delete-bin-line label-icon align-middle fs-16"></i>
    </button>
    {{end}}

    <!-- <div class="btn-group material-shadow">
        <button type="button" class="btn btn-light rounded-4 material-shadow-none btn-sm d-flex align-items-center gap-1 fs-14"><i
                class="ri-file-copy-line fs-16"></i> Duplicate</button>
        <button type="button" class="btn btn-light rounded-4 material-shadow-none btn-sm dropdown-toggle dropdown-toggle-split"
            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="#">Action</a>
            <a class="dropdown-item" href="#">Another action</a>
            <a class="dropdown-item" href="#">Something else here</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="#">Separated link</a>
        </div>
    </div> -->

    <!-- <div class="btn-group material-shadow">
        <button type="button" class="btn btn-light rounded-4 material-shadow-none btn-sm d-flex align-items-center gap-1 fs-14"><i
                class="ri-pencil-line fs-16"></i> Edit</button>
        <button type="button" class="btn btn-light rounded-4 material-shadow-none btn-sm dropdown-toggle dropdown-toggle-split"
            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="#">Action</a>
            <a class="dropdown-item" href="#">Another action</a>
            <a class="dropdown-item" href="#">Something else here</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="#">Separated link</a>
        </div>
    </div> -->
    <!-- Buttons Remove Camp with Label -->

    {{if hasPermission $authPermission "admins/clients/edit-GET"}}
    <span class="mx-2">|</span>
    <div class="d-flex gap-2" id="LTLHAdminControlEl">
        <div class="d-flex gap-2 align-items-center" style="position: relative;z-index: 5;min-width: 370px;">
            <label class="fw-medium text-primary m-0">Client:</label>
            <div class="flex-1">
                <!-- <select class="flex-1" name="listTableSearchUser" id="LTLHAdminControlUserList">
                </select> -->
                <!-- Page select -->
                <select name="clientI" class="form-select" id="clientsChoices" aria-label="Default select example">
                    <option value="" data-label="--- Choose Client ---"></option>
                    {{if .clients}}
                    {{range .clients}}
                    <option data-label="{{.Name}}" data-icon="{{site}}/static/{{.Logo}}" value="{{.ID.Hex}}">
                        {{.Name}}
                    </option>
                    {{end}}
                    {{end}}
                </select>
            </div>
        </div>
        <!-- <button id="LTLHAdminControlBtnApprove" type="button"
            class="btn btn-info rounded btn-sm btn-animation waves-effect waves-light align-items-center gap-1" data-bs-toggle="modal"
            data-bs-target="#modal_confirm_approve_camp">
            <i class="ri-check-line label-icon align-middle fs-16"></i> Approve
        </button> -->
    </div>
    <div class="d-flex gap-2" id="LTLHAdminControlEl">
        <div class="d-flex gap-2 align-items-center" style="position: relative;z-index: 5;min-width: 370px;">
            <label class="fw-medium text-primary m-0">Ad Account:</label>
            <div class="flex-1">
                <select name="adaccount_id" class="form-select" id="adAccountChoices" aria-label="Default select example">
                    <option value="" data-label="--- Choose Ad Account ---">--- Choose Ad Account ---
                    </option>
                    {{if .adAccounts}}
                    {{range .adAccounts}}
                    <option value="{{.AccountID}}" data-label="{{.Name}}" data-client-ids="{{objectIDsToString .ClientIDs}}">
                        {{.Name}}</option>
                    {{end}}
                    {{end}}
                </select>
            </div>
        </div>
        <button id="LTLHAdminControlBtnApprove" type="button"
            class="btn btn-success rounded btn-sm btn-animation waves-effect waves-light align-items-center gap-1" data-bs-toggle="modal"
            data-bs-target="#modal_confirm_approve_camp">
            <i class="ri-check-line label-icon align-middle fs-16"></i> Approve
        </button>
    </div>
    <!-- AdAccount Select -->
    <script>
        window.onload = function () {
            const pixelChoices = new Choices("#adAccountChoices", {
                shouldSort: false,
                allowHTML: true,
                maxItemCount: 1,
                removeItemButton: true,
                renderSelectedChoices: "auto",
                placeholder: true,
                placeholderValue: "--- Choose AdAccount ---",
                searchEnabled: true,
            });
        };
    </script>

    {{end}}

    <!-- <div class="btn-group">
        <button type="button" class="btn btn-light dropdown-toggle btn-sm fs-14" data-bs-toggle="dropdown" aria-haspopup="true"
            aria-expanded="false">More</button>
        <div class="dropdown-menu">
            <a class="dropdown-item" href="#">Action</a>
            <a class="dropdown-item" href="#">Another action</a>
            <a class="dropdown-item" href="#">Something else here</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="#">Separated link</a>
        </div>
    </div> -->

</div>

{{end}}