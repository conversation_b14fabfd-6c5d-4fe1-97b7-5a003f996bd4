{{define "modal_create_custom_audience_website"}}
<!-- Default Mo<PERSON>s -->
<div id="modal_create_custom_audience_website" class="modal fade" tabindex="-1" data-bs-backdrop="static"
  aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title flex-1" id="myModalLabel" name="title">
          Create a website Custom Audience
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-header">
        <span>Include Accounts Centre accounts who meet
          <select class="" aria-label="accounts-select">
            <option value="ANY">ANY</option>
            <option value="ALL">ALL</option>
          </select>
          of the following criteria:</span>
      </div>
      <div class="modal-body">
        <form onsubmit="return false;" id="created_website_form">
          <div class="border mx-2">
            <div class="mx-4 mb-4">
              <div id="pixel_blk" style="display: block">
                <label for="pixel_id" class="mt-3"> Source </label>
                <div class="d-flex gap-2">
                  <select id="pixel-field" data-is-get="true"
                    class="form-control select-flag-templating select2-hidden-accessible"
                    name="promoted_object[pixel_id]" required></select>
                </div>
              </div>

              <div id="event_blk" style="display: block">
                <label for="event_select" class="mt-3">Events</label>
                <div class="d-flex gap-2">
                  <div class="custom-dropdown" data-dropdown-label="">
                    <div class="custom-dropdown-toggle">
                      <span class="custom-dropdown-label" id="event_field"></span>
                      <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                    </div>
                    <div class="custom-dropdown-menu">
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="ALL_VISITORS" data-label="All website visitors"
                          checked />
                        <div><span class="fs-6">All website visitors</span><br>
                          <small>Includes people who have visited any of your websites.</small>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="VISITORS_BY_URL"
                          data-label="People who visited specific web pages" />
                        <div><span class="fs-6">People who visited specific web pages</span><br>
                          <small>Includes people who have visited specific websites or web pages.</small>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="TOP_TIME_SPENDERS"
                          data-label="Visitors by time spent" />
                        <div><span class="fs-6">Visitors by time spent</span><br>
                        </div>
                      </label>
                      <span class="ms-2 mb-1 fs-6 fw-bold">From your events</span>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="PageView" data-label="PageView" disabled />
                        <div><span class="fs-6">PageView</span><br>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="ViewContent" data-label="ViewContent" disabled />
                        <div><span class="fs-6">ViewContent</span><br>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="AddToCart" data-label="AddToCart" disabled />
                        <div><span class="fs-6">AddToCart</span><br>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="Search" data-label="Search" disabled />
                        <div><span class="fs-6">Search</span><br>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="Purchase" data-label="Purchase" disabled />
                        <div><span class="fs-6">Purchase</span><br>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="InitiateCheckout" data-label="InitiateCheckout"
                          disabled />
                        <div><span class="fs-6">InitiateCheckout</span><br>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="AddPaymentInfo" data-label="AddPaymentInfo"
                          disabled />
                        <div><span class="fs-6">AddPaymentInfo</span><br>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-between">
                <div id="audience_retention_blk" class="col-4" style="display: block">
                  <label for="audience_retention_field" class="mt-3">Audience retention</label>
                  <div class="d-flex gap-2">
                    <input type="number" name="audience_retention" value="30" id="audience_retention_field"
                      class="form-control align-middle" required max="180" />
                    <span class="align-self-center">days</span>
                  </div>
                </div>
                <div id="percentile_blk" class="col-4" style="display:none">
                  <label for="percentile_field" class="mt-3">
                    Percentile
                  </label>
                  <div class="custom-dropdown">
                    <div class="custom-dropdown-toggle">
                      <span class="custom-dropdown-label"></span>
                      <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                    </div>
                    <div class="custom-dropdown-menu">
                      <label class="custom-dropdown-item">
                        <input type="radio" name="percentile" value="25" data-label="25%" checked />
                        <div><span class="fs-6">25%</span></div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="percentile" value="10" data-label="10%" />
                        <div><span class="fs-6">10%</span></div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="percentile" value="5" data-label="5%" />
                        <div><span class="fs-6">5%</span></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div id="selected-specific-web-btn" class="align-middle text-primary mt-3" style="display:none">
                <i class="ri-add-line"></i> Select specific web page(s)
              </div>

              <div id="url_blk" class="mt-4">
                <div id="url_blk_1" class="mb-0 bg-light rounded-3 block">
                  <div class="mx-3 mb-4">
                    <div class="d-flex">
                      <div class="col-11">
                        <div class="d-flex py-3">
                          <div class="d-flex gap-2 col-6">
                            <div class="custom-dropdown disabled">
                              <div class="custom-dropdown-toggle left">
                                <span class="custom-dropdown-label"></span>
                                <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                              </div>
                              <div class="custom-dropdown-menu">
                                <label class="custom-dropdown-item">
                                  <input type="radio" name="url_1" value="" data-label="URL" checked />
                                  <div><span class="fs-6">URL</span></div>
                                </label>
                              </div>
                            </div>
                          </div>
                          <div class="d-flex gap-2 col-6">
                            <div class="custom-dropdown">
                              <div class="custom-dropdown-toggle right">
                                <span class="custom-dropdown-label"></span>
                                <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                              </div>
                              <div class="custom-dropdown-menu">
                                <label class="custom-dropdown-item">
                                  <input type="radio" name="url_field_1" value="i_contains" data-label="contains"
                                    checked />
                                  <div><span class="fs-6">contains</span></div>
                                </label>
                                <label class="custom-dropdown-item">
                                  <input type="radio" name="url_field_1" value="i_not_contains"
                                    data-label="doesn't contain" />
                                  <div><span class="fs-6">doesn't contain</span></div>
                                </label>
                                <label class="custom-dropdown-item">
                                  <input type="radio" name="url_field_1" value="eq" data-label="equals" />
                                  <div><span class="fs-6">equals</span></div>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="pb-3">
                          <input class="choices-text-remove-button form-control block-input" name="url_value_1"
                            type="text" placeholder="At least one of these values" />
                        </div>
                      </div>
                      <div class="col-1 px-4 py-3">
                        <i class="ri-close-line text-muted fs-16 cursor-pointer remove-block"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div id="and-also-btn" class="align-middle text-primary mb-3 mt-n2" style="display:none">
                <i class="ri-add-line"></i> And also
              </div>

              <div id="aggrerated_blk" class="mb-0 bg-light rounded-3" style="display: none;">
                <div class="mx-3 mb-4">
                  <div class="d-flex">
                    <div class="col-11">
                      <label for="" class="mt-3">
                        Aggregated value <span class="fs-4 text-primary align-middle">∙</span>
                        <span class="text-primary">Optional</span>
                      </label>
                      <div class="d-flex pb-4">
                        <div class="col-4" name="aggrerated_blk_col" id="aggrerated_blk_col_1">
                          <div class="custom-dropdown">
                            <div class="custom-dropdown-toggle left">
                              <span class="custom-dropdown-label"></span>
                              <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                            </div>
                            <div class="custom-dropdown-menu">
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type" value="frequency" data-label="Frequency"
                                  checked />
                                <div><span class="fs-6">Frequency</span></div>
                              </label>
                              <hr>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type" value="sum_of" data-label="Sum of" />
                                <div><span class="fs-6">Sum of</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type" value="avarage_of" data-label="Avarage of" />
                                <div><span class="fs-6">Avarage of</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type" value="minimum_of" data-label="Minimum of" />
                                <div><span class="fs-6">Minimum of</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type" value="maximum_of" data-label="Maximum of" />
                                <div><span class="fs-6">Maximum of</span></div>
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="col-4" name="aggrerated_blk_col" id="aggrerated_blk_col_2" style="display: none;">
                          <div class="custom-dropdown disabled">
                            <div class="custom-dropdown-toggle middle">
                              <span class="custom-dropdown-label"></span>
                              <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                            </div>
                            <div class="custom-dropdown-menu">
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_parameter" value="" data-label="Select parameter"
                                  checked />
                                <div><span class="fs-6">Select parameter</span></div>
                              </label>

                            </div>
                          </div>
                        </div>
                        <div class="col-4" name="aggrerated_blk_col" id="aggrerated_blk_col_3">
                          <div class="custom-dropdown">
                            <div class="custom-dropdown-toggle middle">
                              <span class="custom-dropdown-label"></span>
                              <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                            </div>
                            <div class="custom-dropdown-menu">
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type_operation" value=""
                                  data-label="is greater than or equal to (≥)" checked />
                                <div><span class="fs-6">is greater than or equal to (≥)</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type_operation" value=""
                                  data-label="is greater than (>)" />
                                <div><span class="fs-6">is greater than (>)</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type_operation" value="" data-label="equals" />
                                <div><span class="fs-6">equals</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type_operation" value=""
                                  data-label="doesn't equal" />
                                <div><span class="fs-6">doesn't equal</span></div>
                              </label>
                              <label class="custom-dropdown-item">
                                <input type="radio" name="aggreation_type_operation" value=""
                                  data-label="is less than or equal to (≤)" />
                                <div><span class="fs-6">is less than or equal to (≤)</span></div>
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="col-4" name="aggrerated_blk_col" id="aggrerated_blk_col_4">
                          <input type="number" name="aggrerate_value" value="" placeholder="Add a value" min="1"
                            id="aggrerate_value_field" class="form-control align-middle input_right"
                            style="padding:10px">
                        </div>
                      </div>
                    </div>
                    <div class="col-1 px-4 py-3">
                      <i class="ri-close-line text-muted fs-16 cursor-pointer remove-block"
                        data-target="aggrerated_blk"></i>
                    </div>
                  </div>
                </div>
              </div>

              <div id="device_blk" class="mb-0 bg-light rounded-3 " style="display: none;">
                <div class="mx-3 mb-4">
                  <div class="d-flex">
                    <div class="col-11">
                      <label for="" class="mt-3">
                        Device <span class="fs-4 text-primary align-middle">∙</span>
                        <span class="text-primary">Optional</span>
                      </label>
                      <div>
                        <div class="custom-dropdown pb-3">
                          <div class="custom-dropdown-toggle left">
                            <span class="custom-dropdown-label"></span>
                            <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                          </div>
                          <div class="custom-dropdown-menu">
                            <label class="custom-dropdown-item">
                              <input type="radio" name="device_selection" value="ALL" data-label="All" checked />
                              <div><span class="fs-6">All</span></div>
                            </label>
                            <hr>
                            <label class="custom-dropdown-item">
                              <input type="radio" name="device_selection" value="ALL_MOBILE_DEVICES"
                                data-label="All mobile devices" />
                              <div><span class="fs-6">All mobile devices</span>
                                <br><small>This includes smartphones and tablets</small>
                              </div>
                            </label>
                            <label class="custom-dropdown-item">
                              <input type="radio" name="device_selection" value="IOS" data-label="iOS" />
                              <div><span class="fs-6">iOS</span></div>
                            </label>
                            <label class="custom-dropdown-item">
                              <input type="radio" name="device_selection" value="ANDROID" data-label="Android" />
                              <div><span class="fs-6">Android</span></div>
                            </label>
                            <hr>
                            <label class="custom-dropdown-item">
                              <input type="radio" name="device_selection" value="DESKTOP" data-label="Desktop" />
                              <div><span class="fs-6">Desktop</span></div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-1 px-4 py-3">
                      <i class="ri-close-line text-muted fs-16 cursor-pointer remove-block"
                        data-target="device_blk"></i>
                    </div>
                  </div>
                </div>
              </div>

              <div id="furthur_refine_dropdown" class="dropdown">
                <button class="btn border dropdown-toggle" type="button" data-bs-toggle="dropdown"
                  aria-expanded="false">
                  Furthur refine by
                </button>
                <div class="dropdown-menu" id="dropdownMenuFurthurRefine">
                  <li class="dropdown-item refine-option" data-target="aggrerated_blk">Aggregated Value</li>
                  <li class="dropdown-item refine-option" data-target="device_blk">Device</li>
                </div>
              </div>

            </div>



          </div>
          <hr class="mx-2 mb-0">
          <div id="audience_name_blk" class="mx-2" style="display: block">
            <label for="audience_name_field" class="mt-3">Audience name</label>
            <div class="input-group">
              <input type="text" name="audience_name" id="audience_name_field" class="form-control pr-5 border-end-0"
                maxlength="50" required data-maxlength="50" data-count-id="char_count_name" />
              <span class="input-group-text text-muted border-start-0 bg-transparent" id="char_count_name">0/50</span>
            </div>
          </div>
          <div id="audience_description_blk" class="mx-2" style="display: block">
            <label for="audience_description_field" class="mt-3">
              Description <span class="fs-4 text-primary align-middle">∙</span>
              <span class="text-primary">Optional</span>
            </label>
            <div class="input-group">
              <input type="text" name="audience_description" id="audience_description_field"
                class="form-control pr-5 border-end-0" maxlength="100" data-maxlength="100"
                data-count-id="char_count_description" />
              <span class="input-group-text text-muted border-start-0 bg-transparent"
                id="char_count_description">0/100</span>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-close-modal" data-bs-dismiss="modal" name="btn_back">
          Back
        </button>
        <button disabled type="button" name="btn_submit" class="btn btn-primary btn-next"
          id="submit_website_audience_form_btn">
          Create an audience
        </button>
      </div>
    </div>
    <!-- /.modal-content -->
  </div>
  <!-- /.modal-dialog -->
</div>
<!-- /.modal -->
{{end}}