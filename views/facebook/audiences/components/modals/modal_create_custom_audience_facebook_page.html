{{define "modal_create_custom_audience_facebook_page"}}
<!-- Default Modals -->
<div id="modal_create_custom_audience_facebook_page" class="modal fade" tabindex="-1" aria-labelledby="myModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title flex-1" id="myModalLabel" name="title">
          Create a Facebook Page Custom Audience
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-header">
        <span>Include Accounts Centre accounts who meet
          <select class="" aria-label="accounts-select">
            <option value="ANY">ANY</option>
            <option value="ALL">ALL</option>
          </select>
          of the following criteria:</span>
      </div>
      <div class="modal-body">
        <form onsubmit="return false;" id="created_facebook_page_form">
          <div class="border mx-2">
            <div class="mx-4 mb-4">
              <div id="fb_page_blk">
                <label for="page_id" class="mt-3"> Page </label>
                <div class="d-flex gap-2">
                  <select id="fb-page-field" data-is-get="true"
                    class="form-control select-flag-templating select2-hidden-accessible"
                    name="promoted_object[page_id]" required></select>
                </div>
              </div>
              <div id="fb_event_blk">
                <label for="event_select" class="mt-3">Events</label>
                <div class="d-flex gap-2">
                  <div class="custom-dropdown">
                    <div class="custom-dropdown-toggle">
                      <span class="custom-dropdown-label" id="fb_event_field"></span>
                      <span class="custom-dropdown-icon"><i class="ri-arrow-down-s-fill fs-3"></i></span>
                    </div>
                    <div class="custom-dropdown-menu">
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="page_engaged"
                          data-label="Everyone who engaged with your Page" checked />
                        <div><span class="fs-6">Everyone who engaged with your Page</span><br>
                          <small>Includes people who have visited your Page or taken an action on a post or ad, such as
                            reactions, shares, comments, link clicks or carousel swipes.</small>
                        </div>
                      </label>
                      <label class="custom-dropdown-item">
                        <input type="radio" name="event_select" value="page_visited"
                          data-label="Anyone who visited your Page" />
                        <div><span class="fs-6">Anyone who visited your Page</span><br>
                          <small>This includes anyone who visited your Page, regardless of the actions they
                            took.</small>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div id="fb_audience_retention_blk">
                <label for="event_select" class="mt-3">Audience retention</label>
                <div class="d-flex gap-2 w-25">
                  <input type="number" name="audience_retention" value="365" id="fb_audience_retention_field"
                    class="form-control align-middle" required max="365" min="1" />
                  <span class="align-self-center">days</span>
                </div>
              </div>
            </div>
          </div>
          <hr class="mx-2 mb-0">
          <div id="fb_audience_name_blk" class="mx-2">
            <label for="audience_name_field" class="mt-3">Audience name</label>
            <div class="input-group">
              <input type="text" name="audience_name" id="fb_audience_name_field" class="form-control pr-5 border-end-0"
                maxlength="50" data-maxlength="50" data-count-id="fb_char_count_name" required />
              <span class="input-group-text text-muted border-start-0 bg-transparent"
                id="fb_char_count_name">0/50</span>
            </div>
          </div>
          <div id="fb_audience_description_blk" class="mx-2">
            <label for="audience_name_field" class="mt-3">
              Description <span class="fs-4 text-primary align-middle">∙</span>
              <span class="text-primary">Optional</span>
            </label>
            <div class="input-group">
              <input type="text" name="audience_description" id="fb_audience_description_field"
                class="form-control pr-5 border-end-0" maxlength="100" data-maxlength="100"
                data-count-id="fb_char_count_description" />
              <span class="input-group-text text-muted border-start-0 bg-transparent"
                id="fb_char_count_description">0/100</span>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light btn-close-modal" data-bs-dismiss="modal" name="btn_back">
          Back
        </button>
        <button disabled type="button" class="btn btn-primary btn-next" id="submit_facebook_page_audience_form_btn"
          name="btn_submit">
          Create an audience
        </button>
      </div>
    </div>

    <!-- /.modal-content -->
  </div>
  <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

{{end}}