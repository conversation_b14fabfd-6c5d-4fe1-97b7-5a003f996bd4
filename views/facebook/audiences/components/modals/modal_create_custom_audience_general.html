{{define "modal_create_custom_audience_general"}}
<!-- Default Mo<PERSON> -->
<div id="modal_create_custom_audience_general" class="modal fade" tabindex="-1" data-bs-backdrop="static"
  aria-labelledby="custom_audience_title" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title flex-1" id="custom_audience_title" name="title">
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form onsubmit="return false;" id="custom_audience_general_form" name="create_form">
          <div id="audience_source_blk" class="mx-2">
            <div id="audience_source_include">
              <span id="audience_source_include_title">Include Accounts Centre accounts who meet
                <select class="" aria-label="accounts-select" id="audience_source_include_account_select">
                  <option value="ANY">ANY</option>
                  <option value="ALL">ALL</option>
                </select>
                of the following criteria:</span>
              <div id="audience_source_include_blk_container">

              </div>
            </div>
            <div id="audience_source_include_exclude_btn_group">
              <button class="btn btn-light" type="button" id="audience_source_include_btn">
                Include people</button>
              <button class="btn btn-light" type="button" id="audience_source_exclude_btn">
                Exclude people</button>
            </div>
            <div id="audience_source_exclude" class="mx-2 mt-4">
              <span id="audience_source_exclude_title">
                <i class="ri-indeterminate-circle-fill"></i> Exclude <a
                  href="https://adsmanager.facebook.com/adsmanager/audiences?act=***************&business_id=***************#"
                  _target="_blank">people</a> who meet any of the following criteria:
              </span>
              <div id="audience_source_exclude_blk_container">

              </div>
            </div>
          </div>

          <hr class="mx-2 mb-0">
          <div class="alert alert-danger alert-left-border alert-dismissible fade show mb-xl-0 mt-2" role="alert"
            name="audience_alert" style="display:none;">
            <i class="ri-error-warning-line me-3 align-middle fs-16 text-danger"></i><span name="alert_message">
              You can't create an audience that combines <b>people who currently like or follow your Page</b> with other
              options in
              the
              drop-down. You can create multiple Facebook Page Custom Audiences and use them together in your ad set
              instead.</span>
          </div>

          <div id="audience_name_blk" class="mx-2" style="display: block">
            <label for="audience_name_field" class="mt-3">Audience name</label>
            <div class="input-group">
              <input type="text" name="audience_name" id="audience_name_field" class="form-control pr-5 border-end-0"
                maxlength="50" required data-maxlength="50" data-count-id="char_count_name" />
              <span class="input-group-text text-muted border-start-0 bg-transparent" name="count">0/50</span>
            </div>
          </div>
          <div id="audience_description_blk" class="mx-2" style="display: block">
            <label for="audience_description_field" class="mt-3">
              Description <span class="fs-4 text-primary align-middle">∙</span>
              <span class="text-primary">Optional</span>
            </label>
            <div class="input-group">
              <input type="text" name="audience_description" id="audience_description_field"
                class="form-control pr-5 border-end-0" maxlength="100" data-maxlength="100"
                data-count-id="char_count_description" />
              <span class="input-group-text text-muted border-start-0 bg-transparent" name="count">0/100</span>
            </div>
          </div>
        </form>
      </div>
      <div class=" modal-footer">
        <button type="button" class="btn btn-light btn-close-modal" data-bs-dismiss="modal"
          data-bs-target="#modal_create_custom_audience" data-bs-toggle="modal" name="btn_back">
          Back
        </button>
        <button type="button" name="btn_submit" class="btn btn-primary btn-next">
          Create an audience
        </button>
      </div>
    </div>
    <!-- /.modal-content -->
  </div>
  <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<script id="custom_audience_website" type="text/x-handlebars-template">
    <div class="border my-3 position-relative" name="source_blk">
      <button type="button" class="btn-close position-absolute" style="top: 0.65rem; right: 0.65rem;"
        name="source-block-remove" aria-label="Close">
      </button>
      <div class="mx-4 mb-4">
        <div name="pixel_blk" style="display: block">
          <label class="mt-3"> Source </label>
          <div class="d-flex gap-2">
            <select data-role="pixel-select" data-is-get="true" class="form-control" name="pixel_select" required></select>
          </div>
        </div>
        <div name="event_blk" style="display: block">
          <label for="event_select" class="mt-3">Events</label>
          <select name="event_select" class="form-select">
          </select>
        </div>
    
        <div class="d-flex justify-content-between">
          <div name="audience_retention_blk" class="col-4" style="display: block">
            <label for="audience_retention_field" class="mt-3">Audience retention</label>
            <div class="d-flex gap-2">
              <input type="number" name="audience_retention" value="30" name="audience_retention_field"
                class="form-control align-middle" required max="180" />
              <span class="align-self-center">days</span>
            </div>
          </div>
          <div name="percentile_blk" class="col-4" style="display:none">
            <label for="percentile_field" class="mt-3">
              Percentile
            </label>
            <select name="percentile" class="form-select" aria-label="Percentile select">
              <option value="25" selected>25%</option>
              <option value="10">10%</option>
              <option value="5">5%</option>
            </select>
          </div>
        </div>
    
        <div name="selected-specific-web-btn" class="align-middle text-primary mt-3" style="display:none">
          <i class="ri-add-line"></i> Select specific web page(s)
        </div>
    
        <div name="url_blk" class="mt-4">
          <div class="url-blk-container">
    
          </div>
        </div>
    
        <button type="button" name="and-also-btn" class="btn btn-outline-light text-primary cursor-pointer w-auto"
          style="display:none">
          <i class="ri-add-line"></i> And also
        </button>
    
        <div name="aggrerated_blk" class="mb-0 bg-light rounded-3" style="display: none;">
          <div class="mx-3 mb-4">
            <div class="d-flex">
              <div class="col-11">
                <label for="" class="mt-3">
                  Aggregated value <span class="fs-4 text-primary align-middle">∙</span>
                  <span class="text-primary">Optional</span>
                </label>
                <div class="d-flex pb-4">
                  <div name="aggrerated_blk_col">
                    <select name="aggreation_type" class="form-select h-100" aria-label="Aggregation Type">
                      <option value="frequency" selected>Frequency</option>
                      <option value="" disabled>-----------------</option>
                      <option value="sum_of">Sum of</option>
                      <option value="avarage_of">Avarage of</option>
                      <option value="minimum_of">Minimum of</option>
                      <option value="maximum_of">Maximum of</option>
                    </select>
                  </div>
                  <div name="aggrerated_blk_col">
                    <select name="aggreation_parameter" class="form-select  h-100" disabled
                      aria-label="Aggregation Parameter">
                      <option value="" selected>Custom data</option>
                    </select>
                  </div>
                  <div col="col-4" name="aggrerated_blk_col">
                    <input type="text" name="custom_data" value="custom_data" placeholder="Custom data" min="1"
                      id="aggrerate_value_field" class="form-control align-middle input_right" style="padding:10px">
                  </div>
                  <div name="aggrerated_blk_col">
                    <select name="aggreation_type_operation" class="form-select h-100"
                      aria-label="Aggregation Type Operation">
                      <option value="" selected>is greater than or equal to (≥)</option>
                      <option value="">is greater than (>)</option>
                      <option value="">equals</option>
                      <option value="">doesn't equal</option>
                      <option value="">is less than or equal to (≤)</option>
                    </select>
                  </div>
                  <div name="aggrerated_blk_col">
                    <input type="number" name="aggrerate_value" value="" placeholder="Add a value" min="1"
                      id="aggrerate_value_field" class="form-control align-middle input_right" style="padding:10px">
                  </div>
                </div>
              </div>
              <div class="col-1 px-4 py-3">
                <i class="ri-close-line text-muted fs-16 cursor-pointer remove-block" data-target="aggrerated_blk"></i>
              </div>
            </div>
          </div>
        </div>
    
        <div name="device_blk" class="mb-0 bg-light rounded-3 " style="display: none;">
          <div class="mx-3 mb-4">
            <div class="d-flex">
              <div class="col-11">
                <label for="" class="mt-3">
                  Device <span class="fs-4 text-primary align-middle">∙</span>
                  <span class="text-primary">Optional</span>
                </label>
                <div class="pb-4">
                  <select name="device_selection" class="form-select" aria-label="Device Selection">
    
                  </select>
                </div>
              </div>
              <div class="col-1 px-4 py-3">
                <i class="ri-close-line text-muted fs-16 cursor-pointer remove-block" data-target="device_blk"></i>
              </div>
            </div>
          </div>
        </div>
    
        <div name="furthur_refine_dropdown" class="dropdown">
          <button class="btn border dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
            Furthur refine by
          </button>
          <div class="dropdown-menu" name="dropdownMenuFurthurRefine">
            <li class="dropdown-item refine-option" data-target="aggrerated_blk">Aggregated Value</li>
            <li class="dropdown-item refine-option" data-target="device_blk">Device</li>
          </div>
        </div>
    
      </div>
    </div>
  </script>

<script id="custom_audience_facebook_page" type="text/x-handlebars-template">
    <div class="border my-3 position-relative" name="source_blk">
        <button type="button" class="btn-close position-absolute" style="top: 0.65rem; right: 0.65rem;"
          name="source-block-remove" aria-label="Close">
        </button>
        <div class="mx-4 mb-4">
            <div name="fb_page_blk">
                <label for="page_id" class="mt-3"> Page </label>
                <div class="d-flex gap-2">
                    <select name="page_select" data-role="facebook-page-select" data-is-get="true"
                        class="form-control select-flag-templating select2-hidden-accessible" required></select>
                </div>
            </div>
            <div name="event_blk">
                <label for="event_select" class="mt-3">Events</label>
                <select name="event_select" class="form-select">
                </select>
            </div>
            <div name="audience_retention_blk">
                <label for="event_select" class="mt-3">Audience retention</label>
                <div class="d-flex gap-2 w-25">
                    <input type="number" name="audience_retention" value="365" id="fb_audience_retention_field"
                        class="form-control align-middle" required max="365" min="1" />
                    <span class="align-self-center">days</span>
                </div>
            </div>
        </div>
    </div>
  </script>
{{end}}