{{template "layout_header" .}}
{{ $authPermission := .authPermission }}
<link rel="stylesheet" href="{{site}}/static/themes/libs/no-ui-slider/noUiSlider.min.css" />

<div class="row">
  <div class="col-12">
    <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
      <h4 class="mb-sm-0">Ad Acount</h4>
      <div class="page-title-right">
        <ol class="breadcrumb m-0">
          <li class="breadcrumb-item">
            <a href="javascript: void(0);">Audience</a>
          </li>
          <li class="breadcrumb-item active">List</li>
        </ol>
      </div>
    </div>
  </div>
</div>


<div class="row">
  <div class="col-xl-3 col-lg-4">{{ template "list_filter_audience" }}</div>
  <div class="col-xl-9 col-lg-8">
    <div>
      <div class="card">
        <div class="card-header border-0">
          <div class="row g-4">
            <div class="col-sm-auto">
              <div class="d-flex gap-3">
                <div class="dropdown">
                  <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton1"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    Create Audience
                  </button>
                  <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                    <li>
                      <button class="dropdown-item" data-bs-toggle="modal"
                        data-bs-target="#modal_create_custom_audience">
                        <i class="ri-group-line text-muted fs-16 align-middle me-1"></i>
                        <span class="align-middle">Custom Audience</span>
                      </button>
                    </li>

                    <li>
                      <button class="dropdown-item" data-bs-toggle="modal"
                        data-bs-target="#modal_create_lookalike_audience">
                        <i class="ri-group-line text-muted fs-16 align-middle me-1"></i>
                        <span class="align-middle">Lookalive Audience</span>
                      </button>
                    </li>
                  </ul>
                </div>
                <button class="btn btn-light btn-sm bg-gradient waves-effect" id="delete-item-btn"
                  data-bs-target="#modal_confirm_audience" data-bs-toggle="modal">
                  <i class="ri-delete-bin-line label-icon align-middle fs-16"></i>
                </button>

                {{if hasPermission $authPermission "admins/clients/edit-GET"}}

                <div class="d-flex gap-2 align-items-center" style="position: relative;z-index: 5;min-width: 370px;">
                  <label class="fw-medium text-primary m-0">Client:</label>
                  <div class="flex-1">
                    <select name="clientI" class="form-select" id="clientsChoices" aria-label="Default select example">
                      <option value="" data-label="--- Choose Client ---">--- Choose Client ---</option>
                      {{if .clients}}
                      {{range .clients}}
                      <option data-label="{{.Name}}" data-icon="{{site}}/static/{{.Logo}}" value="{{.ID.Hex}}">
                        {{.Name}}
                      </option>
                      {{end}}
                      {{end}}
                    </select>
                  </div>

                  <div class="d-flex gap-2 align-items-center" style="position: relative;z-index: 5;min-width: 370px;">
                    <label class="fw-medium text-primary m-0">Ad Account:</label>
                    <div class="flex-1">
                      <select name="adaccount_id" class="form-select" id="adAccountChoices"
                        aria-label="Default select example">
                        <option value="" data-label="--- Choose Ad Account ---">--- Choose Ad Account ---
                        </option>
                        {{if .adAccounts}}
                        {{range .adAccounts}}
                        <option value="{{.AccountID}}" data-label="{{.Name}}"
                          data-client-ids="{{objectIDsToString .ClientIDs}}">
                          {{.Name}}</option>
                        {{end}}
                        {{end}}
                      </select>
                    </div>
                  </div>
                </div>


                <script>
                  window.onload = function () {
                    const pixelChoices = new Choices("#clientsChoices", {
                      shouldSort: false,
                      allowHTML: true,
                      maxItemCount: 1,
                      removeItemButton: true,
                      renderSelectedChoices: "auto",
                      placeholder: true,
                      placeholderValue: "--- Choose client ---",
                      searchEnabled: true,
                    });
                  };

                  window.onload = function () {
                    const pixelChoices = new Choices("#adAccountChoices", {
                      shouldSort: false,
                      allowHTML: true,
                      maxItemCount: 1,
                      removeItemButton: true,
                      renderSelectedChoices: "auto",
                      placeholder: true,
                      placeholderValue: "--- Choose ad acccount ---",
                      searchEnabled: true,
                    });
                  };
                </script>
                {{end}}
              </div>
            </div>
            <div class="col-sm">
              <div class="d-flex justify-content-sm-end">
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#modal_reload_audience">
                  <i class="ri-refresh-line me-1 align-bottom"></i> Reload
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- end card header -->
        <div class="card-body">
          <div>{{ template "list_datatable_audience"}}</div>
        </div>
      </div>
    </div>
  </div>
</div>


{{template "modal_reload_audience"}} {{template "modal_custom_source_audience"}}
{{template "modal_create_lookalike_audience"}} {{template "layout_footer"}}
{{template "modal_create_custom_audience"}} {{template "modal_confirm_audience"}}

{{template "modal_create_custom_audience_website"}} {{template "modal_create_custom_audience_facebook_page"}}
{{template "modal_create_custom_audience_general"}}
<script type="module" src="{{site}}/static/themes/libs/no-ui-slider/noUiSlider.min.js"></script>
<script type="module" src="{{site}}/static/js/pages/audiences/list.js"></script>
<script type="module" src="{{site}}/static/js/pages/audiences/modal_create_custom_audience.js"></script>