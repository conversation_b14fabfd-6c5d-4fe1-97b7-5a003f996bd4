package pages

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/facebook/iface"
	"godsp/views/v2/layouts"
	"godsp/views/v2/layouts/masters"
)

type HomeLayoutData struct {
	AuthPermission map[string]int `json:"auth_permission" `
	UserInfo       iface.UserInfoAuth
}

func getDataLayoutMaster(data *HomeLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}

templ HomePages(data *HomeLayoutData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{cssHeader()}, scriptHomePage()) {
		@HomePageContent(data)
	}
}

templ cssHeader() {
	//<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css" />
}

templ scriptHomePage() {
	<script type="module" defer>
      function getPeriodOfDay(date = new Date()) {
         const hour = date.getHours();

         if (hour >= 5 && hour < 12) {
            return "Morning";
         } else if (hour >= 12 && hour < 17) {
            return "Afternoon";
         } else if (hour >= 17 && hour < 21) {
            return "Evening";
         } else {
            return "Night";
         }
      }

      const HomeJs = (function () {
         const ID_HOME = "homePage";
         const titleHello = $(`#${ID_HOME} #titleHello`);

         function renderTitleWelcome() {
            const userInfo = JSON.parse(localStorage.getItem("user_info"));
            if (userInfo) {
               const userName = userInfo.first_name || userInfo.last_name;
               titleHello.html(`Good ${getPeriodOfDay()}, ${userName}!`);
            }
         }
         return {
            renderTitleWelcome,
         };
      })();

      $(document).ready(function () {
         HomeJs.renderTitleWelcome();
      });
   </script>
}

templ HomePageContent(data *HomeLayoutData) {
	<div class="row" id="homePage">
		<div class="col-12">
			<div class="d-flex align-items-lg-center flex-lg-row flex-column mb-3">
				<div class="flex-grow-1">
					<h4 class="fs-16 mb-1" id="titleHello">Good Morning, Anna!</h4>
					<p class="text-muted mb-0">
						Here's what's happening with your facebook ads
						today.
					</p>
				</div>
				<div class="mt-3 mt-lg-0">
					<form action="javascript:void(0);">
						<div class="row g-3 mb-0 align-items-center">
							<!--end col-->
							// <div class="col-auto">
							// 	<a href={ templates.SafeURL("/dsp/facebook/campaigns/list") }>
							// 		<button type="button" class="btn btn-primary">
							// 			<i class="ri-add-circle-line align-middle me-1"></i>
							// 			Add Campaigns
							// 		</button>
							// 	</a>
							// </div>
							<!--end col-->
							<div class="col-auto d-none">
								<button type="button" class="btn btn-soft-secondary btn-icon waves-effect waves-light layout-rightside-btn">
									<i
										class="ri-pulse-line"
									></i>
								</button>
							</div>
							<!--end col-->
						</div>
						<!--end row-->
					</form>
				</div>
			</div>
		</div>
		<div class="col-12 d-none">
			<div class="row">
				<div class="col-xl-6 col-md-6">
					<!-- card -->
					<div class="card card-animate">
						<div class="card-body">
							<div class="d-flex align-items-center">
								<div class="flex-grow-1 overflow-hidden">
									<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Performance Summary last 7 days</p>
								</div>
								<div class="flex-shrink-0">
									<h5 class="text-success fs-14 mb-0">
										<i class="ri-arrow-right-up-line fs-13 align-middle"></i> +16.24 %
									</h5>
								</div>
							</div>
							<div class="row mt-3">
								<div class="col-4">
									<h6>Reach</h6>
									<h2>328.4K</h2>
									<a href={ templates.SafeURL("/dsp/facebook/campaigns/list") } class="text-decoration-underline">View more</a>
								</div>
								<div class="col-4 text-center">
									<h6>Cost per 1,000 impressions</h6>
									<h2>₫81.8K</h2>
									<a href={ templates.SafeURL("/dsp/facebook/campaigns/list") } class="text-decoration-underline">View more</a>
								</div>
								<div class="col-4 text-right">
									<h6>Clicks</h6>
									<h2>₫75.8K</h2>
									<a href={ templates.SafeURL("/dsp/facebook/campaigns/list") } class="text-decoration-underline">View more</a>
								</div>
								<!-- <div class="col-4 text-right">
                        <h6>Amount spent</h6>
                        <h2>₫75.8M</h2>
                        <a href="{{site}}/dsp/facebook/campaigns/list" class="text-decoration-underline">View more</a>
                     </div> -->
							</div>
						</div><!-- end card body -->
					</div><!-- end card -->
				</div><!-- end col -->
				<div class="col-xl-3 col-md-6">
					<!-- card -->
					<div class="card card-animate">
						<div class="card-body">
							<div class="d-flex align-items-center">
								<div class="flex-grow-1 overflow-hidden">
									<p class="text-uppercase fw-medium text-muted text-truncate mb-0">Totals Active Campaigns</p>
								</div>
								<div class="flex-shrink-0">
									<h5 class="text-primary fs-14 mb-0">
										<i class="ri-stack-line fs-13 align-middle"></i> 20
									</h5>
								</div>
							</div>
							<div class="d-flex align-items-end justify-content-between mt-3">
								<div class="col-4">
									<h6>Amount spent</h6>
									<h2>₫75.8M</h2>
									<a hhref={ templates.SafeURL("/dsp/facebook/campaigns/list") } class="text-decoration-underline">View more</a>
								</div>
								<div class="avatar-sm flex-shrink-0">
									<span class="avatar-title bg-soft-primary rounded fs-3">
										<i class="bx bx-user-circle text-primary"></i>
									</span>
								</div>
							</div>
						</div><!-- end card body -->
					</div><!-- end card -->
				</div><!-- end col -->
				<div class="col-xl-3 col-md-6">
					<!-- card -->
					<div class="card card-animate">
						<div class="card-body">
							<div class="d-flex align-items-center">
								<div class="flex-grow-1 overflow-hidden">
									<p class="text-uppercase fw-medium text-muted text-truncate mb-0">My Balance</p>
								</div>
								<div class="flex-shrink-0">
									<h5 class="text-success fs-14 mb-0">
										<i class="ri-arrow-right-up-line fs-13 align-middle"></i> +16.24 %
									</h5>
								</div>
							</div>
							<div class="d-flex align-items-end justify-content-between mt-3">
								<div>
									<h6>current</h6>
									<h2>₫75.8M</h2>
									<a href={ templates.SafeURL("admins/billings/create") }>Add Funds</a>
								</div>
								<div class="avatar-sm flex-shrink-0">
									<span class="avatar-title bg-soft-info rounded fs-3">
										<i class="bx bx-wallet text-info"></i>
									</span>
								</div>
							</div>
						</div><!-- end card body -->
					</div><!-- end card -->
				</div><!-- end col -->
			</div>
		</div>
		<div class="col-12">
			<div class="row">
				<div class="col-4">
					<!-- card -->
					<div class="card card-animate">
						<div class="card-body">
							<div class="d-flex align-items-center">
								<div class="flex-grow-1 overflow-hidden">
									<p class="text-uppercase text-truncate mb-0 fw-semibold text-primary">
										<img class="icon-less me-1" src={ templates.AssetURL("/static/img/meta_icon.svg") } height="20px"/>
										Facebook Advertisement
									</p>
								</div>
								<div class="flex-shrink-0">
									<h5 class="fs-12 mb-0">
										<span>Current advertiser: </span>
										<span class="badge badge-soft-info fs-12">
											<i
												class="ri-user-shared-line fs-13 align-middle me-1"
											></i>12,000
										</span>
									</h5>
								</div>
							</div>
							<img class="mt-2" src={ templates.AssetURL("/static/images/homepage/facebook-banner.png") } style="max-width: 100%;border-radius: 5px;"/>
							<div class="d-flex align-items-end justify-content-between mt-2">
								<div>
									<a href={ templates.SafeURL("/dsp/facebook/campaigns/list") } class="btn btn-soft-primary fw-medium">View dashboard</a>
								</div>
							</div>
						</div><!-- end card body -->
					</div><!-- end card -->
				</div>
				<div class="col-4">
					<!-- card -->
					<div class="card card-animate">
						<div class="card-body">
							<div class="d-flex align-items-center">
								<div class="flex-grow-1 overflow-hidden">
									<p class="text-uppercase text-truncate mb-0 fw-semibold text-primary">
										<img class="icon-less me-1" src={ templates.AssetURL("/static/img/dv360_icon.svg") } height="20px"/>
										Display & Video 360
									</p>
								</div>
								<div class="flex-shrink-0">
									<h5 class="fs-12 mb-0">
										<span>Current advertiser: </span>
										<span class="badge badge-soft-info fs-12">
											<i
												class="ri-user-shared-line fs-13 align-middle me-1"
											></i>15,900
										</span>
									</h5>
								</div>
							</div>
							<img class="mt-2" src={ templates.AssetURL("/static/images/homepage/dv360_banner.png") } style="max-width: 100%;border-radius: 5px;"/>
							<div class="d-flex align-items-end justify-content-between mt-2">
								<div>
									<a target="_blank" href="https://dv360.networldsolutions.net/" class="btn btn-soft-success fw-medium">
										View
										dashboard
									</a>
								</div>
							</div>
						</div><!-- end card body -->
					</div><!-- end card -->
				</div>
				<div class="col-4">
					<!-- card -->
					<div class="card card-animate">
						<div class="card-body">
							<div class="d-flex align-items-center">
								<div class="flex-grow-1 overflow-hidden">
									<p class="text-uppercase text-truncate mb-0 fw-semibold text-primary">
										<img class="icon-less me-1" src={ templates.AssetURL("/static/img/admin_icon.svg") } height="20px"/>
										KOL/KOC Dashboard
									</p>
								</div>
								<div class="flex-shrink-0">
									<h5 class="fs-12 mb-0">
										<span>KOL/KOC: </span>
										<span class="badge badge-soft-info fs-12">
											<i
												class="ri-user-shared-line fs-13 align-middle me-1"
											></i>25,000
										</span>
									</h5>
								</div>
							</div>
							<img class="mt-2" src={ templates.AssetURL("/static/images/homepage/kol_banner.png") } style="max-width: 100%;border-radius: 5px;"/>
							<div class="d-flex align-items-end justify-content-between mt-2">
								<div>
									<a target="_blank" href="https://kol.brancherx.com" class="btn btn-soft-info fw-medium">View dashboard</a>
								</div>
							</div>
						</div><!-- end card body -->
					</div><!-- end card -->
				</div>
			</div>
		</div>
	</div>
}
