package components

import "godsp/modules/tiktok/campaign/transport/responses"

templ LeftMenuBarCpn(camp *responses.DetailCampRes, activeID string) {
	<li class="nav-item nav-lv1">
		<div class={ "nav-item-blk " + getActiveClass(camp.CampaignID, activeID) } status="" data-id={ camp.CampaignID }>
			<div class="nav-link">
				<i class="ri-folder-fill"></i>
				<span id="campaignOffCanvasCampaignNameLeftBar">{ camp.CampaignName }</span>
			</div>
			<div class="btn-group dropdown">
				<button
					type="button"
					class="btn waves-effect waves-light border-0"
					id=""
					data-bs-toggle="popover"
					data-bs-html="true"
					data-bs-title="Actions for this Campaign"
					data-bs-container="body"
					data-bs-trigger="focus"
					data-bs-custom-class="popover-left-sidebar-menu-action"
					data-bs-content=""
				>
					<i class="ri-more-line ms-auto"></i>
				</button>
			</div>
		</div>
		<ul class="nav flex-column">
			for _, adgroup := range camp.Adgroups {
				<li class="nav-item nav-lv2">
					<div class={ "nav-item-blk " + getActiveClass(adgroup.AdgroupID, activeID) } status="" data-id={ adgroup.AdgroupID }>
						<div class="nav-link child-level-1">
							<i class="ri-function-line"></i>
							<span class="adgroup-name">{ adgroup.AdgroupName }</span>
						</div>
						<div class="btn-group dropdown">
							<button
								type="button"
								class="btn waves-effect waves-light border-0"
								id=""
								data-bs-toggle="popover"
								data-bs-html="true"
								data-bs-title="Actions for this Adgroup"
								data-bs-container="body"
								data-bs-trigger="focus"
								data-bs-custom-class="popover-left-sidebar-menu-action"
								data-bs-content=""
							>
								<i class="ri-more-line ms-auto"></i>
							</button>
						</div>
					</div>
					<ul class="nav flex-column">
						for _, ad := range adgroup.Ads {
							<li class="nav-item nav-lv3">
								<div class={ "nav-item-blk " + getActiveClass(ad.AdID, activeID) } status="draft" data-id={ ad.AdID }>
									<div class="nav-link child-level-2">
										<i class="ri-profile-line"></i>
										<span class="ad-name">{ ad.AdName }</span>
									</div>
									<div class="btn-group dropdown">
										<button
											type="button"
											class="btn waves-effect waves-light border-0"
											id=""
											data-bs-toggle="popover"
											data-bs-html="true"
											data-bs-title="Actions for this Ad"
											data-bs-container="body"
											data-bs-trigger="focus"
											data-bs-custom-class="popover-left-sidebar-menu-action"
											data-bs-content=""
										>
											<i class="ri-more-line ms-auto"></i>
										</button>
									</div>
								</div>
							</li>
						}
					</ul>
				</li>
			}
		</ul>
	</li>
}

func getActiveClass(id string, activeID string) string {
	if id == activeID {
		return "active"
	}
	return ""
}
