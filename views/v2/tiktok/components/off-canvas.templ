package components 

import "github.com/dev-networldasia/dspgos/gos/templates"

templ OffCanvasCpn() {
	<div
		class="offcanvas offcanvas-end offcanvas-medium-content casa-offcanvas"
		data-bs-backdrop="static"
		data-bs-scroll="false"
		tabindex="-1"
		id="OffCanvasCampAdgroupAd"
		aria-labelledby="OffCanvasCampAdgroupAd"
		style="width:90%"
	>
		<div
			class="d-flex flex-row position-absolute translate-middle-y ms-n4 mt-3"
			style="left: -13px; top: 38px;"
		>
			<button type="button" data-bs-dismiss="offcanvas" aria-label="Close" class="btn btn-light btn-sm waves-effect waves-light rounded-0 rounded-start">
				<i class="ri-close-fill fw-bold align-middle fs-18"></i>
			</button>
			<!-- ghost Buttons -->
		</div>
		<div class="offcanvas-body p-0 bg-light">
			// <div class="d-flex flex-row" style="position: sticky; top: 0; height: 100vh;">
			// 	<div class="bg-light" style="width:60px">
			// 		<div class="button-groups p-2">
			// 			<!-- ghost Buttons -->
			// 			<button type="button" data-bs-dismiss="offcanvas" aria-label="Close" class="btn btn-soft-dark waves-effect waves-light"><i class="ri-close-fill"></i></button><!-- ghost Buttons -->
			// 		</div>
			// 	</div>
			// </div>
			<div class="content-offcanvas flex-1 w-100">
				<div class="sidebar-offcanvas-blk">
					<div class="casa-blk">
						<div class="search-bar app-search p-2">
							<div class="position-relative">
								<input type="text" class="form-control" placeholder="Search by Name or ID" autocomplete="off" id="searchCampAdsetAdByNameId" value=""/>
								<span class="mdi mdi-magnify search-widget-icon"></span>
								<span class="mdi mdi-close-circle search-widget-icon search-widget-icon-close d-none" id="search-close-options"></span>
							</div>
						</div>
						<ul class="nav flex-column casa-items-list" id="menu-camp-adgroup-ad-left-side-bar"></ul>
					</div>
				</div>
				<div class="data-blk" id="tiktok-offcanvas-content"></div>
			</div>
		</div>
	</div>
	@scriptOffCanvas()
}

templ scriptOffCanvas() {
	<script type="module" src={ templates.AssetURL("/static/js/tiktok/ads/off-canvas/index.js") } defer></script>
	<script type="module">
		import identityModule from "/static/js/tiktok/ads/off-canvas/components/_identity.js";
		import adDetailsModule from "/static/js/tiktok/ads/off-canvas/components/_ad_details.js";
		import { renderAdOffCanvasContent } from "/static/js/tiktok/ads/off-canvas/index.js";
		import campaignOffCanvasModule from "/static/js/tiktok/campaigns/off-canvas/index.js";
		import adgroupOffCanvasModule from "/static/js/tiktok/adgroups/off-canvas/index.js";

		const ID_OFFCANVAS = "OffCanvasCampAdgroupAd";
		const offcanvasEl = document.getElementById(ID_OFFCANVAS);

		function handleAdOffCanvas() {
			renderAdOffCanvasContent();
			console.log("🚀 Rendered Ad OffCanvas");
		}

		function handleAdgroupOffCanvas() {
			adgroupOffCanvasModule.renderAdgroupOffCanvasContent();
			console.log("🚀 Rendered Adgroup OffCanvas");
		}

		function handleCampaignOffCanvas() {
			campaignOffCanvasModule.renderCampaignOffCanvasContent();
			console.log("🚀 Rendered Campaign OffCanvas");
		}

		function handleOffcanvasShow() {
			if (getParamURL("edit_ad_id")) handleAdOffCanvas();
			else if (getParamURL("edit_adgroup_id")) handleAdgroupOffCanvas();
			else if (getParamURL("edit_campaign_id")) handleCampaignOffCanvas();
		}

		function handleOffcanvasHide() {
			const router = new Navigo("/");

			if (window.offcanvasOriginURL) {
				console.log("🔙 Returning to origin:", window.offcanvasOriginURL);
				router.navigate(window.offcanvasOriginURL);
				window.offcanvasOriginURL = null;
				return;
			}

			if (getParamURL("edit_ad_id")) {
				removeParamURL("edit_ad_id");
				router.navigate(`/dsp/tiktok/ad/list?&${getCurrentQuery()}`);
			} else if (getParamURL("edit_adgroup_id")) {
				removeParamURL("edit_adgroup_id");
				router.navigate(`/dsp/tiktok/adgroup/list?&${getCurrentQuery()}`);
			} else if (getParamURL("edit_campaign_id")) {
				removeParamURL("edit_campaign_id");
				router.navigate(`/dsp/tiktok/campaign/list?&${getCurrentQuery()}`);
			}
		}

		function onSearchLeftMenuSidebar() {
			const onInput = debounce(function () {
				const searchValue = this.value.trim().toLowerCase();
				const isNumericSearch = /^\d+$/.test(searchValue);
				console.log(" 🚀 ~ isNumericSearch:", isNumericSearch)
				const leftMenuBar = $(".sidebar-offcanvas-blk #menu-camp-adgroup-ad-left-side-bar");

				leftMenuBar.find(".nav-lv2 .nav-item-blk .adgroup-name, .nav-lv3 .nav-item-blk .ad-name").each(function () {
						const $this = $(this);
						const $navItem = $this.closest(".nav-item");
						const $navLv2 = $this.closest(".nav-item.nav-lv2");

						if (!searchValue) return $navItem.show(200);

						const text = $this.text().toLowerCase();
						// console.log(" 🚀 ~ text:", text)
						const idItem = $this.closest(".nav-item-blk").data("id");

						const isMatch = isNumericSearch ? idItem == searchValue : text.includes(searchValue);
						$navItem.toggle(isMatch);

						if (isMatch && $navLv2.length > 0) {
							$navLv2.show(200);
						}
				});
			}, 300);
			$(document).on("input", "#searchCampAdsetAdByNameId", onInput);
		}

		document.addEventListener("DOMContentLoaded", () => {
			if (getParamURL("edit_ad_id") || getParamURL("edit_adgroup_id") || getParamURL("edit_campaign_id")) {
				const bsOffcanvas = new bootstrap.Offcanvas(offcanvasEl);
				bsOffcanvas.show();
			}

			onSearchLeftMenuSidebar();
			offcanvasEl.addEventListener("shown.bs.offcanvas", handleOffcanvasShow);
			offcanvasEl.addEventListener("hidden.bs.offcanvas", handleOffcanvasHide);
		});
	</script>
}
