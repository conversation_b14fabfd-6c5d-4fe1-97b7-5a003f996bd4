package campaign

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/tiktok/campaign/response"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	"godsp/views/v2/tiktok/campaign/components"
)

templ CreateCampaign(data *response.ListCampaignResp) {
	{{
		dataLayoutMaster := masters.LayoutMasterData{
			AuthPermission: data.AuthPermission,
			UserInfo:       data.UserInfo,
		}
		pathBreadcrumb := []layoutCops.PathBreadcrumb{
			{Title: "Campaign", DataKey: "t-campaign", Url: "/dsp/tiktok/adgroup/create"},
		}
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{head()}, scriptCampaign()) {
		@layoutCops.ListBreadcrumdCpn("Campaign", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@components.FlashMsgCpn(data.FlashMsg)
		}
		<div class="container mt-4 tiktok">
			<div class="row">
				<div class="col-lg-12">
					<div class="card shadow-sm">
						<div class="campaign-container">
							<div class="campaign-creation-content">
								<ul class="nav nav-tabs nav-tabs-custom nav-success nav-justified mb-3" role="tablist">
									<li class="nav-item" role="presentation">
										<a
											class="nav-link active"
											data-bs-toggle="tab"
											href="#home1"
											role="tab"
											aria-selected="true"
										>
											Create new
										</a>
									</li>
									<li class="nav-item" role="presentation">
										<a
											class="nav-link"
											data-bs-toggle="tab"
											href="#profile1"
											role="tab"
											aria-selected="false"
											tabindex="-1"
										>
											Use existing
										</a>
									</li>
								</ul>
								<div class="tab-content text-muted">
									<div class="tab-pane active show" id="home1" role="tabpanel">
										<div class="sales-switch-box">
											<div class="alert alert-success material-shadow" role="alert">
												<span>
													<strong>Sales objective update </strong> Website conversions and product
													sales are
													now available in the new combined sales objective.
												</span>
												<a href="#" class="alert-link float-end">Switch back </a>
											</div>
										</div>
										<div class="row m-0 objective-container">
											<div class="objective-selector-title">Advertising objective</div>
											<div class="col-md-4">
												@components.CampaignMenuObjective(nil)
											</div>
											<div class="col-md-8">
												<!-- Content for Reach -->
												@components.CampaignREACH(nil)
												<!-- Content for Traffic -->
												@components.CampaignTRAFFIC(nil)
												<!-- Content for Video Views -->
												@components.CampaignVIDEOVIEWS(nil)
												<!-- Community interaction -->
												@components.CampaignCOMMUNITYINTERACTION(nil)
												<!-- App promotion -->
												@components.CampaignAPPPROMOTION(nil)
												<!-- Lead generation -->
												@components.CampaignLEADGENERATION(nil)
												<!-- Sales -->
												@components.CampaignSALES(nil)
											</div>
										</div>
										<div class="row m-0 mb-3 objective-container setting mt-3">
											<div class="objective-selector-title mb-3">Settings</div>
											<div class="col-md-6 d-flex flex-column gap-2">
												@components.CampaignName(nil)
												@components.CampaignBudget(nil)
											</div>
										</div>
									</div>
									<div class="tab-pane" id="profile1" role="tabpanel">
										<div class="row m-0 mb-3 objective-container setting mt-3">
											<div class="col-md-8 name">
												<label for="">Select campaign</label>
												<select class="form-select mb-3" aria-label="Default select example">
													<option value="1">Campagin test 1</option>
													<option value="2">Campagin test 2</option>
													<option value="3">Campagin maybi test 1</option>
												</select>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="card-footer d-flex justify-content-between">
							<button type="button" class="btn btn-outline-secondary">Exit</button>
							<button type="button" class="btn btn-secondary continue">Continue</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	}
}

templ head() {
	<link href={ templates.AssetURL("/static/css/pages/tiktok/campaign.css") } rel="stylesheet" type="text/css"/>
}

templ scriptCampaign() {
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/campaign/tiktokCampaignLoader.js") }></script>
	<script>
		document.addEventListener("DOMContentLoaded", function () {
			numberFormat(".number-format");
		});
	</script>
}
