package campaign

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/tiktok/campaign/response"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	"godsp/views/v2/tiktok/campaign/components"
)

templ ListCampaign(data *response.ListCampaignResp) {
	{{
dataLayoutMaster := masters.LayoutMasterData{
	AuthPermission: data.AuthPermission,
	UserInfo:       data.UserInfo,
}
pathBreadcrumb := []layoutCops.PathBreadcrumb{
	{Title: "Campaign", DataKey: "t-campaign", Url: "/dsp/tiktok/adgroup/list"},
}
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{head()}, scriptCampaignList()) {
		@layoutCops.ListBreadcrumdCpn("Campaign", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@components.FlashMsgCpn(data.FlashMsg)
		}
		<div class="row">
			<div class="col-lg-12">
				<div class="card">
					<div class="card-header border-0"></div>
					<div class="card-body">
						<div>
							<div class="table-responsive table-card mb-1">
								<table
									class="table table-hover align-middle m-0"
									id="user_table"
									style="min-height: 200px;width:100%"
								>
									<thead class="table-light text-muted">
										<tr class="text-uppercase">
											<th style="width: 20px;">
												<div class="form-check">
													<input
														class="form-check-input"
														type="checkbox"
														id="checkAll"
														value="option"
													/>
												</div>
											</th>
											<th data-key="t-action">Action</th>
											<th data-key="t-advertiser-id">ID</th>
											<th data-key="t-fullname">Full name</th>
											<th data-key="t-status">Status</th>
											<th data-key="t-audit-info">Audit Info</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<div class="form-check">
													<input
														class="form-check-input"
														type="checkbox"
														value=""
														name="campaign_id"
													/>
												</div>
											</td>
											<td>
												<!-- Placement Offcanvas -->
												<a
													href="javascript: void(0);"
													class="btn btn-sm btn-primary edit-btn"
													type="button"
													data-bs-toggle="offcanvas"
													data-id="1830449340619809"
													data-bs-target="#campaignOffCanvas"
													aria-controls="campaignOffCanvas"
												>Edit</a>
											</td>
											<td>1830449340619809</td>
											<td>2025 | Cuckoo | PSA | Purchase</td>
											<td>1</td>
											<td>12312</td>
										</tr>
										<tr>
											<td>
												<div class="form-check">
													<input
														class="form-check-input"
														type="checkbox"
														value=""
														name="campaign_id"
													/>
												</div>
											</td>
											<td>
												<a
													href="javascript: void(0);"
													class="btn btn-sm btn-primary edit-btn"
													type="button"
													data-bs-toggle="offcanvas"
													data-id="4535435"
													data-bs-target="#campaignOffCanvas"
													aria-controls="campaignOffCanvas"
												>Edit</a>
											</td>
											<td>543675634534</td>
											<td>2025 | Cuckoo | PSA | Click</td>
											<td>2</td>
											<td>4354654</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		@components.OffCanvasCampaign()
	}
}

templ scriptCampaignList() {
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/campaign/tiktokCampaignLoader.js") }></script>
	<script>
		document.addEventListener("DOMContentLoaded", function () {
			numberFormat(".number-format");
		});
	</script>
	<script>
		document.querySelectorAll('.edit-btn').forEach(button => {
			button.addEventListener('click', function () {
				const id = button.getAttribute('data-id');
				const newUrl = `/dsp/tiktok/campaign/edit?id=${id}`;

				const previousPath = window.location.pathname + window.location.search;
				history.replaceState({ previousPath }, '', previousPath);

				history.pushState({}, '', newUrl);

				const offcanvasEl = document.getElementById('campaignOffCanvas');
				const offcanvas = bootstrap.Offcanvas.getOrCreateInstance(offcanvasEl);
				offcanvas.show();
			});
		});

		const offcanvasEl = document.getElementById('campaignOffCanvas');

		offcanvasEl.addEventListener('hide.bs.offcanvas', function () {
			const state = history.state;

			if (state && state.previousPath) {
				// Quay lại path trước khi mở offcanvas
				history.pushState({}, '', state.previousPath);
			} else {
				// Fallback nếu không có path trước
				history.pushState({}, '', '/dsp/tiktok/campaign/list');
			}
		});
	</script>
}

templ ajaxCampaignShowEdit() {
	<script type="module" src={ templates.AssetURL("/static/js/tiktok/campaign/index.js") }></script>
}
