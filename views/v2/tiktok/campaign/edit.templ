package campaign

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/tiktok/campaign/response"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	"godsp/views/v2/tiktok/campaign/components"
)

templ EditCampaign(data *response.ListCampaignResp) {
	{{
		dataLayoutMaster := masters.LayoutMasterData{
			AuthPermission: data.AuthPermission,
			UserInfo:       data.UserInfo,
		}
		pathBreadcrumb := []layoutCops.PathBreadcrumb{
			{Title: "Campaign", DataKey: "t-campaign", Url: "/dsp/tiktok/adgroup/edit"},
		}
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{head()}, scriptCampaignEdit()) {
		@layoutCops.ListBreadcrumdCpn("Campaign", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@components.FlashMsgCpn(data.FlashMsg)
		}
		@components.OffCanvasCampaign()
	}
}

templ scriptCampaignEdit() {
	<script type="module" src={ templates.AssetURL("/static/js/pages/tiktok/campaign/tiktokCampaignLoader.js") }></script>
}
