package components

import (
	"godsp/modules/tiktok/campaign/common/enums"
	"godsp/modules/tiktok/campaign/entity"
	"strconv"
	"strings"
)

templ CampaignBudget(camp *entity.CampaignEntity) {
	<div class="ms-3 mt-3 form-check form-switch form-switch-md" dir="ltr">
		<input type="checkbox" class="form-check-input" id="setCampaignBudgetSwitch"/>
		<label class="form-check-label" for="setCampaignBudgetSwitch">
			<span>
				Set campaign
				budget
			</span><i class="ri-question-line"></i>
		</label>
	</div>
	<div class="align-items-center mb-3 d-flex" id="budgetSettingsContainer">
		<select class="form-select" aria-label="Budget type select" style="width: max-content;">
			<option value="1" selected={ camp.BudgetMode == enums.BUDGET_MODE_DAY }>Daily</option>
			<option value="2" selected={ camp.BudgetMode == enums.BUDGET_MODE_TOTAL }>Lifetime</option>
		</select>
		<div class="input-group flex-grow-1">
			<input
				type="text"
				class="form-control number-format"
				id="budgetAmountInput"
				placeholder="At least 100,000"
				aria-label="Budget amount"
				inputmode="numberic"
				value={ formatVNNumber(camp.Budget) }
			/>
			<span class="input-group-text">VND</span>
		</div>
	</div>
}

func formatVNNumber(n float64) string {
	s := strconv.FormatFloat(n, 'f', 0, 64)
	parts := strings.Split(s, ".")
	intPart := parts[0]
	var result string
	count := 0

	for i := len(intPart) - 1; i >= 0; i-- {
		result = string(intPart[i]) + result
		count++
		if count%3 == 0 && i != 0 {
			result = "." + result
		}
	}

	if len(parts) > 1 {
		result = result + "," + parts[1]
	}

	return result
}
