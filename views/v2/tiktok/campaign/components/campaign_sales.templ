package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignSALES(camp *entity.CampaignEntity) {
	<div id="content-sales" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/products.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">Sales</p>
					<div style="position: relative; display: inline-block;">
						<button type="button" class="btn tips-btn" id="lead-generation-tips-button">
							<i class="ri-lightbulb-flash-line me-1"></i> Tips
						</button>
						<div
							role="tooltip"
							id="lead-generation-custom-popover"
							class="vi-popover vi-popper objective-tips-popper"
							tabindex="0"
						>
							<div class="tips-container">
								<div class="ctot">
									<p class="eachTitle">
										Choose this
										objective to:
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Use TikTok Shop product links, videos,
										images or livestreams.
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Use your TikTok Shop to create ads and reach
										relevant audiences.
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>Use your
										product catalog to create ads and drive
										shoppers to purchase products on your site.
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best for:
									</p>
									<p class="bestFor">
										<span class="vi-tag vi-tag--small">
											Website visits
										</span>
										<span class="vi-tag vi-tag--small">
											Reach
										</span>
										<span class="vi-tag vi-tag--small">
											Selling products
										</span>
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best
										practices:
									</p>
									<a target="_blank" href="#">
										How to create video shopping ads <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a target="_blank" href="#">
										Maximize results of livestreams <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a target="_blank" href="#">
										Display products in shopping center and
										search <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a target="_blank" href="#">
										Getting ready for video shopping ads (for
										Catalog) <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a target="_blank" href="#">
										How to create video shopping ads (for
										Catalog) <i class="ri-external-link-line"></i>
									</a>
								</div>
							</div>
							<div x-arrow="" class="popper__arrow" style="left: 50%; transform: translateX(-50%);"></div>
						</div>
					</div>
				</div>
				<p class="desc">
					Drive sales on your TikTok Shop, website, or app.
				</p>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Sales destination
			</div>
			<div class="form-item">
				<div class="list-group">
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="PRODUCT_SALES"
							checked
						/>
						<div>
							<div class="campaignTitle">TikTok Shop</div>
							<div class="mb-0 campaignRadioDes">
								Drive sales on your
								TikTok Shop with Shop Ads campaign settings chosen
								by you.
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="WEB_CONVERSIONS"
							disabled
						/>
						<div>
							<div class="campaignTitle">Website</div>
							<div class="mb-0 campaignRadioDes">
								Drive sales on your
								website with campaign settings chosen by you.
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="auction_traffic"
							disabled
						/>
						<div>
							<div class="campaignTitle">App</div>
							<div class="mb-0 campaignRadioDes">
								Drive sales on your
								app (product catalog required).
							</div>
						</div>
					</label>
				</div>
			</div>
		</div>
	</div>
}
