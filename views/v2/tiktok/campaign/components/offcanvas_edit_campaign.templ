package components

import "godsp/modules/tiktok/campaign/entity"
import "godsp/modules/tiktok/campaign/common/enums"

templ OffCanvasEditCampaignCpn(camp *entity.CampaignEntity) {
	@CampaignHeaderOffcanvas()
	<!-- // Offcanvas content -->
	<form id="campaignForm" class="tiktok-custom">
		<div class="container d-flex flex-1">
			<div class="row flex-1">
				<!-- Main Container -->
				<div class="col-12 d-flex flex-column position-relative ">
					<div class=" container-input-control d-flex flex-column mt-3 flex-1 ">
						<div class="tiktok">
							<div class="campaign-creation-content">
								<ul class="nav nav-tabs nav-tabs-custom nav-success nav-justified mb-3" role="tablist">
									<li class="nav-item" role="presentation">
										<a class="nav-link active" data-bs-toggle="tab" href="#home1" role="tab" aria-selected="true">
											Create new
										</a>
									</li>
									<li class="nav-item" role="presentation">
										<a class="nav-link" data-bs-toggle="tab" href="#profile1" role="tab" aria-selected="false" tabindex="-1">
											Use existing
										</a>
									</li>
								</ul>
								<div class="tab-content text-muted">
									<div class="tab-pane active show" id="home1" role="tabpanel">
										<div class="row m-0 objective-container">
											<div class="objective-selector-title">Advertising objective</div>
											<div class="col-md-4">
												@CampaignMenuObjective(camp)
											</div>
											<div class="col-md-8">
												<!-- Content for Reach -->
												if camp != nil && camp.ObjectiveType == enums.REACH {
													@CampaignREACH(camp)
												}
												<!-- Content for Traffic -->
												if camp != nil && camp.ObjectiveType == enums.TRAFFIC {
													@CampaignTRAFFIC(camp)
												}
												<!-- Content for Video Views -->
												if camp != nil && camp.ObjectiveType == enums.VIDEO_VIEWS {
													@CampaignVIDEOVIEWS(camp)
												}
												<!-- Community interaction -->
												if camp != nil && camp.ObjectiveType == enums.ENGAGEMENT {
													@CampaignCOMMUNITYINTERACTION(camp)
												}
												<!-- App promotion -->
												if camp != nil && camp.ObjectiveType == enums.APP_PROMOTION {
													@CampaignAPPPROMOTION(camp)
												}
												<!-- Lead generation -->
												if camp != nil && camp.ObjectiveType == enums.LEAD_GENERATION {
													@CampaignLEADGENERATION(camp)
												}
												<!-- Sales -->
												if camp != nil && camp.ObjectiveType == enums.PRODUCT_SALES {
													@CampaignSALES(camp)
												}
											</div>
										</div>
										<div class="row m-0 mb-3 objective-container setting mt-3">
											<div class="objective-selector-title mb-3">Settings</div>
											<div class="col-md-6 d-flex flex-column gap-2">
												@CampaignName(camp)
												@CampaignBudget(camp)
											</div>
										</div>
									</div>
									<div class="tab-pane" id="profile1" role="tabpanel">
										<div class="row m-0 mb-3 objective-container setting mt-3">
											<div class="col-md-8 name">
												<label for="">Select campaign</label>
												<select class="form-select mb-3" aria-label="Default select example">
													<option value="1">Campagin test 1</option>
													<option value="2">Campagin test 2</option>
													<option value="3">Campagin maybi test 1</option>
												</select>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- // Footer Container -->
					<div class="form-footer p-3 bg-white border-top d-none" id="">
						<div class="d-flex justify-content-between">
							<div class="d-flex flex-row gap-2 align-items-center">
								<div class="" id="alert-auto-save">
									<span class="d-none save-success text-success fs-16">
										Auto save success
										<i class="ri-check-double-line"></i>
									</span>
									<span class="d-none save-fail text-danger">
										Auto save fail
										<i class="ri-close-circle-line"></i>
									</span>
								</div>
							</div>
							<div>
								<button
									id="btn-save-form-off-canvas"
									type="button"
									class="btn btn-success waves-effect waves-light"
								>
									Save
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
}
