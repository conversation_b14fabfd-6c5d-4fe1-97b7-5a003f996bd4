package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignLEADGENERATION(camp *entity.CampaignEntity) {
	<div id="content-lead_generation" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/lead-ads.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">Lead generation</p>
					<div style="position: relative; display: inline-block;">
						<button type="button" class="btn tips-btn" id="lead-generation-tips-button">
							<i class="ri-lightbulb-flash-line me-1"></i> Tips
						</button>
						<div
							role="tooltip"
							id="lead-generation-custom-popover"
							class="vi-popover vi-popper objective-tips-popper"
							tabindex="0"
						>
							<div class="tips-container">
								<div class="ctot">
									<p class="eachTitle">
										Choose this
										objective to:
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Collect leads using instant forms on TikTok.
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Collect leads, contact info, and
										registration on your website.
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>Start
										conversations on WhatsApp, Messenger, TikTok
										direct message, and more.
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best for:
									</p>
									<p class="bestFor">
										<span class="vi-tag vi-tag--small">
											Find new customers
										</span>
										<span class="vi-tag vi-tag--small">
											Qualify leads
										</span>
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best
										practices:
									</p>
									<a target="_blank" href="#">
										About TikTok lead generation solutions <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a target="_blank" href="#">
										Lead generation on TikTok <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a target="_blank" href="#">
										Message ads on TikTok <i class="ri-external-link-line"></i>
									</a>
								</div>
							</div>
							<div x-arrow="" class="popper__arrow" style="left: 50%; transform: translateX(-50%);"></div>
						</div>
					</div>
				</div>
				<p class="desc">
					Collect leads for your business.
				</p>
				<div class="tagContainer">
					<span class="tag">
						Website
					</span>
					<span class="tag">
						Instant form
					</span>
					<span class="tag">
						TikTok direct messages
					</span>
					<span class="tag">
						Instant messaging apps
					</span>
				</div>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Campaign setup
			</div>
			<div class="form-item">
				<div class="list-group">
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="auction_traffic"
							checked
						/>
						<div>
							<div class="campaignTitle">Manual campaign</div>
							<div class="mb-0 campaignRadioDes">
								Create your campaign
								using the standard workflow to maximize precise
								control for your ads settings.
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="auction_traffic"
						/>
						<div>
							<div class="campaignTitle">
								<span>
									Smart+
									campaign
								</span>
								<button
									type="button"
									class="btn btn-new"
									data-bs-toggle="tooltip"
									data-bs-placement="top"
									title="Smart+ campaign is a new way to easily and quickly create effective campaigns."
								>
									New
								</button>
							</div>
							<div class="mb-0 campaignRadioDes">
								Improve ad
								performance with automated campaign management and
								smart optimization (placement selection, AIGC,
								audience targeting, and more).
							</div>
						</div>
					</label>
				</div>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="ms-3 mt-3 form-check form-switch form-switch-md" dir="ltr">
			<input type="checkbox" class="form-check-input" id="customSwitchsizemd"/>
			<label class="form-check-label" for="customSwitchsizemd">
				<span>
					Use
					catalog
				</span>
				<button type="button" class="btn btn-new" data-bs-toggle="tooltip" data-bs-placement="top" title="">
					New
				</button>
			</label>
			<br/>
			<span>
				Use your automotive catalog to promote car inventory or models.
			</span>
		</div>
	</div>
}
