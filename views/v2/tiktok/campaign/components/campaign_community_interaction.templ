package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignCOMMUNITYINTERACTION(camp *entity.CampaignEntity) {
	<div id="content-community_interaction" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/engagement.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">
						Community interaction
					</p>
					<div style="position: relative; display: inline-block;">
						<button type="button" class="btn tips-btn" id="community-interaction-tips-button">
							<i class="ri-lightbulb-flash-line me-1"></i> Tips
						</button>
						<div
							role="tooltip"
							id="community-interaction-custom-popover"
							class="vi-popover vi-popper objective-tips-popper"
							tabindex="0"
						>
							<div class="tips-container">
								<div>
									<p class="eachTitle">
										Best for:
									</p>
									<p class="bestFor">
										<span class="vi-tag vi-tag--small">
											Community engagement
										</span>
										<span class="vi-tag vi-tag--small">
											Follower growth
										</span>
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best
										practices:
									</p>
									<a
										target="_blank"
										href="https://ads.tiktok.com/help/article/community-interaction-objective"
									>
										Intro to community interaction campaigns <i class="ri-external-link-line"></i>
									</a>
								</div>
							</div>
							<div x-arrow="" class="popper__arrow" style="left: 50%; transform: translateX(-50%);"></div>
						</div>
					</div>
				</div>
				<p class="desc">
					Get more followers or TikTok page visits.
				</p>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Choose this objective to:
			</div>
			<div class="form-item d-flex flex-column gap-2">
				<div class="objective-item d-flex align-items-center">
					<i class="ri-check-line me-2"></i> Get more people to follow
					your TikTok account.
				</div>
				<div class="objective-item d-flex align-items-center">
					<i class="ri-check-line me-2"></i> Get more people to visit
					your TikTok profile.
				</div>
				<div class="objective-item d-flex align-items-center">
					<i class="ri-check-line me-2"></i> Promote your livestream.
				</div>
			</div>
		</div>
	</div>
}
