package components

templ OffCanvasCampaign() {
	<div
		class="offcanvas offcanvas-end offcanvas-medium-content casa-offcanvas"
		data-bs-backdrop="static"
		data-bs-scroll="false"
		tabindex="-1"
		id="campaignOffCanvas"
		aria-labelledby="offcanvasRightLabel"
	>
		<div class="offcanvas-body p-0 bg-light">
			<div class="bg-white" style="width: 60px; position: sticky; top: 0; height: 100vh;">
				<div class="button-groups p-2">
					<!-- ghost Buttons -->
					<button
						type="button"
						data-bs-dismiss="offcanvas"
						aria-label="Close"
						class="btn btn-soft-dark waves-effect waves-light"
					>
						<i class="ri-close-fill"></i>
					</button>
					<!-- ghost Buttons -->
				</div>
			</div>
			<div class="content-offcanvas flex-1">
				<div class="data-blk">
					@CampaignHeaderOffcanvas()
					<!-- // Offcanvas content -->
					<form id="campaignForm" class="tiktok-custom">
						<div class="container d-flex flex-1">
							<div class="row flex-1">
								<!-- Main Container -->
								<div class="col-12 d-flex flex-column position-relative ">
									<div class=" container-input-control d-flex flex-column mt-3 flex-1 ">
										<div class="tiktok" id="tiktok-campaign"></div>
									</div>
									<!-- // Footer Container -->
									<div class="form-footer p-3 bg-white border-top d-none" id="">
										<div class="d-flex justify-content-between">
											<div class="d-flex flex-row gap-2 align-items-center">
												<div class="" id="alert-auto-save">
													<span class="d-none save-success text-success fs-16">
														Auto save success
														<i class="ri-check-double-line"></i>
													</span>
													<span class="d-none save-fail text-danger">
														Auto save fail
														<i class="ri-close-circle-line"></i>
													</span>
												</div>
											</div>
											<div>
												<button
													id="btn-save-form-off-canvas"
													type="button"
													class="btn btn-success waves-effect waves-light"
												>
													Save
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
}
