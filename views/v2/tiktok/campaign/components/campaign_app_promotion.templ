package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignAPPPROMOTION(camp *entity.CampaignEntity) {
	<div id="content-app_promotion" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/app-install.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">
						App promotion
					</p>
					<div style="position: relative; display: inline-block;">
						<button type="button" class="btn tips-btn" id="app-promotion-tips-button">
							<i class="ri-lightbulb-flash-line me-1"></i> Tips
						</button>
						<div
							role="tooltip"
							id="app-promotion-custom-popover"
							class="vi-popover vi-popper objective-tips-popper"
							tabindex="0"
						>
							<div class="tips-container">
								<div class="ctot">
									<p class="eachTitle">
										Choose this
										objective to:
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Optimize for people who install your app and
										take additional actions like purchase or
										subscribe.
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Get more people to take action on your app.
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best
										practices:
									</p>
									<a
										target="_blank"
										href="https://ads.tiktok.com/help/article/set-up-app-attribution-tiktok-ads-manager?redirected=2"
									>
										How to set up app attribution in TikTok Ads
										Manager <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a
										target="_blank"
										href="https://ads.tiktok.com/help/article/ios14-dedicated-campaigns-video-walkthrough?redirected=2"
									>
										iOS 14 dedicated campaigns video walkthrough
										<i class="ri-external-link-line"></i>
									</a>
								</div>
							</div>
							<div x-arrow="" class="popper__arrow" style="left: 50%; transform: translateX(-50%);"></div>
						</div>
					</div>
				</div>
				<p class="desc">
					The cost effective way to get more people to install and take
					desired actions in your app.
				</p>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Promotion types
			</div>
			<div class="form-item">
				<div class="list-group">
					<label class="list-group-item d-flex flex-column">
						<div class="parent d-flex">
							<input
								class="form-check-input me-3"
								type="radio"
								name="app_promotion_type"
								value="auction_traffic"
								checked
							/>
							<div>
								<div class="campaignTitle">App install</div>
								<div class="mb-0 campaignRadioDes">
									Get people to
									install and use your app.
								</div>
							</div>
						</div>
						<div class="segmentationLine"></div>
						<div class="child ps-4">
							<div class="campaign-selector_title mb-2 mt-2" style="font-size: 14px;">
								Campaign setup
							</div>
							<div class="form-item">
								<div class="list-group">
									<label class="list-group-item d-flex">
										<input
											class="form-check-input me-3"
											type="radio"
											name="app_promotion_type1"
											value="auction_traffic"
											checked
										/>
										<div>
											<div class="campaignTitle">
												Manual
												campaign
											</div>
											<div class="mb-0 campaignRadioDes">
												Create your campaign using the
												standard workflow to maximize
												precise control for your ads
												settings.
											</div>
										</div>
									</label>
									<label class="list-group-item d-flex">
										<input
											class="form-check-input me-3"
											type="radio"
											name="app_promotion_type1"
											value="auction_traffic"
										/>
										<div>
											<div class="campaignTitle">
												<span>
													Smart+
													campaign
												</span>
												<button
													type="button"
													class="btn btn-new"
													data-bs-toggle="tooltip"
													data-bs-placement="top"
													title="Smart+ campaign is a new way to easily and quickly create effective campaigns."
												>
													New
												</button>
											</div>
											<div class="mb-0 campaignRadioDes">
												Improve
												ad
												performance with automated campaign
												management and
												smart optimization (placement
												selection,
												AIGC,
												audience targeting, and more).
											</div>
										</div>
									</label>
								</div>
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="app_promotion_type"
							value="auction_traffic"
						/>
						<div>
							<div class="campaignTitle">
								<span>App retargeting</span>
							</div>
							<div class="mb-0 campaignRadioDes">
								Re-engage existing
								app users to take action in your app.
							</div>
						</div>
					</label>
				</div>
			</div>
		</div>
	</div>
}
