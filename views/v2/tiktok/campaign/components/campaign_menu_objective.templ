package components

import (
	"godsp/modules/tiktok/campaign/common/enums"
	"godsp/modules/tiktok/campaign/entity"
)

var objectiveType = ""

templ CampaignMenuObjective(camp *entity.CampaignEntity) {
	if camp != nil {
		{{ objectiveType = camp.ObjectiveType }}
	}
	<div class="list-group">
		<div class="group-objective mb-2">
			Awareness
		</div>
		<label class="list-group-item d-flex">
			<input
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="reach"
				checked?={ isObjectiveChecked(objectiveType, enums.REACH) }
			/>
			<div class="flex-grow-1 d-flex justify-content-between">
				<span>Reach</span> <i class="ri-arrow-right-s-line"></i>
			</div>
		</label>
		<div class="group-objective mt-3 mb-2">
			Consideration
		</div>
		<label class="list-group-item d-flex">
			<input
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="traffic"
				checked?={ isObjectiveChecked(objectiveType, enums.TRAFFIC) }
			/>
			<div class="flex-grow-1 d-flex justify-content-between">
				<span>Traffic</span> <i class="ri-arrow-right-s-line"></i>
			</div>
		</label>
		<label class="list-group-item d-flex">
			<input
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="video_views"
				checked?={ isObjectiveChecked(objectiveType, enums.VIDEO_VIEWS) }
			/>
			Video views
		</label>
		<label class="list-group-item d-flex">
			<input
				checked="checked"
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="community_interaction"
				checked?={ isObjectiveChecked(objectiveType, enums.ENGAGEMENT) }
			/>
			Community interaction
		</label>
		<div class="group-objective mt-3 mb-2">
			Conversion
		</div>
		<label class="list-group-item d-flex">
			<input
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="app_promotion"
				checked?={ isObjectiveChecked(objectiveType, enums.APP_PROMOTION) }
			/>
			<div class="flex-grow-1 d-flex justify-content-between">
				<span>App promotion</span> <i class="ri-arrow-right-s-line"></i>
			</div>
		</label>
		<label class="list-group-item d-flex">
			<input
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="lead_generation"
				checked?={ isObjectiveChecked(objectiveType, enums.LEAD_GENERATION) }
			/>
			<div class="flex-grow-1 d-flex justify-content-between">
				<span>Lead generation</span> <i class="ri-arrow-right-s-line"></i>
			</div>
		</label>
		<label class="list-group-item d-flex">
			<input
				class="form-check-input me-3"
				type="radio"
				name="objective"
				value="sales"
				checked?={ isObjectiveChecked(objectiveType, enums.PRODUCT_SALES) }
			/>
			<div class="flex-grow-1 d-flex justify-content-between">
				<span>Sales</span> <i class="ri-arrow-right-s-line"></i>
			</div>
		</label>
	</div>
}

func isObjectiveChecked(objectiveType string, value string) bool {
	return objectiveType != "" && objectiveType == value
}
