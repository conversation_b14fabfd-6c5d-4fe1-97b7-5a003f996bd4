package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignREACH(camp *entity.CampaignEntity) {
	<div id="content-reach" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/reach.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">Reach </p>
					<div style="position: relative; display: inline-block;">
						<button type="button" class="btn tips-btn" id="reach-tips-button">
							<i class="ri-lightbulb-flash-line me-1"></i> Tips
						</button>
						<div
							role="tooltip"
							id="reach-custom-popover"
							class="vi-popover vi-popper objective-tips-popper"
							tabindex="0"
						>
							<div class="tips-container">
								<div class="ctot">
									<p class="eachTitle">
										Choose this
										objective to:
									</p>
									<p class="ctotItem">
										<i class="ri-check-line"></i>
										Tell more people about your product,
										service, or company.
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best for:
									</p>
									<p class="bestFor">
										<span class="vi-tag vi-tag--small">
											Brand awareness
										</span>
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best
										practices:
									</p>
									<a target="_blank" href="https://ads.tiktok.com/help/article/reach-objective?lang=en">
										Introduction
										to reach <i class="ri-external-link-line"></i>
									</a>
									<br/>
									<a
										target="_blank"
										href="https://ads.tiktok.com/help/article/reach-frequency-ad-buying-type?lang=en"
									>
										How, why,
										and when to use reach &amp; frequency <i class="ri-external-link-line"></i>
									</a>
								</div>
							</div>
							<div x-arrow="" class="popper__arrow" style="left: 50%; transform: translateX(-50%);"></div>
						</div>
					</div>
				</div>
				<p class="desc">
					Drive sales on your TikTok Shop, website, or app.
				</p>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Campaign type
			</div>
			<div class="form-item">
				<div class="list-group">
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="reach_campaign_type"
							value="auction_reach"
							checked
						/>
						<div>
							<div class="campaignTitle">Auction reach</div>
							<div class="mb-0 campaignRadioDes">
								Ads the most
								efficient reach.
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="reach_campaign_type"
							value="reach_frequency"
						/>
						<div class="w-100">
							<div class="campaignTitle d-flex justify-content-between w-100">
								<span>
									Reach &
									Frequency
								</span>
								<button
									type="button"
									class="btn btn-light btn-sm"
									data-bs-toggle="tooltip"
									data-bs-placement="top"
									title="Reserve your campaign at a fixed CPM (cost per thousand) with predictable impressions."
								>
									Reserve
								</button>
							</div>
							<div class="mb-0 campaignRadioDes">
								Reserve ads in
								advance with a
								guaranteed
								reach and
								frequency.
							</div>
						</div>
					</label>
				</div>
			</div>
		</div>
	</div>
}
