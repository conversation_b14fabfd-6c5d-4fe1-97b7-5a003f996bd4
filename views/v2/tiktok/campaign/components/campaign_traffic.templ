package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignTRAFFIC(camp *entity.CampaignEntity) {
	<div id="content-traffic" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/traffic.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">Traffic </p>
				</div>
				<p class="desc">
					Send more people to a destination on your website or app.
				</p>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Campaign setup
			</div>
			<div class="form-item">
				<div class="list-group">
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="auction_traffic"
							checked
						/>
						<div>
							<div class="campaignTitle">Manual campaign</div>
							<div class="mb-0 campaignRadioDes">
								Create your campaign
								using the standard workflow to maximize precise
								control for your ads settings.
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="auction_traffic"
						/>
						<div>
							<div class="campaignTitle">
								<span>
									Smart+
									campaign
								</span>
								<button
									type="button"
									class="btn btn-new"
									data-bs-toggle="tooltip"
									data-bs-placement="top"
									title="Smart+ campaign is a new way to easily and quickly create effective campaigns."
								>
									New
								</button>
							</div>
							<div class="mb-0 campaignRadioDes">
								Improve ad
								performance with automated campaign management and
								smart optimization (placement selection, AIGC,
								audience targeting, and more).
							</div>
						</div>
					</label>
					<label class="list-group-item d-flex">
						<input
							class="form-check-input me-3"
							type="radio"
							name="traffic_campaign_type"
							value="auction_traffic"
						/>
						<div>
							<div class="campaignTitle">
								<span>Search campaign</span>
								<i class="ri-question-line"></i>
								<button
									type="button"
									class="btn btn-new"
									data-bs-toggle="tooltip"
									data-bs-placement="top"
									title=""
								>
									New
								</button>
							</div>
							<div class="mb-0 campaignRadioDes">
								Create your campaign
								with keywords and serve ads within TikTok's search
								result page.
							</div>
						</div>
					</label>
				</div>
			</div>
		</div>
	</div>
}
