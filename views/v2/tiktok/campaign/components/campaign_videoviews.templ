package components

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/modules/tiktok/campaign/entity"

templ CampaignVIDEOVIEWS(camp *entity.CampaignEntity) {
	<div id="content-video_views" class="objective-content-pane">
		<div class="objectiv-description">
			<img
				src={ templates.AssetURL("/static/images/tiktok/campaign/video-view.gif") }
				loop="loop"
				style="background-color: transparent;"
				class="objective-icon"
			/>
			<div class="content">
				<div class="name-container">
					<p class="name">
						Video views
					</p>
					<div style="position: relative; display: inline-block;">
						<button type="button" class="btn tips-btn" id="video-view-tips-button">
							<i class="ri-lightbulb-flash-line me-1"></i> Tips
						</button>
						<div
							role="tooltip"
							id="video-view-custom-popover"
							class="vi-popover vi-popper objective-tips-popper"
							tabindex="0"
						>
							<div class="tips-container">
								<div>
									<p class="eachTitle">
										Best for:
									</p>
									<p class="bestFor">
										<span class="vi-tag vi-tag--small">
											Consideration
										</span>
										<span class="vi-tag vi-tag--small">
											Focused view
										</span>
									</p>
								</div>
								<div>
									<p class="eachTitle">
										Best
										practices:
									</p>
									<a target="_blank" href="https://ads.tiktok.com/help/article/video-views-objective">
										About focused view optimization updates to
										video view objectiv <i class="ri-external-link-line"></i>
									</a>
								</div>
							</div>
							<div x-arrow="" class="popper__arrow" style="left: 50%; transform: translateX(-50%);"></div>
						</div>
					</div>
				</div>
				<p class="desc">
					Get more views and engagement for your video ads.
				</p>
			</div>
		</div>
		<div class="segmentationLine"></div>
		<div class="campaignSelector">
			<div class="campaign-selector_title">
				Choose this objective to:
			</div>
			<div class="form-item d-flex flex-column gap-2">
				<div class="objective-item d-flex align-items-center">
					<i class="ri-check-line me-2"></i> Maximize the plays of your
					video
					ads.
				</div>
				<div class="objective-item d-flex align-items-center">
					<i class="ri-check-line me-2"></i> Drive consideration by
					showing
					your ads to users who are more actively engaged.
				</div>
			</div>
		</div>
	</div>
}
