package tableAds

import (
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/facebook/iface"
	advertiserE "godsp/modules/tiktok/advertiser/entity"
	cusColRes "godsp/modules/tiktok/custom_column_table/transport/responses"
)

type ListTableAdsLayoutData struct {
	FlashMsg           string         `json:"flash_msg"`
	AuthPermission     map[string]int `json:"auth_permission" `
	UserInfo           iface.UserInfoAuth
	Clients            []*clientE.ClientEntity
	Advertisers        []*advertiserE.AdvertiserEntity
	PresetsColumnTable []*cusColRes.PresetColumnResp `json:"presets_column_table"`
}
