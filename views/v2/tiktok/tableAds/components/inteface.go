package components

import (
	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/facebook/iface"
	advertiserE "godsp/modules/tiktok/advertiser/entity"
	cusColRes "godsp/modules/tiktok/custom_column_table/transport/responses"
)

type LayoutHeaderTableData struct {
	AuthPermission     map[string]int
	UserInfo           iface.UserInfoAuth
	Clients            []*clientE.ClientEntity
	Advertisers        []*advertiserE.AdvertiserEntity
	PresetsColumnTable []*cusColRes.PresetColumnResp `json:"presets_column_table"`
}

type LayoutTableData struct {
	AuthPermission     map[string]int
	UserInfo           iface.UserInfoAuth
	Clients            []*clientE.ClientEntity
	Advertisers        []*advertiserE.AdvertiserEntity
	PresetsColumnTable []*cusColRes.PresetColumnResp `json:"presets_column_table"`
}
