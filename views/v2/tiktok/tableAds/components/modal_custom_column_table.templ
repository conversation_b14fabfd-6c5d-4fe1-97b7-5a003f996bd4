package components

type CustomMetricColumnTableTempl struct {
	// Define any necessary fields here if needed
	ReportFields *[]map[string]string // Example field to hold report fields

}

templ ModalCustomColumnTable() {
	<!-- Bootstrap Modal -->
	<div class="modal fade zoomIn" id="customColumnModal" tabindex="-1" aria-labelledby="customColumnModalLabel" aria-hidden="true">
		<div class="modal-dialog  modal-dialog-centered modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="customColumnModalLabel">Customize Columns</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body border my-3 py-0 px-2">
					<div class="row" id="customColumnContent"></div>
				</div>
				<div class="modal-footer">
					<div class="flex-1 d-flex p-0 m-0 gap-2 align-items-end">
						<div class="form-check form-switch form-check-inline ps-0 mb-2 d-flex gap-2 align-items-center">
							<input
								type="checkbox"
								class="form-check-input form-switch-md float-left ms-0 form-control-sm"
								id="switchToggleSaveNewPresetColumTable"
							/>
							<label class="form-check-label" for="switchToggleSaveNewPresetColumTable" style="white-space: nowrap;">New preset</label>
						</div>
						<div id="footerControlPresetForm" style="display: none;">
							<div class="d-flex flex-1 align-items-end gap-2">
								<div class="d-flex flex-wrap gap-1 align-items-center">
									<label class="mb-0" for="footernamePreset">Name<i class="text-danger">*</i></label>
									<input
										data-id=""
										type="text"
										name="namePreset"
										id="footernamePreset"
										class="form-control"
										placeholder="Please enter name private"
									/>
								</div>
								<div class="d-flex flex-wrap gap-1 align-items-end">
									<label class="mb-0" for="footerNamePrivatePreset">Name private</label>
									<input
										data-id=""
										type="text"
										name="namePrivatePreset"
										id="footerNamePrivatePreset"
										class="form-control"
										placeholder="Please enter name private"
									/>
								</div>
								<button class=" btn btn-danger waves-effect" id="cancleEditOrCreatePreset">Cancel</button>
							</div>
						</div>
					</div>
					<div class="">
						<button class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
						<button class="btn btn-success" id="saveColumns">Save</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Custom Metric Column Modal -->
	@ModalCustomMetricColumnTable()
	<!-- Custom Column Table Script -->
	@scriptCustomColumnTable()
	@ModalDeleteItems()
}

templ ModalDeleteItems() {
	<div class="modal fade zoomIn modal-nested" id="modalRemoveItem" tabindex="-1" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="btn-close"></button>
				</div>
				<div class="modal-body p-5 text-center">
					<lord-icon
						src="https://cdn.lordicon.com/gsqxdxog.json"
						trigger="loop"
						colors="primary:#405189,secondary:#f06548"
						style="width:150px;height:150px"
					></lord-icon>
					<div class="mt-4 text-center">
						<h4 class="fs-semibold" data-key="t-reload-data">Remove Metric?</h4>
						<p class="text-muted fs-14 mb-4 pt-1" data-key="t-des-reload-data">
							Are you sure you want to remove this metric?
						</p>
						<div class="hstack gap-2 justify-content-center remove">
							<button class="btn btn-light fw-medium text-decoration-none btn-light" data-bs-dismiss="modal">
								<i class="ri-close-line me-1 align-middle"></i>
								<span data-key="t-cancel">Cancel</span>
							</button>
							<button
								class="btn btn-danger"
								id="btnConfirmRemove"
								data-action-by=""
								data-url=""
								data-bs-dismiss="modal"
								data-key="t-yes"
							>Yes</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ ModalCustomMetricColumnTable() {
	<div
		class="modal fade zoomIn modal-nested"
		id="customMetricColumnModal"
		tabindex="1"
		aria-labelledby="customMetricColumnModal"
		aria-hidden="true"
	>
		<div class="modal-dialog  modal-dialog-centered modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-title">
						<h5 class="fw-bold mb-0">Create custom metric</h5>
						<p class="text-muted fw-normal mb-0">
							Create custom metrics to get more detailed information about campaign performance.
						</p>
					</div>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="row" id="customMetricColumnContent"></div>
				</div>
				<div class="modal-footer">
					<button class="btn btn-light waves-effect" data-bs-dismiss="modal">Cancel</button>
					<button class="btn btn-success" id="saveCustomMetricBtn">Save Metric</button>
				</div>
			</div>
		</div>
	</div>
}

templ scriptCustomColumnTable() {
	<script type="module" defer>
	import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
	import { requestApi } from "/static/js/common/httpService.js";


	/**
* Get custom metric modal content HTML
*/
	function getHtmlContentCustomColumnTable() {
		const noResultHTML = `<div class="no-results p-4"><img src="/static/images/no-data.png" alt="No Results" style="max-width: 250px; margin-bottom: 10px;"><p>Empty Content</p></div>`;
		$('#customColumnContent').empty();
		$('#customMetricColumnContent').empty();
		loader();
		requestApi("POST", "/dsp/tiktok/api/custom-column-table/custom-modal")
			.then((response) => {
				if (response?.modal_custom_column_html && response?.modal_custom_metric_column_html) {
					$('#customColumnContent').html(response.modal_custom_column_html);
					$('#customMetricColumnContent').html(response.modal_custom_metric_column_html);
				} else {
					$('#customColumnContent').html(noResultHTML);
					$('#customMetricColumnContent').html(noResultHTML);
					errorAlert("Failed to load custom column content.");
				}
			})
			.catch((error) => {
				$('#customColumnContent').html(noResultHTML);
				$('#customMetricColumnContent').html(noResultHTML);
				errorAlert("Error loading custom column content: " + (error?.msg || "Unknown error"));
			}).finally(() => {
				loader(false);
			});
	}

	function removeEventListeners() {
		$('#customColumnModal #customColumnContent').off().empty();
		$('#customColumnModal').find("*").addBack().filter('#saveColumns, #switchToggleSaveNewPresetColumTable, #cancleEditOrCreatePreset, input[name="namePreset"], input[name="namePrivatePreset"]').off();

		$('#customMetricColumnModal #customMetricColumnContent').off().empty();
		$('#customMetricColumnModal #saveCustomMetricBtn').off();

		$('#modalRemoveItem #btnConfirmRemove').off();
	}


	$(function () {
		$('#customColumnModal').on('show.bs.modal', function (e) {
			getHtmlContentCustomColumnTable();
		});

		$('#customColumnModal').on('hidden.bs.modal', function () {
			console.log("Modal closed");
			removeEventListeners();
		});
	})


</script>
}
