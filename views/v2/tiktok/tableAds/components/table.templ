package components

import "github.com/dev-networldasia/dspgos/gos/templates"

templ Table(data LayoutTableData) {
	{{
LayoutHeaderTableData := LayoutHeaderTableData{
	AuthPermission:     data.AuthPermission,
	UserInfo:           data.UserInfo,
	Clients:            data.Clients,
	Advertisers:        data.Advertisers,
	PresetsColumnTable: data.PresetsColumnTable,
}
	}}
	@styeTable()
	<div class="row">
		<div class="col-lg-12">
			<div class="card">
				<div class="card-header d-flex justify-content-between">
					<div class="flex-1">
						<ul id="navTabTableAds" class=" nav nav-tabs-custom nav_cus rounded card-header-tabs border-bottom-0" role="tablist">
							<li class="nav-item" style="width: auto;">
								<a
									data-navigo-href="dsp/tiktok/campaign/list"
									href="#campaign"
									class="nav-link d-flex justify-content-between align-items-center active"
									data-bs-toggle="tab"
									data-tab="campaigns"
									role="tab"
								>
									<span class="d-flex align-items-center gap-1 fs-15 ">
										<i class="ri-input-method-line fs-18 text-tiktok"></i> <span class="text-dark">Campaigns</span>
									</span>
									<button
										type="button"
										class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill ms-2 bg-tiktok"
										id="camp_selected"
									>
										<i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="camp-selected-close"></i>
										0 selected
									</button>
								</a>
							</li>
							<li class="nav-item" style="width: auto;">
								<a
									data-navigo-href="dsp/tiktok/adgroup/list"
									href="#adgroup"
									class="nav-link d-flex justify-content-between align-items-center"
									data-bs-toggle="tab"
									data-tab="adgroups"
									role="tab"
								>
									<span class="d-flex align-items-center gap-1 fs-15 ">
										<i class="ri-dashboard-line fs-18 text-tiktok"></i>
										<span id="selected_adgroup_name" class="text-dark">
											Ad
											group
										</span>
									</span>
									<button
										type="button"
										class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill  ms-2 bg-tiktok"
										id="adgroup_selected"
									>
										<i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="adgroup-selected-close"></i>
										0 selected
									</button>
								</a>
							</li>
							<li class="nav-item" style="width: auto;">
								<a
									data-navigo-href="dsp/tiktok/ad/list"
									href="#ad"
									class="nav-link d-flex justify-content-between align-items-center"
									data-bs-toggle="tab"
									data-tab="ads"
									role="tab"
								>
									<span class="d-flex align-items-center gap-1 fs-15 ">
										<i class="ri-advertisement-line fs-18 text-tiktok"></i> <span id="selected_ad_name" class="text-dark">Ads</span>
									</span>
									<button
										type="button"
										class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill  ms-2 bg-tiktok"
										id="ad_selected"
									>
										<i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="ad-selected-close"></i>
										0 selected
									</button>
								</a>
							</li>
						</ul>
					</div>
					@FilterTable()
				</div>
				<div class="card-body pt-2">
					<div class="campaigns-page">
						<div class="card-title header-table mb-0 pb-2 d-flex align-items-center justify-content-between" style="margin: -2px -10px;">
							@HeaderTableLeft(LayoutHeaderTableData)
							@HeaderTableRight(LayoutHeaderTableData)
						</div>
						<div class="tab-content">
							<div class="tab-pane active" id="campaign" role="tabpanel">
								<div class="table-responsive table-card">
									<table
										id="data-table-campaign"
										class="table table-cus  table-bordered table-hover dataTable table-striped align-middle table-nowrap"
										style="width: 100%;"
									></table>
								</div>
							</div>
							<div class="tab-pane" id="adgroup" role="tabpanel">
								<div class="table-responsive table-card">
									<table
										id="data-table-adgroup"
										class="table table-cus  table-bordered table-hover dataTable table-striped align-middle table-nowrap"
										style="width: 100%;"
									></table>
								</div>
							</div>
							<div class="tab-pane" id="ad" role="tabpanel">
								<div class="table-responsive table-card">
									<table
										id="data-table-ad"
										class="table table-cus  table-bordered table-hover dataTable table-striped align-middle table-nowrap"
										style="width: 100%;"
									></table>
								</div>
							</div>
						</div>
					</div>
				</div>
				// {{ template "modal_reload_camp" }}
				// {{ template "component_camp_html" }}
			</div>
		</div>
	</div>
	@ModalCustomColumnTable()
	@HtmlElement()
	if data.AuthPermission == nil {
		<script type="module" src={ templates.AssetURL("/static/js/tiktok/list-table-ads/admin.js") } defer></script>
	}
}

templ HtmlElement() {
	<template id="titleFootTableHtml">
		<th colspan="3" class="dtfc-fixed-left" style="position: sticky;left: 0;background-color: white;">
			Results from { `{rowCount} {title}` }
		</th>
	</template>
	<template id="nullFootTableHtml">
		<th class="align-top">
			<div class=" d-flex flex-column align-items-end fw-normal">
				<span class="text-dark">-</span>
			</div>
		</th>
	</template>
	<template id="totalFootTableHtml">
		<th class="align-top">
			<div class=" d-flex flex-column align-items-end fw-normal">
				<span class="text-dark">{ `{value}` }</span>
				<span class="text-muted fs-10">{ `{titleTotal}` }</span>
			</div>
		</th>
	</template>
	// Template for cell in table
	<template id="bugetCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12 text-primary">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	<template id="numberCellTable">
		<div class="text-end">
			<span>{ `{value}` }</span>
		</div>
	</template>
	<template id="rawValueCellTable">
		<div class="text-start">
			<span>{ `{value}` }</span>
		</div>
	</template>
	// Adgroup Table
	<template id="bidCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
	<template id="resultAndResultRateCellTable">
		<div class="flex-grow-1 ms-3 text-right">
			<h5 class="mb-1 fs-12">{ `{value}` }</h5>
			<p class="mb-0 fs-10 text-muted">{ `{label}` }</p>
		</div>
	</template>
}

templ styeTable() {
	<style>
	table th .drag-icon {
		visibility: hidden;
	}

	table th:hover .drag-icon {
		visibility: visible;
	}
</style>
}
