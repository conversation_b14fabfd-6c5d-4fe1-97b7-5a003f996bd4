package components

templ CustomViewTable(data LayoutHeaderTableData) {
	<div class="btn-group" id="presetColumnDropdown">
		<button
			type="button"
			class="btn btn-light dropdown-toggle d-flex gap-2 align-items-center"
			data-bs-toggle="dropdown"
			aria-haspopup="true"
			aria-expanded="false"
			style="padding: 0px 10px;"
		>
			<i class="ri-equalizer-line align-middle"></i>
			<div class="d-flex flex-column">
				<span class="mt-1" style="line-height: 1;">Columns</span>
				<span class="text-muted fs-10" id="presetNameView" style="display: line-height:1;">Default</span>
			</div>
		</button>
		<div class="dropdown-menu">
			if data.PresetsColumnTable != nil {
				for _, preset := range data.PresetsColumnTable {
					<a class="dropdown-item" href="#" data-preset-id={ preset.ID.Hex() } data-preset-name={ preset.Name }>{ preset.Name }</a>
				}
			}
		</div>
	</div><!-- /btn-group -->
	@scriptCustomViewTable()
}

templ scriptCustomViewTable() {
	<script type="module" defer>
		$(document).ready(function () {
			$("#presetColumnDropdown").on("click", ".dropdown-item", function () {
				const id = $(this).data("preset-id");
				localStorage.setItem("preset_column", id);
				document.dispatchEvent(new CustomEvent("onChangeConfigColumnTable", {}));
			});
		})
	</script>
}
