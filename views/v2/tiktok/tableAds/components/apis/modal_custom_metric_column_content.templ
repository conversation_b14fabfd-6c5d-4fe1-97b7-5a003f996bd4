package apis

type DataScript struct {
	FieldsStr       string `json:"fieldsStr"`
	FieldsSelectStr string `json:"FieldsSelectStr"`
}

templ ModalCustomMetricColumnTableContent(data CustomColumnTableTempl) {
	@styleCustomMetricColumnTable()
	<!-- Bootstrap Modal -->
	<div class="p-4 border rounded bg-light">
		<div class="d-flex flex-wrap gap-2 align-items-center mb-3">
			<select class="form-select w-auto" id="selectMetric" style="flex-grow: 2;">
				<option value="" disabled selected>+ Metrics</option>
			</select>
			<button class="btn btn-outline-secondary operator-btn flex-1 py-1 fs-18">+</button>
			<button class="btn btn-outline-secondary operator-btn flex-1 py-1 fs-18">-</button>
			<button class="btn btn-outline-secondary operator-btn flex-1 py-1 fs-18">×</button>
			<button class="btn btn-outline-secondary operator-btn flex-1 py-1 fs-18">÷</button>
			<button class="btn btn-outline-secondary operator-btn flex-1 py-1 fs-18">(</button>
			<button class="btn btn-outline-secondary operator-btn flex-1 py-1 fs-18">)</button>
		</div>
		<div class="mb-3">
			<label class="form-label">Enter value for expression </label>
			<input type="text" class="form-control" id="typeValue" placeholder="Type a value..."/>
			<div class="invalid-feedback" style="display: none" id="typeValueError">
				Only numbers and math symbols (e.g. + - × ÷ .) are allowed.
			</div>
		</div>
		<div id="expressionPreview" class="form-control mb-3 d-flex flex-wrap align-items-center gap-2 p-2" style="min-height: 70px">
			<i class="text-info">Custom metrics will appear here. Drag and drop to reorder them.</i>
		</div>
		<div class="mb-2">
			<label class="form-label">Metric column</label>
			<pre id="expressionMetric" class="bg-white border rounded p-2" Style="min-height: 40px;"></pre>
		</div>
		<div class="d-flex gap-3 mb-3">
			<div class="flex-1">
				<label class="form-label" for="customMetricName">Name<i class="text-danger fs-10">(*)</i></label>
				<input type="text" class="form-control" id="customMetricName" placeholder="Name this metric" maxlength="100"/>
			</div>
			<div class="flex-1">
				<label class="form-label" for="customMetricNamePrivate">Name(Private)</label>
				<input type="text" class="form-control" id="customMetricNamePrivate" placeholder="Private mame this metric" maxlength="100"/>
			</div>
			<div class="">
				<label class="form-label">Format</label>
				<select class="form-select" id="customMetricFormat">
					<option value="int64">Numeric (123)</option>
					<option value="%">Percent (%)</option>
					<option value="float64">Decimal (1.23)</option>
					<option value="CURRENCY">Currency</option>
				</select>
			</div>
		</div>
		<div class="mb-0">
			<label class="form-label">Description <span class="text-muted">• Optional</span></label>
			<textarea class="form-control" id="customMetricDescription" rows="2" maxlength="350" placeholder="Describe this metric"></textarea>
		</div>
		// Error message save form
		<div id="saveFormError" class="alert alert-danger material-shadow mt-3 mb-0" role="alert" style="display: none;"></div>
	</div>
	@scriptCustomMetricColumnTable(data)
}

templ scriptCustomMetricColumnTable(data CustomColumnTableTempl) {
	{{
fieldsSelectData := []map[string]any{}
fields := map[string]string{}

if data.ReportFields != nil && len(*data.ReportFields) > 0 {
	for _, field := range *data.ReportFields {
		if field.IsReport {
			fields[field.Key] = field.Title
			fieldsSelectData = append(fieldsSelectData, map[string]any{
				"value":    field.Key,
				"label":    field.Title,
				"selected": false,
				"disabled": false,
			})
		}
	}
}
	}}
	<template id="fieldsStr">{ templ.JSONString(fields) }</template>
	<template id="fieldsSelectStr">{ templ.JSONString(fieldsSelectData) }</template>
	<script id="scriptCustomMetricColumnModal" type="module" defer>
   import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
   import { requestApi } from "/static/js/common/httpService.js";
   $(function () {
      let expressionTokens = [];
      let fields = JSON.parse($('#fieldsStr').html());
      let fieldsSelect = JSON.parse($('#fieldsSelectStr').html());

      $('#fieldsStr').remove();
      $('#fieldsSelectStr').remove();


      const catalogueChoices = new Choices('#selectMetric', {
         shouldSort: false,
         allowHTML: true,
         removeItemButton: true,
         renderSelectedChoices: "auto",
         searchEnabled: true,
         placeholder: true,
         choices: fieldsSelect,
      });
      catalogueChoices.containerOuter.element.classList.add("mb-0");
      catalogueChoices.containerOuter.element.style.flexGrow = "2";

      /**
      * Render the expression tokens visually and update text display
      */
      function renderExpression() {
         const $preview = $('#expressionPreview').empty();
         let expressionView = ''

         if (!expressionTokens.length) {
            $preview.append('<i class="text-info">Custom metrics will appear here. Drag and drop to reorder them.</i>');
            $('#expressionMetric').text('');
            return;
         }

         expressionTokens.forEach((token, index) => {
            const value = /^(\d+(\.\d+)?|\.\d+|[+\-×÷()])$/.test(token) ? token : fields[token] || token;
            expressionView += value + " ";
            const $tag = $(`<span class="badge fw-medium fs-14 text-dark border d-inline-flex align-items-center px-2 py-1" style="cursor: grab;"></span>`).text(value);
            const $remove = $(`<i class="ms-2 ps-1 border-left text-danger fw-normal" style="cursor:pointer;font-size: 1.2rem; border-left: 1px solid #d3d3d3; padding-left: 8px;">&times;</i>`)
               .on('click', () => {
                  expressionTokens.splice(index, 1);
                  renderExpression();
               });
            $tag.append($remove).appendTo($preview);
         });

         $('#expressionMetric').text(expressionView.trim());

         // Make expression sortable
         new Sortable($preview[0], {
            animation: 150,
            ghostClass: 'sortable-ghost',
            onEnd: ({ oldIndex, newIndex }) => {
               const moved = expressionTokens.splice(oldIndex, 1)[0];
               expressionTokens.splice(newIndex, 0, moved);
               renderExpression();
            }
         });
      }

      /**
      * Parse String Metric Expression
      */
      function parseMetricExpression(metric) {
         const replaced = metric
            .replace(/\//g, "÷")
            .replace(/\*/g, "×");

         const tokens = [];
         let buffer = '';
         let inPlaceholder = false;

         for (let char of replaced) {
            if (char === '{') {
               inPlaceholder = true;
               buffer = '';
            } else if (char === '}') {
               inPlaceholder = false;
               tokens.push(buffer);
               buffer = '';
            } else if (inPlaceholder) {
               buffer += char;
            } else if (/[()\-\+×÷]/.test(char)) {
               if (buffer.trim()) {
                  tokens.push(buffer.trim());
                  buffer = '';
               }
               tokens.push(char);
            } else {
               buffer += char;
            }
         }

         if (buffer.trim()) {
            tokens.push(buffer.trim());
         }

         return tokens;
      }

      /**
      * Validate custom metric expression tokens
      */
      function isValidMetricExpression(tokens) {
         if (!tokens.length) return false;

         const ops = ['+', '-', '×', '÷'];
         const stack = [];
         let last = null;

         for (let i = 0; i < tokens.length; i++) {
            const cur = tokens[i].trim();
            const next = tokens[i + 1]?.trim();

            if (cur === '(') {
               stack.push(cur);
               if (next && [')', ...ops].includes(next)) return false;
            }

            if (cur === ')') {
               if (!stack.length || (last && ops.includes(last))) return false;
               stack.pop();
            }

            if (ops.includes(cur)) {
               if (i === 0 || i === tokens.length - 1) return false;
               if (!next || next === ')' || ops.includes(next) || last === '(') return false;
            }

            if (cur === '÷' && (next === '0' || next === '0.0')) return false;
            if (last === ')' && cur !== ')' && !ops.includes(cur)) return false;
            last = cur;
         }

         return stack.length === 0;
      }

      /**
      * Wrap variable names in curly braces for expression
      */
      function normalizeExpression(expr) {
         return expr
            .replace(/×/g, '*')
            .replace(/÷/g, '/')
            .replace(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g, match => `{${match}}`);
      }

      /**
      * Reset all input fields and state
      */
      function resetCustomMetricForm() {
         $('#customMetricName, #customMetricDescription, #selectMetric, #typeValue, #customMetricNamePrivate').val('');
         $('#customMetricFormat').val('int64');
         $('#expressionMetric, #saveFormError').text('');
         $('#typeValueError').hide();
         $('#selectMetric').val('');
         $('#expressionPreview').empty();
         $("#customMetricColumnModal #saveCustomMetricBtn").attr("data-id", "");
         $('#saveFormError').text('').hide(120);

         expressionTokens = [];
         renderExpression();
      }

      /**
      * Request create custom metric
      */
      function requestCreateCustomMetric(metricData) {
         loader();
         requestApi("POST", "/dsp/tiktok/api/custom-column-table/create-metric-column", metricData)
            .then((response) => {
               successAlert("Custom metric saved successfully!");
               setTimeout(() => {
                  $("#customMetricColumnModal").modal('hide');
                  window.customColumnModalContent?.renderCustomMetricList(response?.data?.data || {})
                  resetCustomMetricForm();
               }, 500);
               return true;
            })
            .catch((error) => {
               errorAlert("Failed to create custom metric: " + error?.details?.msg);
               return false;
            })
            .finally(() => {
               loader(false);
            });
      }

      /**
      * Request update metric column
      */
      function requestUpdateCustomMetric(metricData) {
         loader();
         requestApi("PATCH", "/dsp/tiktok/api/custom-column-table/update-metric-column", metricData)
            .then((response) => {
               successAlert("Update metric saved successfully!");
               setTimeout(() => {
                  $("#customMetricColumnModal").modal('hide');
                  window.customColumnModalContent?.renderCustomMetricList(metricData)
                  resetCustomMetricForm();
               }, 500);
               return true;
            })
            .catch((error) => {
               errorAlert("Failed to update metric: " + error?.msg);
               return false;
            })
            .finally(() => {
               loader(false);
            });
      }

      // Render Edit Metric Content
      function renderEditMetricContent(metric) {
         resetCustomMetricForm();
         $("#customMetricColumnModal #saveCustomMetricBtn").attr("data-id", metric.id);
         expressionTokens = parseMetricExpression(metric.metric);
         $('#customMetricName').val(metric.title);
         $('#customMetricNamePrivate').val(metric.title_private);
         const format = metric.unit === "%" || metric.unit === "CURRENCY" ? metric.unit : metric.type;
         $('#customMetricFormat').val(format);
         $('#customMetricDescription').val(metric.description);
         expressionTokens = parseMetricExpression(metric.metric);
         renderExpression();
      }

      /**
      * Handle Save Custom Metric
      */
      $('#saveCustomMetricBtn').on('click', function () {
         const name = $('#customMetricName').val().trim();
         const title_private = $('#customMetricNamePrivate').val().trim();
         const format = $('#customMetricFormat').val();
         const desc = $('#customMetricDescription').val().trim();
         const $err = $('#saveFormError').text('').hide(120);

         if (!name) return $err.text("Please enter a name for the custom metric.").show(120);
         if (!expressionTokens.length) return $err.text("Expression cannot be empty.").show(120);
         if (!isValidMetricExpression(expressionTokens)) return $err.text("Invalid metric expression.").show(120);

         const formatValue = $("#customMetricFormat").val();
         const metricData = {
            title: name,
            title_private: title_private ? title_private : null,
            unit: (formatValue === "int64" || formatValue === "float64") ? "" : formatValue,
            type: (formatValue === "%" || formatValue === "CURRENCY") ? "float" : formatValue,
            format,
            description: desc,
            metric: normalizeExpression(expressionTokens.join(" ").replaceAll(" ", ""))
         };

         if ($("#customMetricColumnModal #saveCustomMetricBtn").attr("data-id")) {
            const id = $("#customMetricColumnModal #saveCustomMetricBtn").attr("data-id") || "";
            requestUpdateCustomMetric({
               ...metricData,
               id: id
            });
         } else {
            requestCreateCustomMetric(metricData);
         }

      });

      /**
           * Add metric from dropdown
           */
      $('#selectMetric').on('change', function () {
         // const label = $(this).find('option:selected').text();
         if (this.value) {
            expressionTokens.push(this.value);
            renderExpression();
            $(this).val('');
         }
      });

      /**
      * Add operator to expression
      */
      $('.operator-btn').on('click', function () {
         expressionTokens.push($(this).text().trim());
         renderExpression();
      });

      /**
      * Handle input for numeric and math-only values (Enter to add)
      */
      $('#typeValue').on('keydown', debounce(function (e) {
         $('#typeValueError').hide(150);
         if (e.key !== 'Enter') return;

         let val = $(this).val().trim().replace(/\*/g, '×');
         const validPattern = /^(\d+(\.\d+)?|\.\d+|[+\-×÷()])$/;

         if (!validPattern.test(val)) {
            $('#typeValueError').show(150);
            return;
         }

         if (val !== '') {
            expressionTokens.push(val);
            renderExpression();
            $(this).val('');
         }
      }, 200));

      /**
      * On close modal, reset form
      */
      $('#customMetricColumnModal').on('hidden.bs.modal', function () {
         resetCustomMetricForm();
      });

      window.customMetricColumnContentTable = {
         renderEditMetricContent: renderEditMetricContent,
      }
   })
</script>
}

templ styleCustomMetricColumnTable() {
	<style>
   #customMetricColumnModal #expressionPreview span.badge:hover {
      background-color: var(--neutral-700);
   }

   #customMetricColumnModal #expressionPreview span.badge {
      background-color: var(--neutral-800);

   }

   #customMetricColumnModal #expressionPreview span.badge:hover i {
      display: inline-block;
   }

   #customMetricColumnModal #expressionPreview span.badge i {
      display: none;
   }

   #customMetricColumnModal,
   .modal-nested {
      z-index: 1060 !important;
   }

   .modal-backdrop+.modal-backdrop {
      z-index: 1055 !important;
   }
</style>
}
