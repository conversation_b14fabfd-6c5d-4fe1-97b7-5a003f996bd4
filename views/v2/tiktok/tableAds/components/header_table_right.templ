package components

templ HeaderTableRight(data LayoutHeaderTableData) {
	<div class="flex-shrink-0 d-flex gap-1">
		if data.AuthPermission == nil {
			<div class="d-flex flex-wrap align-items-center gap-2" id="listTableRighttHeader">
				<!-- Trigger button -->
				<div class="d-flex gap-1">
					<button
						class="btn btn-light waves-effect d-flex gap-2 py-1 align-items-center"
						data-bs-toggle="modal"
						data-bs-target="#customColumnModal"
						style="padding: 0px 10px;"
					>
						<i class="ri-equalizer-line align-middle"></i>
						<div class="d-flex flex-column">
							<span class="mt-1" style="line-height: 1;">Custom Columns</span>
							<span class="text-muted fs-10" id="presetNameView" style="line-height:1.3;"></span>
						</div>
					</button>
					<button class="btn btn-light waves-effect d-flex gap-2 py-1 align-items-center" id="btnExportExcel" style="height: 40px;">
						<i class="ri-file-excel-2-line"></i>
						<span>Export excel</span>
					</button>
				</div>
			</div>
			@scriptHeaderTableRight()
		} else {
			@CustomViewTable(data)
		}
	</div>
}

templ scriptHeaderTableRight() {
	<script type="module" defer>
	$('#btnExportExcel').on('click', function () {
		$('#btnExportExcelTable').trigger('click');
	});
</script>
}
