package components

import ()

templ SearchFilterTable() {
	<div
		class="input-group input-group-sm flex-nowrap"
		style="margin-top: -5px; margin-bottom: -5px; white-space: nowrap;"
	>
		<input
			type="text"
			class="form-control"
			id="valueSearchTable"
			placeholder="Search campaign by Name or Id"
			style="min-width: 220px;"
		/>
		<button class="btn btn-success" type="button" id="btnSearchTable">
			Search <i class="ri-search-2-line align-middle"></i>
		</button>
	</div>
	@scriptSearchTable()
}

templ scriptSearchTable() {
	<script type="module" defer>
		function onSearchTable() {
        $("#btnSearchTable").on("click", function () {
         	removeParamURL("page");

				const searchValue = $("#valueSearchTable").val().trim().toLowerCase();
				const isNumericSearch = /^\d+$/.test(searchValue);
				const search = {}
				
				if(/^\d+$/.test(searchValue)){
					search.id = searchValue;
				} else {
					search.search = searchValue;
				}

				document.dispatchEvent(
						new CustomEvent("onUpdateFilterChange", {
							detail: {
								...search,
								label: "search value"
							},
						})
				);

        });
        $("#valueSearchTable").on("keydown", function (e) {
            if (e.key === "Enter" || e.keyCode === 13) {
                $("#btnSearchTable").click();
            }
        });
    }
		$(document).ready(function(){
				onSearchTable();
		})
	</script>
}
