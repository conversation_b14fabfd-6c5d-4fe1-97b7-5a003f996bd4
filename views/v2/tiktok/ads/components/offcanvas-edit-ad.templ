package components

import "godsp/modules/tiktok/ad/transport/responses"

templ OffcanvasEditAdCpn(ad *responses.DetailAdResp, preview *responses.AdPreviewResp) {
	<div class="data-blk">
		@AdHeaderOffcanvas(ad)
		<!-- // Offcanvas content -->
		<form class="off-canvas-content__ad_form tiktok-custom" id="ad-form">
			// @CampaignAndAdGroupHiddenInput(ad)
			<div class="container d-flex flex-1">
				<span></span>
				<div class="row flex-1 position-relative">
					<!-- Main Container -->
					<div class="col-8 d-flex flex-column position-relative ">
						<div class=" container-input-control d-flex flex-column mt-3 flex-1 ">
							@AdNameCpn(ad)
							@IdentityCpn(ad)
							@ProductDetailsCpn(ad)
							@AdDetailsCpn(ad)
							@TrackingCpn(ad)
							@PolicyCpn(ad)
						</div>
						<!-- // Footer Container -->
						<div class="form-footer p-3 bg-white border-top" id="">
							<div class="d-flex justify-content-between">
								<div class="d-flex flex-row gap-2 align-items-center">
									<div class="" id="alert-auto-save">
										<span class="d-none save-success text-success fs-16">
											Auto save success
											<i class="ri-check-double-line"></i>
										</span>
										<span class="d-none save-fail text-danger">
											Auto save fail
											<i class="ri-close-circle-line"></i>
										</span>
									</div>
								</div>
								<div>
									<button id="btn-save-form-off-canvas" type="button" class="btn btn-success waves-effect waves-light">
										Save
									</button>
								</div>
							</div>
						</div>
					</div>
					<!-- Right Container -->
					<div class="col-4 d-flex flex-column position-sticky mt-3 top-0">
						<div class="position-sticky" style="top:75px">
							@AdPreview(ad, preview)
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>
}
