package ad_preview

import (
	adEnums "godsp/modules/tiktok/ad/common/enums"
	"godsp/modules/tiktok/ad/transport/responses"
)

templ AdPreviewPlacementSelectCpn(ad *responses.DetailAdResp) {
	if ad.AdGroup.TiktokSubplacements != nil {
		<div class="ad-preview__placement-select mt-3">
			<select class="form-select" id="ad-preview__placement-select">
				for index ,placement := range ad.AdGroup.TiktokSubplacements {
					if index == 0 {
						<option value={ placement } selected>{ adEnums.AdPreviewSubplacements(placement).DisplayName() }</option>
					} else {
						<option value={ placement }>{ adEnums.AdPreviewSubplacements(placement).DisplayName() }</option>
					}
				}
			</select>
		</div>
	}
}
