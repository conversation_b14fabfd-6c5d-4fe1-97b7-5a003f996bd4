package components

import (
	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
	"godsp/modules/tiktok/ad/transport/responses"
	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
	campaignEnums "godsp/modules/tiktok/campaign/common/enums"
	"godsp/views/v2/tiktok/ads/components/tracking"
)

templ TrackingCpn(ad *responses.DetailAdResp) {
	if isShowingTrackingCpn(ad) {
		<div class="ad-component_tracking card ">
			<div class="card-body  p-4">
				<h5>Tracking <span class="text-muted">(optional)</span></h5>
				<div class="d-flex flex-column gap-4 mt-4">
					@tracking.EventTrackingCpn()
					@tracking.ThirdPartyTrackingCpn()
				</div>
			</div>
		</div>
	}
}

type AdTrackingRule struct {
	Visible              bool
	ShopAdsType          map[v13.ShoppingAdsType]AdTrackingRule
	OptimizationLocation map[string]AdTrackingRule
}

var AD_TRACKING_RULE = map[string]AdTrackingRule{
	campaignEnums.PRODUCT_SALES: {
		ShopAdsType: map[v13.ShoppingAdsType]AdTrackingRule{
			adgroupEnums.ShoppingAdsTypeVSA:     {Visible: false},
			adgroupEnums.ShoppingAdsTypeLive:    {Visible: false},
			adgroupEnums.ShoppingAdsTypeProduct: {Visible: false},
		},
	},
	campaignEnums.VIDEO_VIEWS: {Visible: true},
	campaignEnums.TRAFFIC: {
		OptimizationLocation: map[string]AdTrackingRule{
			adgroupEnums.PROMOTION_TYPE_WEBSITE: {Visible: true},
			adgroupEnums.PROMOTION_TYPE_APP:     {Visible: true},
		},
	},
	campaignEnums.REACH: {Visible: true},
}

func isShowingTrackingCpn(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.Campaign == nil || ad.AdGroup == nil {
		return false
	}

	rule, ok := AD_TRACKING_RULE[ad.Campaign.ObjectiveType]
	if !ok {
		return false
	}

	if ad.AdGroup.ShoppingAdsType != "" && rule.ShopAdsType != nil {
		if subRule, ok := rule.ShopAdsType[ad.AdGroup.ShoppingAdsType]; ok {
			return subRule.Visible
		}
	}

	if ad.AdGroup.PromotionType != "" && rule.OptimizationLocation != nil {
		if subRule, ok := rule.OptimizationLocation[ad.AdGroup.PromotionType]; ok {
			return subRule.Visible
		}
	}

	return rule.Visible
}
