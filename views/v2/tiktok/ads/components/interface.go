package components

import (
	clientE "godsp/modules/admin/client/entity"

	"godsp/modules/facebook/iface"
	"godsp/modules/tiktok/ad/transport/responses"
)

type EditAdsLayoutData struct {
	FlashMsg       string         `json:"flash_msg"`
	AuthPermission map[string]int `json:"auth_permission" `
	UserInfo       iface.UserInfoAuth
	Clients        []*clientE.ClientEntity
	Data           responses.DetailAdResp `json:"data"`
}
