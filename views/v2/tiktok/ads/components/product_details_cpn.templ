package components

import "godsp/modules/tiktok/ad/transport/responses"
import adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
import campaignEnums "godsp/modules/tiktok/campaign/common/enums"
import v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"

templ ProductDetailsCpn(ad *responses.DetailAdResp) {
	@renderProductDetailsCpn(shouldShowProductDetails(ad))
}

templ renderProductDetailsCpn(visible bool) {
	if visible {
		<div class="ad_component_product_details card">
			<div class="card-body p-4">
				<h5>Product details</h5>
				<small class="text-muted">Add products from - <span class="ad_component_product_details_target"></span></small>
				<div class="mt-3">
					<div class="mb-3">
						<h6 class="fw-bold m-0">Products</h6>
						<small class="text-muted mb-2">
							Only available products can be selected to deliver ads. This includes reviewed
							products with sufficient inventory.
							<span class="ad_component_product_details_target"></span>
						</small>
					</div>
					@productSetSelect()
				</div>
			</div>
		</div>
	}
}

templ productSetSelect() {
	<select class="form-select">
		<option>All poroducts</option>
	</select>
}

type ProductDetailsRule struct {
	Visible              bool
	ShopAdsType          map[v13.ShoppingAdsType]ProductDetailsRule
	OptimizationLocation map[string]ProductDetailsRule
}

var PRODUCT_DETAILS_RULE = map[string]ProductDetailsRule{
	campaignEnums.PRODUCT_SALES: {
		ShopAdsType: map[v13.ShoppingAdsType]ProductDetailsRule{
			adgroupEnums.ShoppingAdsTypeVSA:     {Visible: false},
			adgroupEnums.ShoppingAdsTypeLive:    {Visible: false},
			adgroupEnums.ShoppingAdsTypeProduct: {Visible: true},
		},
	},
	campaignEnums.VIDEO_VIEWS: {Visible: false},
	campaignEnums.TRAFFIC: {
		OptimizationLocation: map[string]ProductDetailsRule{
			adgroupEnums.PROMOTION_TYPE_WEBSITE: {Visible: false},
			adgroupEnums.PROMOTION_TYPE_APP:     {Visible: false},
		},
	},
	campaignEnums.REACH: {Visible: false},
}

func shouldShowProductDetails(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.Campaign == nil || ad.AdGroup == nil {
		return false
	}

	rule, ok := PRODUCT_DETAILS_RULE[ad.Campaign.ObjectiveType]
	if !ok {
		return false
	}

	if ad.AdGroup.ShoppingAdsType != "" && rule.ShopAdsType != nil {
		if subRule, ok := rule.ShopAdsType[ad.AdGroup.ShoppingAdsType]; ok {
			return subRule.Visible
		}
	}

	if ad.AdGroup.PromotionType != "" && rule.OptimizationLocation != nil {
		if subRule, ok := rule.OptimizationLocation[ad.AdGroup.PromotionType]; ok {
			return subRule.Visible
		}
	}

	return rule.Visible
}
