package components

import "godsp/modules/tiktok/ad/transport/responses"

templ AdNameCpn(ad *responses.DetailAdResp) {
	<div class="ad-component__ad-name card ">
		<div class="card-body p-4">
			<h5>Ad name</h5>
			<div>
				<div class="form-group mt-3">
					<input
						id="ad_name"
						class="form-control"
						value={ GetAdNameDisplay(ad) }
					/>
					<button type="button" class="btn btn-ghost-info waves-effect waves-light d-none">
						+ Add creative
						name
					</button>
				</div>
			</div>
		</div>
	</div>
}

func GetAdNameDisplay(ad *responses.DetailAdResp) string {
	if ad != nil {
		return ad.AdName
	}
	return "New ad"
}
