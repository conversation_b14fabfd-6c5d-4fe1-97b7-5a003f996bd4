package ad_details

import (
	adEnums "godsp/modules/tiktok/ad/common/enums"
	"godsp/modules/tiktok/ad/transport/responses"
	"godsp/views/v2/tiktok/ads/partials"
)

func ptr(s string) *string {
	return &s
}

templ AdFormatCpn(ad *responses.DetailAdResp) {
	if ad != nil {
		<div class="ad_details_ad_format">
			<h6 class="fw-semibold">Ad format</h6>
			<div
				class="btn-group-vertical gap-3 w-100 ad-format-btn"
				role="group"
				aria-label="Vertical radio toggle button group"
			>
				if  ad.AdFormat == adEnums.SINGLE_VIDEO {
					@adFormatInput("ad-format-single-video", "Single video", adEnums.SINGLE_VIDEO, "Create ad with a single video or a TikTok video post. ", "ri-movie-line", ptr("Choose how you present your business in your ads."), true)
				}
				if ad.AdFormat == adEnums.CAROUSEL_ADS {
					@adFormatInput("ad-format-carousel-images", "Carousel images", adEnums.CAROUSEL_ADS, "Create an ad with 1-35 images, displayed in a carousel. ", "bx bx-images", ptr("Choose how you present your business in your ads."), true)
				}
				if ad.AdFormat == adEnums.LIVE_CONTENT {
					@adFormatInput("ad-format-realtime-live", "Real-time LIVE", adEnums.LIVE_CONTENT, "Stream content in real-time", "ri-live-line", nil, true)
				}
			</div>
		</div>
	}
}

templ adFormatInput(id string, formatType string, inputValue string, description string, iconClass string, popoverContent *string, check bool) {
	<input value={ inputValue } type="radio" class="btn-check" name="ad-format-btn" id={ id } checked={ check }/>
	<label class="btn btn-outline rounded" for={ id }>
		<div class="d-flex flex-row gap-3 align-items-start">
			<div class="flex-auto">
				<i class={ iconClass + " fs-20" }></i>
			</div>
			<div class="btn-content flex-grow-1 d-flex flex-column ">
				<strong>{ formatType }</strong>
				<small class="text-muted">
					{ description }
					if popoverContent != nil {
						@partials.PopoverInfo(*popoverContent)
					}
				</small>
			</div>
		</div>
	</label>
}
