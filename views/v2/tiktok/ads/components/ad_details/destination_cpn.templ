package ad_details

import (
	"godsp/modules/tiktok/ad/transport/responses"
	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
	campaignEnums "godsp/modules/tiktok/campaign/common/enums"
	"godsp/views/v2/tiktok/ads/partials"
	"slices"
)

templ DestinationCpn(ad *responses.DetailAdResp) {
	if ad != nil && isShowingDestinationCpn(ad) {
		<div class="mt-3 destination-cpn">
			<hr/>
			<div class="accordion-item trd-custom border rounded border-opacity-50" id="destination-accordion-item">
				<div class="accordion-header accordion-button justify-content-between ps-0 pe-2">
					<div class="d-flex gap-2 align-items-center py-2 flex-1">
						<span
							class="avatar-title bg-soft-light fs-5 px-2 py-1 border-end btn-toggle collapsed"
							data-bs-toggle="collapse"
							data-bs-target="#destinationAdDetails"
							aria-expanded="false"
							style="flex: 0 1 0%;"
						>
							<i
								class="ri-play-fill text-black-50"
							></i>
						</span>
						<div class="fs-14 fw-bold">
							<p class="mb-0">Destination</p>
						</div>
					</div>
				</div>
				<div id="destinationAdDetails" class="accordion-collapse multi-collapse collapse">
					<div class="accordion-body p-3 d-flex flex-column gap-3">
						@DestinationTypeOptions(ad, isShowingDestinationTypeCpn(ad))
						<div class="destination_setup mt-3">
							if isCheckDestinationWebsiteType(ad) {
								<div class="destination_website_content">
									<h6 class="fw-bold">
										Destination URL 
										<span
											type="button"
											data-container="body"
											data-toggle="popover"
											data-placement="top"
											data-content="Choose how you present your business in your ads."
										>
											<i class="ri ri-question-fill"></i>
										</span>
									</h6>
									<small class="text-muted">
										By selecting a webpage, you are granting TikTok
										permission
										to scan, download, and modify images, videos, and other
										assets located on that webpage, and you are confirming that you own the
										necessary legal rights to the images, videos,
										and assets located on the webpage and have permission to share the assets
										with
										TikTok for use on your behalf in
										advertising or for other commercial purposes.
									</small>
									<div class="d-flex flex-row gap-2 mt-3">
										<div class="d-flex flex-column gap-1 w-100">
											<input
												class="form-control"
												type="url"
												placeholder="Enter URL start with http:// or https://"
												value={ *ad.LandingPageUrl }
												name="destination_website_url"
											/>
											<div class="invalid-feedback">Enter a valid URL</div>
										</div>
										<button type="button" class="btn btn-light">Preview</button>
									</div>
									<div class="mt-4">
										<div class="form-check form-switch form-switch-lg p-0 ms-2" dir="ltr">
											<input
												type="checkbox"
												class="form-check-input m-0"
												id="destination_url_deeplink_first_toggle"
												checked?={ isCheckDeeplinkDestination(ad) }
											/>
											<label
												class="form-check-label"
												for="destination_url_deeplink_first_toggle"
											>
												Direct users to deeplink
												first 
												@partials.PopoverInfo("Choose how you present your business in your ads.")
											</label>
										</div>
										if isCheckDeeplinkDestination(ad) {
											<div class="d-flex flex-column gap-1 destination_deeplink_input">
												<input
													type="url"
													class="form-control mt-2"
													name="destination_deeplink_input"
													value={ *ad.Deeplink }
													placeholder="Scheme, Universal, and App Link formast are supported"
												/>
												<div class="invalid-feedback">Enter a valid URL</div>
											</div>
										} else {
											<div class="d-flex flex-column gap-1 destination_deeplink_input d-none">
												<input
													type="url"
													class="form-control mt-2"
													name="destination_deeplink_input"
													value=""
													placeholder="Scheme, Universal, and App Link formast are supported"
												/>
												<div class="invalid-feedback">Enter a valid URL</div>
											</div>
										}
									</div>
								</div>
							}
							if isCheckDestinationInstantPageContentType(ad) {
								<div class="destination_tiktok_instant_page_content">
									<h6 class="fw-bold">Custom Page </h6>
									<div class="d-flex flex-row gap-2">
										<button type="button" class="btn btn-light">
											<i
												class="ri ri-add-fill label-icon align-middle fs-16 me-2  w-auto"
											></i>
											Create
										</button>
										<button type="button" class="btn btn-light">
											<i
												class="ri ri-add-fill label-icon align-middle fs-16 me-2  w-auto"
											></i>
											Choose
											from library
										</button>
									</div>
								</div>
							}
							// if isCheckDestinationAppType(ad) {
							// 	<div class="destination_app_content">
							// 		<div class="form-check form-switch form-switch-lg p-0 ms-2" dir="ltr">
							// 			<input
							// 				type="checkbox"
							// 				class="form-check-input m-0"
							// 				id="destination_url_deeplink_first_app_toggle"
							// 			/>
							// 			<label
							// 				class="form-check-label"
							// 				for="destination_url_deeplink_first_app_toggle"
							// 			>
							// 				Direct users to deeplink first
							// 				@partials.PopoverInfo("Choose how you present your business in your ads.")
							// 			</label>
							// 		</div>
							// 		<div class="destination-app-details mt-3">
							// 			<div class="destination-deeplink-input">
							// 				<input
							// 					type="url"
							// 					class="form-control mt-2"
							// 					name="destination_deeplink_input"
							// 					placeholder="Scheme, Universal, and App Link format are supported"
							// 				/>
							// 				<div class="invalid-feedback">Enter a valid URL</div>
							// 			</div>
							// 			<div class="destination-fallback-type mt-3">
							// 				<label class="fw-bold mb-2 d-block">
							// 					Fallback type
							// 					@partials.PopoverInfo("Choose how you present your business in your ads.")
							// 				</label>
							// 				<select class="form-select fallback-type-select">
							// 					<option value="app_store">App store</option>
							// 					<option value="website">Website</option>
							// 				</select>
							// 			</div>
							// 			<div class="destination-fallback-url mt-3">
							// 				<label class="fw-bold mb-1">Fallback URL</label>
							// 				<input class="form-control" placeholder="Enter URL"/>
							// 			</div>
							// 		</div>
							// 	</div>
							// }
						</div>
					</div>
				</div>
			</div>
		</div>
	}
}

func isShowingDestinationCpn(ad *responses.DetailAdResp) bool {
	if ad.Campaign.ObjectiveType == campaignEnums.REACH || ad.Campaign.ObjectiveType == campaignEnums.TRAFFIC || ad.Campaign.ObjectiveType == campaignEnums.VIDEO_VIEWS {
		return true
	}
	return false
}

type DESTINATION_TYPES string

const (
	DESTINATION_WEBSITE             DESTINATION_TYPES = "WEBSITE"
	DESTINATION_TIKTOK_INSTANT_PAGE DESTINATION_TYPES = "TIKTOK_INSTANT_PAGE"
	DESTINATION_APP                 DESTINATION_TYPES = "APP"
)

func isShowingDestinationTypeCpn(ad *responses.DetailAdResp) []DESTINATION_TYPES {
	var types []DESTINATION_TYPES

	switch ad.Campaign.ObjectiveType {
	case campaignEnums.REACH:
		types = append(types, DESTINATION_WEBSITE, DESTINATION_TIKTOK_INSTANT_PAGE)

	case campaignEnums.TRAFFIC:
		if ad.AdGroup.OptimizationGoal == adgroupEnums.PROMOTION_TYPE_WEBSITE {
			types = append(types, DESTINATION_WEBSITE, DESTINATION_TIKTOK_INSTANT_PAGE)
		} else if ad.AdGroup.OptimizationGoal == adgroupEnums.PROMOTION_TYPE_APP {
			types = append(types, DESTINATION_APP)
		}

	case campaignEnums.VIDEO_VIEWS:
		types = append(types, DESTINATION_WEBSITE)

	case campaignEnums.PRODUCT_SALES:

	}

	return types
}

templ DestinationTypeOptions(ad *responses.DetailAdResp, types []DESTINATION_TYPES) {
	<div class="btn-group gap-3 w-100 ad-format-btn destination-types" role="group" aria-label="Horizontal radio toggle button group">
		if slices.Contains(types, DESTINATION_WEBSITE) {
			@DestinationTypeItem("website", "destination-type-website-btn", "Website", "URL", isCheckDestinationWebsiteType(ad))
		}
		if slices.Contains(types, DESTINATION_TIKTOK_INSTANT_PAGE) {
			@DestinationTypeItem("tiktok_instant_page", "destination-type-tiktok-instant-page-btn", "TikTok Instant Page", "Custom Page", isCheckDestinationInstantPageContentType(ad))
		}
		if slices.Contains(types, DESTINATION_APP) {
			@DestinationTypeItem("app", "destination-type-app-btn", "App", "Google Play", isCheckDestinationAppType(ad))
		}
	</div>
}

templ DestinationTypeItem(value string, btnID string, label string, desc string, checked bool) {
	<input
		type="radio"
		class="btn-check"
		name="destination-type-btn"
		id={ btnID }
		value={ value }
		checked?={ checked }
		disabled?={ !checked }
	/>
	<label class="btn btn-outline rounded w-50" for={ btnID }>
		<div class="d-flex flex-column gap-3 align-items-start">
			<small class="text-muted">{ label }</small>
			<h6 class="fw-bold">{ desc }</h6>
		</div>
	</label>
}

func isCheckDestinationWebsiteType(ad *responses.DetailAdResp) bool {
	// jsonData, _ := json.MarshalIndent(ad, "", "  ")
	// fmt.Println("\n-------- ad -------> \n", string(jsonData))
	if ad.LandingPageUrl != nil || len(ad.LandingPageUrls) > 0 {
		return true
	}
	return false
}

func isCheckDestinationInstantPageContentType(ad *responses.DetailAdResp) bool {
	return false
}

func isCheckDestinationAppType(ad *responses.DetailAdResp) bool {
	return false

}

func isCheckDeeplinkDestination(ad *responses.DetailAdResp) bool {
	if ad.Deeplink != nil && *ad.Deeplink != "" {
		return true
	}
	return false
}
