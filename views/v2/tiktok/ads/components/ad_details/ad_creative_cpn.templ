package ad_details

import (
	adEnums "godsp/modules/tiktok/ad/common/enums"
	"godsp/modules/tiktok/ad/transport/responses"
	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
	campaignEnums "godsp/modules/tiktok/campaign/common/enums"
	"godsp/views/v2/tiktok/ads/partials"
	"strconv"
)

templ AdCreativeCpn(ad *responses.DetailAdResp) {
	if ad != nil &&  ad.AdFormat != adEnums.LIVE_CONTENT {
		<div class="mt-3 ad-creative-cpn">
			<h6 class="fw-semibold">
				Ad creative
			</h6>
			<div class="ad-creative d-flex flex-column gap-4">
				<!-- Ad creative Post -->
				@adCreative(ad)
				<!-- Ad creative Text -->
				@adCreativeText(ad)
				<!-- Ad creative CTA -->
				if ad.AdGroup.ShoppingAdsType != adgroupEnums.ShoppingAdsTypeLive {
					if ad.Campaign.ObjectiveType == campaignEnums.VIDEO_VIEWS || ad.Campaign.ObjectiveType == campaignEnums.REACH {
						@adCreativeCtaToggle(ad)
					}
					if ad.Campaign.ObjectiveType == campaignEnums.TRAFFIC {
						@adCreativeCtaDefault()
					}
				}
				<!-- Ad creative Interactive add ons -->
				// @adCreativeInteractiveAddOns()
			</div>
		</div>
	}
}

templ adCreative(ad *responses.DetailAdResp) {
	// <div class="ad_component_ad_creative ad-creative-default">
	// 	<div class="media-btns mt-3">
	// 		// 		<button
	// 		// 			type="button"
	// 		// 			class="btn btn-light"
	// 		// 			data-bs-toggle="modal"
	// 		// 			data-bs-target="#modal_add_creative"
	// 		// 		>
	// 		// 			<i
	// 		// 				class="ri ri-add-fill label-icon align-middle fs-16 me-2  w-auto"
	// 		// 			></i>
	// 		// 			Add videos or images
	// 		// 		</button>
	// 		// 		<button
	// 		// 			type="button"
	// 		// 			class="btn btn-light"
	// 		// 		>
	// 		// 			<i
	// 		// 				class="ri ri-add-fill label-icon align-middle fs-16 me-2  w-auto"
	// 		// 			></i>
	// 		// 			Create new
	// 		// 		</button>
	// 	</div>
	// </div>
	// <div class="ad_component_ad_creative ad-creative-post">
	// 	<span class="fw-semibold">Video</span>
	// 	<div class="media-btns mt-3">
	// 		<button
	// 			type="button"
	// 			class="btn btn-light"
	// 			data-bs-toggle="modal"
	// 			data-bs-target="#modal_post_gallery"
	// 		>
	// 			<i
	// 				class="ri ri-add-fill label-icon align-middle fs-16 me-2  w-auto"
	// 			></i>
	// 			Tiktok post
	// 		</button>
	// 		<button
	// 			type="button"
	// 			class="btn btn-light"
	// 			data-bs-toggle="modal"
	// 			data-bs-target="#modal_add_creative"
	// 		>
	// 			<i
	// 				class="ri ri-add-fill label-icon align-middle fs-16 me-2  w-auto"
	// 			></i>
	// 			Add videos or images
	// 		</button>
	// 		<div class="btn-group" role="group">
	// 			<button
	// 				id="media-video"
	// 				type="button"
	// 				class="btn btn-light dropdown-toggle  w-auto"
	// 				data-bs-toggle="dropdown"
	// 				aria-haspopup="true"
	// 				aria-expanded="false"
	// 				style="display:none;"
	// 			>
	// 				<i
	// 					class="ri ri-add-fill label-icon align-middle fs-16 me-2 "
	// 				></i>
	// 				Video
	// 			</button>
	// 			<div class="dropdown-menu" aria-labelledby="media-video">
	// 				<a class="dropdown-item" href="#">Dropdown link</a>
	// 				<a class="dropdown-item" href="#">Dropdown link</a>
	// 			</div>
	// 		</div>
	// 		<button type="button" class="btn btn-light w-auto" style="display:none;">
	// 			<i
	// 				class="ri ri-add-fill label-icon align-middle fs-16 me-2 "
	// 			></i>
	// 			Tiktok photo post
	// 		</button>
	// 	</div>
	// </div>
	<div id="ad-creative-select-result" class="d-flex flex-column flex-md-row gap-3 px-3 bg-light">
		<div
			class="rounded align-content-center w-md-25 image-square-container"
		>
			<img src={ getCreativeMedia(ad) } class="w-100"/>
		</div>
		<div class="d-flex flex-row gap-3 flex-grow-1 py-3">
			<div class="media-name-wrapper w-50">
				// <span id="ad-creative-media-name" class="text-truncate d-inline-block w-100"></span>
			</div>
			<div class="media-info-wrapper w-50 text-muted">
				<span id="ad-creative-length">{ getCreativeDuration(ad) } </span>
				<span>| </span>
				<span id="ad-creative-width-height">{ getCreativeSize(ad) }</span>
			</div>
		</div>
	</div>
}

templ adCreativeText(ad *responses.DetailAdResp) {
	<div class="ad-creative-text">
		<span class="fw-semibold">
			Text 
			@partials.PopoverInfo("Choose how you present your business in your ads.")
		</span>
		<textarea class="form-control mt-3" type="text" id="ad-creative-text" row="3" style="resize: none; min-height: 31px; height: 115px;" placeholder="Enter a text" disabled>{ getCreativeText(ad) }</textarea>
	</div>
}

templ adCreativeCtaToggle(ad *responses.DetailAdResp) {
	<div class="ad-creative-cta toggle-cta mt-3">
		<div class="form-check form-switch form-switch-lg p-0 ms-2" dir="ltr">
			<input type="checkbox" class="form-check-input m-0" id="ad_creative_cta_toggle" checked?={ isToggleCallToAction(ad) } disabled/>
			<label class="form-check-label" for="customSwitchsizelg">
				Call to action 
				@partials.PopoverInfo("Choose how you present your business in your ads.")
			</label>
		</div>
		<div class="ad-creative-cta-setup d-flex flex-column gap-3">
			<div>
				<div class="form-check form-check-success ms-2 mt-3">
					<input
						class="form-check-input"
						type="radio"
						name="cta-type"
						id="cta-type-dynamic"
						value="dynamic"
					/>
					<label class="form-check-label d-flex flex-column pt-1" for="cta-type-dynamic">
						Dynamic
						<small class="text-muted" name="cta-type-dynamic-content">
							Automatically show
							different
							call to action text to
							different users to maximize performance
						</small>
					</label>
				</div>
				<div class="mt-2 ms-4" name="cta-type-dynamic-content">
					<button
						type="button"
						class="btn btn-ghost-success d-flex align-items-center justify-content-center gap-1 w-auto align-self-center p-1"
						data-bs-toggle="modal"
						data-bs-target="#modal_dynamic_cta_text_options"
					>
						<i class="ri ri-eye-line fs-20"></i>
						View selected text 
					</button>
				</div>
			</div>
			<div>
				<div class="form-check form-check-success ms-2">
					<input
						class="form-check-input"
						type="radio"
						name="cta-type"
						id="cta-type-standard"
						value="standard"
						checked?={ isStandardCallToAction(ad) }
					/>
					<label class=" form-check-label d-flex flex-column pt-1" for="cta-type-dynamic">
						Standard
					</label>
				</div>
				<div class="cta-select mt-4" name="cta-type-standard-content">
					<select class="form-select" name="cta-type-select"></select>
				</div>
			</div>
		</div>
	</div>
}

templ adCreativeCtaDefault() {
	<div class="ad-creative-cta default-cta mt-3">
		<label class="form-check-label">
			Call to action 
			@partials.PopoverInfo("Choose how you present your business in your ads.")
		</label>
		<br/>
		<small class="text-muted">Maximize performance by automatically showing different call to action text to different users.</small>
		<select
			class="form-select form-control w-75"
			id="cta-select"
			name="carrier"
			multiple
			data-placeholder="--- Choose actions ---"
			style="width: 100%"
		></select>
	</div>
}

templ AdCreativeInteractiveAddOnCard(title string, imagePath string) {
	<div class="ad-block d-flex flex-column">
		<span class="ad-title text-truncate" title={ title }>{ title }</span>
		<img class="w-100" src={ imagePath } alt={ title }/>
		<button type="button" class="btn btn-light mt-auto">Create</button>
	</div>
}

templ adCreativeInteractiveAddOns() {
	<div class="ad-creative-interactive-add-ons">
		<span class="fw-semibold">
			Interactive add-ons
			@partials.PopoverInfo("Choose how you present your business in your ads.")
		</span>
		<div class="media-btns mt-3 d-flex gap-2 flex-wrap">
			<button type="button" class="btn btn-light">
				<i class="ri ri-add-fill label-icon align-middle fs-16 me-2"></i>
				Create
			</button>
			<button type="button" class="btn btn-light">
				<i class="ri ri-add-fill label-icon align-middle fs-16 me-2"></i>
				Choose from library
			</button>
		</div>
		<div class="alert alert-dark alert-dismissible alert-outline fade show mt-3" role="alert">
			<div class="d-flex flex-column gap-2">
				<label class="fw-bold">
					<i class="ri-lightbulb-line me-1"></i>
					Recommended add-ons:
					<span class="fw-normal text-opacity-75">Based on ads performance</span>
				</label>
				<div class="d-flex flex-row gap-3 align-items-stretch">
					@AdCreativeInteractiveAddOnCard("Superlike", "/static/images/tiktok/ad_creative_super_like.png")
					@AdCreativeInteractiveAddOnCard("Display Card", "/static/images/tiktok/ad_creative_display_card.png")
					@AdCreativeInteractiveAddOnCard("Pop-Out Showcase with a very long name that should truncate", "/static/images/tiktok/ad_creative_popout_showcase.png")
				</div>
			</div>
			<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
		</div>
	</div>
}

func isToggleCallToAction(ad *responses.DetailAdResp) bool {
	if ad.CallToAction != "" || ad.CallToActionID != nil {
		return true
	}
	return false
}

func isStandardCallToAction(ad *responses.DetailAdResp) bool {
	if ad.CallToAction != "" {
		return true
	}
	return false
}

func formatDuration(seconds float64) string {
	mins := int(seconds / 60)
	secs := int(seconds) % 60
	return strconv.Itoa(mins) + `:` + strconv.Itoa(secs)
}

func getCreativeDuration(ad *responses.DetailAdResp) string {
	if ad.Post != nil {
		return formatDuration(ad.Post.VideoInfo.Duration)
	}
	return "unset"
}

func formatSize(height int, width int) string {
	return strconv.Itoa(width) + `:` + strconv.Itoa(height)
}

func getCreativeSize(ad *responses.DetailAdResp) string {
	if ad.Post != nil {
		return formatSize(ad.Post.VideoInfo.Height, ad.Post.VideoInfo.Width)
	}
	return "unset"
}

func getCreativeText(ad *responses.DetailAdResp) string {
	if ad.Post != nil {
		return ad.Post.Text
	}

	if ad.AdText != nil {
		return *ad.AdText
	}

	return "unknown"
}

func getCreativeMedia(ad *responses.DetailAdResp) string {
	if ad.Post != nil {
		return ad.Post.VideoInfo.PosterURL
	}

	return "unset"
}
