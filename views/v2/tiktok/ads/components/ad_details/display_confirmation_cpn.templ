package ad_details

import (
	"context"
	"fmt"
	"godsp/modules/tiktok/ad/transport/responses"
	campaignEnums "godsp/modules/tiktok/campaign/common/enums"
	"io"

	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
)

templ DisplayConfirmationCpn(ad *responses.DetailAdResp) {
	@renderDisplayConfirmation(ad)
}

type DisplayConfirmationFlags struct {
	ShowAIContent bool
	ShowBranded   bool
}

func shouldShowDisplayConfirmation(ad *responses.DetailAdResp) DisplayConfirmationFlags {
	flags := DisplayConfirmationFlags{}

	switch ad.Campaign.ObjectiveType {
	case campaignEnums.REACH, campaignEnums.VIDEO_VIEWS:
		flags.ShowBranded = true
	case campaignEnums.TRAFFIC:
		switch ad.AdGroup.OptimizationGoal {
		case adgroupEnums.PROMOTION_TYPE_WEBSITE, adgroupEnums.PROMOTION_TYPE_APP:
			flags.ShowBranded = true
		}
	case campaignEnums.PRODUCT_SALES:
		flags.ShowAIContent = false
	}

	return flags
}

func renderDisplayConfirmation(ad *responses.DetailAdResp) templ.Component {
	flags := shouldShowDisplayConfirmation(ad)

	if !flags.ShowAIContent && !flags.ShowBranded {
		return templ.ComponentFunc(func(ctx context.Context, w io.Writer) error {
			return nil // Không render gì cả
		})
	}

	return templ.ComponentFunc(func(ctx context.Context, w io.Writer) error {
		_, _ = fmt.Fprint(w, `<div class="display_confirmation_cpn"><hr/>`)

		if flags.ShowAIContent {
			_, _ = fmt.Fprint(w, renderAIContentCheckbox())
		}
		if flags.ShowBranded {
			_, _ = fmt.Fprint(w, renderBrandedCheckbox())
		}

		_, _ = fmt.Fprint(w, "</div>")
		return nil
	})
}

func renderAIContentCheckbox() string {
	return `
	<div class="form-check form-check-success ms-2 contains_ai_generated_content">
	<input class="form-check-input" type="checkbox" id="contains_ai_generated_content" checked/>
	<label class="form-check-label" for="contains_ai_generated_content">
		This ad contains AI-generated content
		<br/>
		<small>
		Our advertising policies require the labeling of AI-generated content.
		By clicking this checkbox you confirm that your content conforms to our guidelines for AI-generated content.
		This can't be changed once you submit the ad. Learn more
		</small>
	</label>
	</div>`
}

func renderBrandedCheckbox() string {
	return `
	<div class="form-check form-check-success ms-2 displayed_on_tiktok_branded_or_affiliated_platforms">
	<input class="form-check-input" type="checkbox" id="displayed_on_tiktok_branded_or_affiliated_platforms" checked/>
	<label class="form-check-label" for="displayed_on_tiktok_branded_or_affiliated_platforms">
		By checking the box, you authorize the ad and the associated performance metrics of the campaign to
		be displayed on TikTok branded or affiliated platforms
	</label>
	</div>`
}
