package components

import (
	"godsp/modules/tiktok/ad/transport/responses"
	"godsp/views/v2/tiktok/ads/components/ad_details"
	"godsp/views/v2/tiktok/ads/partials"

	adEnums "godsp/modules/tiktok/ad/common/enums"
	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
)

templ AdDetailsCpn(ad *responses.DetailAdResp) {
	<div class="ad_component_ad_details card">
		<div class="card-body  p-4">
			<h5>Ad details</h5>
			if isShowingAdDetailsLivestreamCpn(ad) && ad.AdFormat == adEnums.LIVE_CONTENT {
				<div class="ad-details-livestream-info">
					<div class="align-items-center  d-flex flex-row gap-2 text-muted">
						Your LIVE video will delivered from
						<div class="d-flex flex-row gap-2 align-items-center">
							<img src={ ad.Identity.ProfileImage } alt="" class="rounded-circle avatar-xxs"/>
							<span class="ad-details-livestream-channel">{ ad.Identity.DisplayName }</span>
						</div>
					</div>
				</div>
			}
			if ad.Post != nil && ad.Identity != nil {
				<div class="ad-details-post-info">
					<div class="align-items-center  d-flex flex-row gap-2 text-muted">
						Your post will delivered from
						<div class="d-flex flex-row gap-2 align-items-center">
							<img src={ ad.Identity.ProfileImage } alt="Identity avatar" class="rounded-circle avatar-xxs"/>
							<span class="ad-details-post-identity">{ ad.Identity.DisplayName }</span>
						</div>
					</div>
				</div>
			}
			<div class="mt-4">
				if isShowingAdDetailsDefaultSetupCpn(ad) {
					<div class="ad-details-type ad-details-setup ">
						<!--      Ad Format      -->
						@ad_details.AdFormatCpn(ad)
						<!--      Ad Creative      -->
						@ad_details.AdCreativeCpn(ad)
						<!--      Destination       -->
						@ad_details.DestinationCpn(ad)
						<!--      Display confirmation      -->
						@ad_details.DisplayConfirmationCpn(ad)
					</div>
				}
				if isShowingAdDetailsProductCpn(ad) {
					<div class="ad-details-type ad-details-product-set mt-5 ">
						@adProducts()
					</div>
				}
				if isShowingAdDetailsLivestreamCpn(ad) && ad.AdFormat == adEnums.LIVE_CONTENT {
					<div class="ad-details-type ad-details-livestream mt-5 ">
						@adLivestream()
					</div>
				}
				// <div class="ad-details-type ad-deatils-video-shopping"></div>
				if isShowingAdDetailsLivestreamCpn(ad) && ad.AdFormat != adEnums.LIVE_CONTENT {
					<div id="ad-details-cta-edit">
						@adDestinationCtaEdit(ad)
					</div>
				}
			</div>
		</div>
	</div>
}

templ adProducts() {
	<span class="">The product visuals will be used as your ad creative. Your product images will be automatically used as your carousel's ad creative.</span>
}

templ adLivestream() {
	<span class="">The LIVE content will be used as your ad creative.</span>
}

templ adDestinationCtaEdit(ad *responses.DetailAdResp) {
	<hr/>
	<h6 class="fw-bold">Destination Page</h6>
	<h6 class="fw-bold">
		Call to action 
		<span>
			@partials.PopoverInfo("Choose how you present your business in your ads.")
		</span>
	</h6>
	<span id="ad_call_to_action_edit">{ adEnums.CallToAction(ad.CallToAction).DisplayName() } </span>
}

func isShowingAdDetailsDefaultSetupCpn(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.AdGroup == nil {
		return false
	}
	if isShowingAdDetailsProductCpn(ad) == true {
		return false
	}
	return true
}

func isShowingAdDetailsLivestreamCpn(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.AdGroup == nil {
		return false
	}
	if ad.AdGroup.ShoppingAdsType == adgroupEnums.ShoppingAdsTypeLive {
		return true
	}
	return false
}

func isShowingAdDetailsProductCpn(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.AdGroup == nil {
		return false
	}
	if ad.AdGroup.ShoppingAdsType == adgroupEnums.ShoppingAdsTypeProduct {
		return true
	}
	return false
}
