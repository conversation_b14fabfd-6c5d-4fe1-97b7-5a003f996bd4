package modals

templ ModalPostGallery() {
	<div
		id="modal_post_gallery"
		class="modal fade modal-xl tiktok tiktok-custom"
		tabindex="-1"
		aria-labelledby="modal_post_gallery_label"
		aria-hidden="true"
		style="display: none;"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal_post_gallery_label">Spark Ad post</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body px-0 pb-0">
					<div
						class="modal_post_gallery_filter_container d-flex flex-row justify-content-between align-items-center flex-wrap gap-2 px-3 pb-3"
					>
						<div class="account_info">
							<label class="text-muted">
								From: <span class="modal_post_gallery_account_info">Navy clothing</span>
							</label>
						</div>
						<div class="filters d-flex flex-row gap-3 flex-wrap">
							<div class="d-flex flex-row text-nowrap gap-3 align-items-center">
								<label class="form-label mb-1 d-block text-muted">Sort by</label>
								<select class="form-select form-select">
									<option value="">Update time (Most to least recent)</option>
									<option value="">Update time (Least to most recent)</option>
								</select>
							</div>
							<div class="d-flex flex-row text-nowrap gap-2 align-items-center">
								<div class="btn-group post_gallery_filter" role="group">
									<button
										id="btnGroupVerticalDrop1"
										type="button"
										class="btn dropdown-toggle btn-white"
										data-bs-toggle="dropdown"
										aria-haspopup="true"
										aria-expanded="false"
									>
										Filter
									</button>
									<div class="dropdown-menu" aria-labelledby="btnGroupVerticalDrop1">
										<div class="p-2">
											<label class="text-muted">Post source</label>
											<div class="input-control-group flex-row">
												<input class="form-check-input" type="checkbox" id="post_souces_all"/>
												<label for="post_souces_all">All</label>
											</div>
											<div class="input-control-group flex-row">
												<input
													class="form-check-input"
													type="checkbox"
													id="post_souces_ads_manager"
												/>
												<label for="post_souces_ads_manager">Ads Manager</label>
											</div>
											<div class="input-control-group flex-row">
												<input
													class="form-check-input"
													type="checkbox"
													id="post_souces_tiktok_creator_marketplace"
												/>
												<label for="post_souces_tiktok_creator_marketplace">
													TikTok Creator
													Marketplace
												</label>
											</div>
											<div class="input-control-group flex-row">
												<input
													class="form-check-input"
													type="checkbox"
													id="post_souces_tiktok_content_suite"
												/>
												<label for="post_souces_tiktok_content_suite">Content Suite</label>
											</div>
										</div>
										<div class="p-2">
											<label class="text-muted">Post status</label>
											<div class="input-control-group flex-row">
												<input class="form-check-input" type="checkbox" id="post_status_all"/>
												<label for="post_status_all">All</label>
											</div>
											<div class="input-control-group flex-row">
												<input class="form-check-input" type="checkbox" id="post_status_available"/>
												<label for="post_status_available">Available</label>
											</div>
											<div class="input-control-group flex-row">
												<input
													class="form-check-input"
													type="checkbox"
													id="post_status_unavailable"
												/>
												<label for="post_status_unavailable">Unavailable</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="d-flex flex-row text-nowrap gap-2 align-items-center">
								<div class="input-group input-group-sm">
									<input type="text" class="form-control" placeholder="Search by text or post ID"/>
									<span class="input-group-text bg-black">
										<i
											class="ri ri-search-line fs-16 text-white"
										></i>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="modal_post_gallery_content">
						<div class="alert alert-success" role="alert">
							<div class="d-flex flex-row">
								<i class="ri ri-information-fill me-3 align-middle fs-16"></i>
								Editing the download or comment settings of a Spark Ad post on TikTok will also change the
								ad group settings in TikTok
								Ads Manager.
							</div>
						</div>
						<div class="modal_post_gallery_gallery_container">
							<div class="gallery"></div>
						</div>
					</div>
				</div>
				<div class="modal-footer d-flex justify-content-between align-items-center border-top pt-2">
					<div class="text-muted">
						Selected <span class="gallery-selected">NaN</span>/<span class="gallery-total">NaN</span>
					</div>
					<div>
						<button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
						<button type="button" class="btn btn-success">Continue</button>
					</div>
				</div>
			</div>
		</div>
	</div>
}
