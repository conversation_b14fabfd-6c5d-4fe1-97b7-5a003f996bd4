package modals

import "godsp/views/v2/tiktok/ads/partials"

templ ModalDynamicCtaTextOptions() {
	<div
		id="modal_dynamic_cta_text_options"
		class="modal fade tiktok"
		tabindex="-1"
		aria-labelledby="modal_dynamic_cta_text_options_label"
		aria-hidden="true"
		style="display: none;"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal_dynamic_cta_text_options_label">Edit text options</h5>
				</div>
				<div class="modal-body">
					<p class="text-muted">The system will use the text you select to achieve optimal results.</p>
					<div class="modal-dynamic-cta__text-options border rounded">
						<h6 class="fw-bold border p-3 m-0 bg-light">
							Text options 
							@partials.PopoverInfo("Choose how you present your business in your ads.")
						</h6>
						<div class="list-group border p-3" style="max-height:30vh; overflow-y:scroll"></div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
					<button type="button" class="btn btn-success ">Save Changes</button>
				</div>
			</div>
		</div>
	</div>
}
