package modals

templ ModalPostCode() {
	<div
		id="modal_authorize_tiktok_post"
		class="modal tiktok tiktok-custom fade modal-xl"
		tabindex="-1"
		aria-labelledby="modal_authorize_tiktok_post_label"
		aria-hidden="true"
		style="display: none;"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal_authorize_tiktok_post_label">Post code</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="modal_authorize_tiktok_container d-flex flex-row gap-3">
						<div class="modal_authorize_tiktok_left_content d-flex flex-column gap-3 mt-2">
							<div>
								<h6 class="fw-semibold">Enter TikTok post code and preview the post</h6>
								<textarea class="form-control" cols="20" rows="5" placeholder="Add code"></textarea>
							</div>
							<div>
								<button class="btn btn-success" type="button">Search</button>
							</div>
						</div>
						<div class=" modal_authorize_tiktok_right_content d-flex flex-column gap-3">
							<h6 class="fw-semibold">How do i get TikTok post code?</h6>
							<div
								id="post_authorization_carousel"
								class="carousel carousel-dark slide"
								data-bs-ride="carousel"
							>
								<div class="carousel-inner" role="listbox">
									@carouselItem("/static/images/tiktok/post_authorization_step1.gif", "1. Go to creator tools in the TikTok profile page.", true)
									@carouselItem("/static/images/tiktok/post_authorization_step2.gif", `2. Turn on "Ad settings".`, false)
									@carouselItem("/static/images/tiktok/post_authorization_step3.gif", "3. Select the TikTok post you want to create the ad with, and go to the ad settings page.", false)
									@carouselItem("/static/images/tiktok/post_authorization_step4.gif", `4. Turn on the "Ad authorization" and generate a video code.`, false)
								</div>
							</div>
							<div class="text-center mt-3">
								<button id="carousel-prev" class="btn px-2" style="font-size: 1.2rem;">&lt;</button>
								<span id="carousel-indicator">1 / 4</span>
								<button id="carousel-next" class="btn px-2" style="font-size: 1.2rem;">&gt;</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

func activeCarouselItem(active bool) string {
	if active {
		return "active"
	}
	return ""
}

templ carouselItem(src string, guideline string, active bool) {
	<div class={ "carousel-item" + activeCarouselItem(active) }>
		<div>
			<p>{ guideline }</p>
		</div>
		<img
			src={ src }
			alt=""
			class="d-block img-fluid mx-auto"
		/>
	</div>
}
