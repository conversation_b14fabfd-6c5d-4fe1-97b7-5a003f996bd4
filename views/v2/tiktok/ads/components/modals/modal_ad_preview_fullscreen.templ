package modals

templ ModalAdPreviewFullScreen() {
	<div
		id="ad-preview-fullscreen"
		class="modal fade"
		tabindex="-1"
		aria-labelledby="myModalLabel"
		aria-hidden="true"
		style="display: none;"
	>
		<div class="modal-dialog modal-sm">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="btn-close btn-rounded bg-light" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="ad-preview__placement-select mt-3">
						<select class="form-select">
							<option selected>In feed</option>
							<option>Search feed</option>
						</select>
					</div>
					<div class="ad-preview__result bg-light mt-3 h-75">
						<img class="w-100" src="/static/images/tiktok/preview-screen.png" alt=""/>
					</div>
				</div>
				<div class="modal-footer"></div>
			</div>
		</div>
	</div>
}
