package modals

templ ModalAddCreative() {
	<div
		id="modal_add_creative"
		class="modal fade modal-xl tiktok tiktok-custom"
		tabindex="-1"
		aria-labelledby="modal_add_creative_label"
		aria-hidden="true"
		style="display: none;"
	>
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modal_add_creative_label">Add creatives</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body px-0 pb-0">
					<div class="modal_add_creative_tabs">
						<ul class="nav nav-tabs nav-tabs-custom nav-success nav-justified mb-3 " role="tablist">
							<li class="nav-item" role="presentation">
								<a class="nav-link active" data-bs-toggle="tab" href="#tiktok_post" role="tab" aria-selected="true">
									TikTok posts
								</a>
							</li>
							<li class="nav-item" role="presentation">
								<a class="nav-link" data-bs-toggle="tab" href="#your_library" role="tab" aria-selected="false" tabindex="-1">
									Your library
								</a>
							</li>
						</ul>
					</div>
					<div class="tab-content text-muted">
						<div class="tab-pane active show" id="tiktok_post" role="tabpanel">
							@tiktokPostCpn()
						</div>
						<div class="tab-pane" id="your_library" role="tabpanel">
							<div class="d-flex">
								@yourLibraryCpn()
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer d-flex justify-content-end align-items-center border-top pt-2">
					<div>
						<button type="button" class="btn btn-success">Confirm</button>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ tiktokPostCpn() {
	<div class="d-flex flex-column p-3 gap-3">
		<div class="alert border alert-light bg-white alert-dismissible alert-solid alert-label-icon fade show m-0 p-0 alert-tiktok-post-cpn" role="alert">
			<div class="d-flex flex-row gap-2"><img src="/static/images/tiktok/ads/spark-ads.png" class="my-auto w-100"/><div class="d-flex flex-column gap-2 justify-content-start py-3"><h6 class="fw-bold m-0">Spark Ads vs non-Spark Ads: Performance lift</h6><small>Running Spark Ads with the same post as non-Spark Ads will bring a lower CPA versus non-Spark Ads, based on historical data.</small><div class="w-auto"><button type="button" class="btn btn-light"><i class="bx bx-trending-down fs-20"></i> CPA</button></div></div></div>
			<button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close"></button>
		</div>
		<div class="d-flex flex-column">
			<div class="d-flex flex-row justify-content-between align-items-center">
				<input class="form-control w-25" name="search_term" placeholder="Search by post caption or post ID"/>
				<div class="d-flex flex-row gap-3">
					<a class="d-inline-flex align-items-center text-success text-decoration-none text-nowrap m-0 p-0">
						<i class="ri-links-line fs-20 me-1"></i> Link TikTok account
					</a>
					<button class="btn btn-dark" type="button">Add post</button>
				</div>
			</div>
		</div>
		<div class="add-creative-filter d-flex flex-row gap-3">
			<select class="form-select py-0">
				<option value="most_to_least_recent">Post date (Most to least recent)</option>
				<option value="least_to_most_recent">
					Post date (Least to most recent)
				</option>
			</select>
			<select class="form-select py-0"></select>
			<select class="form-select py-0">
				<option value="all">Format: All</option>
				<option value="videos">
					Videos
				</option>
				<option value="images">
					Images
				</option>
			</select>
			<div class="btn-group" role="group">
				<button id="add_creative_filter" type="button" class="btn btn-light d-inline-flex align-items-center" data-bs-toggle="dropdown">
					<i class="ri-add-line fs-20 me-1"></i> Filter
				</button>
				<div class="dropdown-menu" aria-labelledby="add_creative_filter">
					<a class="dropdown-item" href="#">Status</a>
					<a class="dropdown-item" href="#">Source</a>
				</div>
			</div>
		</div>
		<div class="add-creative-tiktok-post-gallery d-flex flex-wrap gap-3"></div>
	</div>
}

templ yourLibraryCpn() {
}
