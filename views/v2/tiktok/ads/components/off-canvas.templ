package components

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/tiktok/ad/transport/responses"
	"godsp/views/v2/tiktok/ads/components/modals"
)

// import "godsp/views/v2/tiktok/ads/components"
templ AdOffCanvas() {
	<div
		class="offcanvas offcanvas-end offcanvas-medium-content casa-offcanvas"
		data-bs-backdrop="static"
		data-bs-scroll="false"
		tabindex="-1"
		id="adOffCanvas"
		aria-labelledby="adOffCanvas"
		style="width:95%"
	>
		<div class="offcanvas-body p-0 bg-light">
			<div class="d-flex flex-row" style="position: sticky; top: 0; height: 100vh;">
				<div class="bg-light" style="width:60px">
					<div class="button-groups p-2">
						<!-- ghost Buttons -->
						<button type="button" data-bs-dismiss="offcanvas" aria-label="Close" class="btn btn-soft-dark waves-effect waves-light"><i class="ri-close-fill"></i></button><!-- ghost Buttons -->
					</div>
				</div>
				// <div class="bg-white w-100" style="border-right: 1px solid #ccc"></div>
			</div>
			<div class="content-offcanvas flex-1">
				<div class="sidebar-offcanvas-blk">
					<div class="casa-blk">
						<div class="search-bar app-search p-2">
							<div class="position-relative">
								<input type="text" class="form-control" placeholder="Search by Name or ID" autocomplete="off" id="searchCampAdsetAdByNameId" value=""/>
								<span class="mdi mdi-magnify search-widget-icon"></span>
								<span class="mdi mdi-close-circle search-widget-icon search-widget-icon-close d-none" id="search-close-options"></span>
							</div>
						</div>
						<ul class="nav flex-column casa-items-list" id="menu-camp-adset-ad-left-side-bar"></ul>
					</div>
				</div>
				<div class="data-blk" id="tiktok-ad">
					@AdHeaderOffcanvas(nil)
					<!-- // Offcanvas content -->
					<form class="off-canvas-content__ad_form tiktok-custom" id="ad-form">
						// @CampaignAndAdGroupHiddenInput(nil)
						<div class="container d-flex flex-1">
							<div class="row flex-1 position-relative">
								<!-- Main Container -->
								<div class="col-8 d-flex flex-column position-relative ">
									<div class=" container-input-control d-flex flex-column mt-3 flex-1 ">
										@AdNameCpn(nil)
										@IdentityCpn(nil)
										@ProductDetailsCpn(nil)
										@AdDetailsCpn(nil)
										@TrackingCpn(nil)
										@PolicyCpn(nil)
									</div>
									<!-- // Footer Container -->
									<div class="form-footer p-3 bg-white border-top" id="">
										<div class="d-flex justify-content-between">
											<div class="d-flex flex-row gap-2 align-items-center">
												<div class="" id="alert-auto-save">
													<span class="d-none save-success text-success fs-16">
														Auto save success
														<i class="ri-check-double-line"></i>
													</span>
													<span class="d-none save-fail text-danger">
														Auto save fail
														<i class="ri-close-circle-line"></i>
													</span>
												</div>
											</div>
											<div>
												<button id="btn-save-form-off-canvas" type="button" class="btn btn-success waves-effect waves-light">
													Save
												</button>
											</div>
										</div>
									</div>
								</div>
								<!-- Right Container -->
								<div class="col-4 d-flex flex-column position-sticky mt-3 top-0">
									<div class="position-sticky" style="top:75px">
										@AdPreview(nil, nil)
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
	@initModal()
	@scriptAds()
}

templ initModal() {
	@modals.ModalPostCode()
	@modals.ModalPostGallery()
	@modals.ModalDynamicCtaTextOptions()
	@modals.ModalAdPreviewFullScreen()
	@modals.ModalAddCreative()
}

func ptr(data string) *string {
	return &data
}

templ scriptAds() {
	<script type="module" src={ templates.AssetURL("/static/js/tiktok/ads/off-canvas/index.js") } defer></script>
}

templ CampaignAndAdGroupHiddenInput(ad *responses.DetailAdResp) {
	if ad != nil {
		if ad.Campaign != nil {
			<input type="hidden" id="outcomeObjective" value={ ad.Campaign.ObjectiveType }/>
		}
		if ad.AdGroup != nil {
			<input type="hidden" id="shopAdsType" value={ ad.AdGroup.ShoppingAdsType }/>
			<input type="hidden" id="optimizationLocation" value={ ad.AdGroup.PromotionType }/>
		}
	} else {
		<input type="hidden" id="outcomeObjective" value=""/>
		<input type="hidden" id="shopAdsType" value=""/>
		<input type="hidden" id="optimizationLocation" value=""/>
	}
}
