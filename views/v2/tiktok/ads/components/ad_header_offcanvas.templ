package components

import "godsp/modules/tiktok/ad/transport/responses"

templ AdHeaderOffcanvas(ad *responses.DetailAdResp) {
	<div class="breadcrumbs-offcanvas-blk bg-white position-sticky top-0" style="z-index: 9">
		<div class="breadcrumb-container border-0 justify-content-between align-items-center ">
			<div class="d-flex align-items-center mt-2 mb-2 gap-2 justify-content-between">
				<h5 class="mb-0">Ad View</h5>
				<div class="status-indicator text-muted fs-12">
					AD ID: 
					<span id="ad_id">
						{ GetAdIDDisplay(ad) }
					</span>
				</div>
			</div>
		</div>
	</div>
}

func GetAdIDDisplay(ad *responses.DetailAdResp) string {

	if ad != nil {
		return ad.AdID
	}
	return "N/A"
}
