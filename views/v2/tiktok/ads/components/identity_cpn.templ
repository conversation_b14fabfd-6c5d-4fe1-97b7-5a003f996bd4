package components

import (
	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
	"godsp/modules/tiktok/ad/transport/responses"
	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
	campaignEnums "godsp/modules/tiktok/campaign/common/enums"
	identityEnums "godsp/modules/tiktok/identity/common/enums"
	"godsp/views/v2/tiktok/ads/partials"
)

templ IdentityCpn(ad *responses.DetailAdResp) {
	@renderIdentityCpn(ad)
}

templ renderIdentityCpn(ad *responses.DetailAdResp) {
	if shouldShowIdentity(ad) {
		<div class="ad_component_identity card">
			<div class="card-body p-4">
				<h5 class="d-flex gap-2 align-items-center">
					Identity
					@partials.PopoverInfo("Choose how you present your business in your ads.")
				</h5>
				if ad.Identity.IdentityType != string(identityEnums.IDENTITY_CUSTOMIZED_USER) {
					<div class="d-flex flex-column gap-3">
						<div class="d-flex flex-row align-items-center">
							<span class="flex-grow-1">Select a TikTok account</span>
							<button class="btn btn-icon btn-ghost" type="button">
								<i class="bx bx-refresh flex-auto fs-14"></i>
							</button>
						</div>
						@IdentityAccordion()
						@partials.AlertInfo(
							"Custom identity is no longer supported. Using a TikTok account maximizes ad engagement and helps people find and engage with your brand.",
						)
					</div>
				}
				if ad.Identity.IdentityType == string(identityEnums.IDENTITY_CUSTOMIZED_USER) {
					<div class="d-flex flex-column gap-3">
						<div class="form-check form-switch form-switch-md ms-3" dir="ltr">
							<input type="checkbox" class="form-check-input" id="use_tiktok_account_to_sparkads_switch" disabled/>
							<label class="form-check-label d-flex flex-row gap-2 align-items-center" for="use_tiktok_account_to_sparkads_switch">
								Use TikTok account to deliver Spark Ads
								@partials.PopoverInfo("Choose how you present your business in your ads.")
							</label>
						</div>
						<div class="align-items-center d-flex gap-2">
							<span class="fw-bold">Set custom identity</span>
							@partials.PopoverInfo("Choose how you present your business in your ads.")
						</div>
						<div>
							<select id="custom_identity_select" class="w-100"></select>
						</div>
						<div>
							<button type="button" class="btn btn-ghost-dark waves-effect waves-light">
								<span class="d-flex align-items-center">
									<i class="ri ri-add-line flex-shrink-0"></i>
									<span class="flex-grow-1 ms-2">Create new custom identity</span>
								</span>
							</button>
						</div>
					</div>
				}
			</div>
		</div>
	}
}

templ IdentityAccordion() {
	<div class="accordion" id="default-accordion-example">
		<div class="accordion-item">
			<h2 class="accordion-header" id="headingOne">
				<button
					class="accordion-button"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#collapseOne"
					aria-expanded="true"
					aria-controls="collapseOne"
				>
					<div class="d-flex flex-row gap-3 align-items-center">
						<i class="ri ri-store-2-line fs-20"></i> Authorized accounts
						@partials.PopoverInfo("Choose how you present your business in your ads.")
					</div>
				</button>
			</h2>
			<div
				id="collapseOne"
				class="accordion-collapse collapse show"
				aria-labelledby="headingOne"
				data-bs-parent="#default-accordion-example"
			>
				<div class="accordion-body">
					<div class="d-flex flex-row">
						<div class="d-flex flex-grow-1 align-items-center">
							<i class="ri ri-account-circle-line fs-20"></i>
							<span class="ms-2">No account links</span>
							@partials.PopoverInfo("Choose how you present your business in your ads.")
						</div>
						<div class="flex-auto btn-group" role="group">
							<button
								id="btnGroupDrop1"
								type="button"
								class="btn btn-light dropdown-toggle"
								data-bs-toggle="dropdown"
								aria-expanded="false"
							>
								Connect account
							</button>
							<ul class="dropdown-menu" aria-labelledby="btnGroupDrop1">
								<li><a class="dropdown-item" href="#">From Tiktok</a></li>
								<li><a class="dropdown-item" href="#">From Business Center</a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="accordion-item">
			<h2 class="accordion-header" id="headingTwo">
				<button
					class="accordion-button collapsed"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#collapseTwo"
					aria-expanded="false"
					aria-controls="collapseTwo"
				>
					<div class="d-flex flex-row gap-3 align-items-center">
						<i class="ri ri-user-star-line fs-20"></i> Post authorized by account
						@partials.PopoverInfo("Choose how you present your business in your ads.")
					</div>
				</button>
			</h2>
			<div
				id="collapseTwo"
				class="accordion-collapse collapse"
				aria-labelledby="headingTwo"
				data-bs-parent="#default-accordion-example"
			>
				<div class="accordion-body">
					<div class="d-flex flex-column gap-2">
						@PostAuthorizedSelect()
						<div>
							<button
								type="button"
								class="w-auto btn btn-ghost-info waves-effect waves-light"
								data-bs-toggle="modal"
								data-bs-target="#modal_authorize_tiktok_post"
							>
								+
								Authorize
								TikTok post
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ PostAuthorizedSelect() {
	<select class="form-select" name="post_authorized_select" id="identity_post-authorized-select"></select>
}

func isShowingIdentityCpn(ad *responses.DetailAdResp) string {
	if ad == nil || ad.Campaign == nil {
		return "d-none"
	}
	if ad.Campaign.ObjectiveType == campaignEnums.REACH || ad.Campaign.ObjectiveType == campaignEnums.VIDEO_VIEWS {
		return ``
	}
	return "d-none"
}

type IdentityRule struct {
	Visible              bool
	ShopAdsType          map[v13.ShoppingAdsType]IdentityRule
	OptimizationLocation map[string]IdentityRule
}

var IDENTITY_RULE = map[string]IdentityRule{
	campaignEnums.PRODUCT_SALES: {
		ShopAdsType: map[v13.ShoppingAdsType]IdentityRule{
			adgroupEnums.ShoppingAdsTypeVSA:     {Visible: false},
			adgroupEnums.ShoppingAdsTypeLive:    {Visible: false},
			adgroupEnums.ShoppingAdsTypeProduct: {Visible: false},
		},
	},
	campaignEnums.VIDEO_VIEWS: {Visible: true},
	campaignEnums.TRAFFIC: {
		OptimizationLocation: map[string]IdentityRule{
			adgroupEnums.PROMOTION_TYPE_WEBSITE: {Visible: false},
			adgroupEnums.PROMOTION_TYPE_APP:     {Visible: false},
		},
	},
	campaignEnums.REACH: {Visible: true},
}

func shouldShowIdentity(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.Campaign == nil || ad.AdGroup == nil {
		return false
	}

	rule, ok := IDENTITY_RULE[ad.Campaign.ObjectiveType]
	if !ok {
		return false
	}

	if ad.AdGroup.ShoppingAdsType != "" && rule.ShopAdsType != nil {
		if subRule, ok := rule.ShopAdsType[ad.AdGroup.ShoppingAdsType]; ok {
			return subRule.Visible
		}
	}

	if ad.AdGroup.PromotionType != "" && rule.OptimizationLocation != nil {
		if subRule, ok := rule.OptimizationLocation[ad.AdGroup.PromotionType]; ok {
			return subRule.Visible
		}
	}

	return rule.Visible
}
