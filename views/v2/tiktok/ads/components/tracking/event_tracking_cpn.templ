package tracking

import "godsp/views/v2/tiktok/ads/partials"

templ EventTrackingCpn() {
	@eventTrackingCollapsed()
	@eventTrackingExpanded()
}

templ eventTrackingExpanded() {
	<div class="tracking-event-tracking collapse mt-2">
		<h6 class="fw-bold">Tiktok events tracking</h6>
		<div class="w-100 d-flex flex-column gap-3 bg-light p-3 bg-opacity-50 mt-2">
			<div>
				<label>Website events</label>
				<select class="form-select w-75">
					<option>Select optimization event</option>
				</select>
			</div>
			<div>
				<label>App events</label>
				<div>
					<label class="text-muted">
						No app available 
						@partials.PopoverInfo("Choose how you present your business in your ads.")
					</label>
				</div>
			</div>
			<div class="d-flex flex-column gap-2">
				<label>
					Offline events 
					@partials.PopoverInfo("Choose how you present your business in your ads.")
				</label>
				<div class="p-3 w-75 offline-event">
					<p>1 auto-tracking offline event set (not editable)</p>
					<div>
						<div class="p-2 d-inline-block rounded offline-event-tag">
							Maybi pix
						</div>
					</div>
				</div>
				<select class="form-select w-75">
					<option>Select offline event sets</option>
				</select>
			</div>
		</div>
	</div>
}

templ eventTrackingCollapsed() {
	<div class="d-flex flex-column gap-1 position-relative tiktok-events-tracking-blk">
		<button class="hover-edit-btn btn" type="button" id="editEventTrackingBtn">
			<i class="ri ri-edit-line fs-15"></i>
		</button>
		<h6 class="fw-bold">TikTok events tracking</h6>
		<label class="fw-light">
			<span class="text-muted">Website events: </span><span
	class="events_trackng_website_events"
>-</span>
		</label>
		<label class="fw-light">
			<span class="text-muted">App events: </span><span
	class="events_trackng_app_events"
>-</span>
		</label>
		<label class="fw-light">
			<span class="text-muted">Offline events: </span><span
	class="events_trackng_offline_events"
>Maybi pix</span>
		</label>
	</div>
}
