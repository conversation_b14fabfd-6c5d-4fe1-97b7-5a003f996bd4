package components 

import (
	"godsp/modules/tiktok/ad/transport/responses"
	"godsp/views/v2/tiktok/ads/components/ad_preview"
)

templ AdPreview(ad *responses.DetailAdResp, preview *responses.AdPreviewResp) {
	<div class="ad-component__ad-preview card sticky-wrapper">
		<div class="card-body p-4">
			<div class="ad-preview__header d-flex flex-row">
				<div class="flex-grow-1">
					@ad_preview.AdPreviewPlatformsCpn()
				</div>
				<hr/>
				<div class="ad-preview__expand flex-grow-0">
					<button
						class="btn btn-icon btn-light"
						type="button"
						data-bs-toggle="modal"
						data-bs-target="#ad-preview-fullscreen"
					>
						<i class="ri-fullscreen-fill"></i>
					</button>
				</div>
			</div>
			@ad_preview.AdPreviewPlacementSelectCpn(ad)
			<div class="ad-preview__container mt-3">
				<div class="ad-preview__iframe-wrapper position-relative">
					if isValidPreviewResp(preview) {
						<div id="previewSpinner" class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75">
							<div class="spinner-border text-success" role="status">
								<span class="visually-hidden">Loading...</span>
							</div>
						</div>
						<iframe
							src={ preview.PreviewLink }
							class="ad-preview__iframe"
							allowfullscreen
							loading
							onload="document.getElementById('previewSpinner').style.display = 'none';"
						></iframe>
					} else {
						<!-- warning Alert -->
						<div class="alert alert-danger" role="alert">
							Không thể hiển thị preview cho quảng cáo này. Vui lòng kiểm tra dữ liệu đầu vào hoặc thử lại sau.
						</div>
					}
				</div>
			</div>
		</div>
	</div>
}

func isValidPreviewResp(preview *responses.AdPreviewResp) bool {
	if preview == nil {
		return false
	}
	if preview.PreviewLink == "" {
		return false
	}
	return true
}
