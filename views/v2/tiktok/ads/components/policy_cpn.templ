package components

import (
	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
	"godsp/modules/tiktok/ad/transport/responses"
	adgroupEnums "godsp/modules/tiktok/adgroup/common/enums"
	campaignEnums "godsp/modules/tiktok/campaign/common/enums"
)

// templ gọi hàm renderer
templ PolicyCpn(ad *responses.DetailAdResp) {
	@renderPolicyCpn(shouldShowPolicy(ad))
}

templ renderPolicyCpn(shouldShow bool) {
	if shouldShow {
		<div class="ad-component__policy card py-3 px-4">
			<div class="card-body">
				By clicking "Publish", you agree to TikTok's Online Data Terms
			</div>
		</div>
	}
}

type PolicyRule struct {
	Visible              bool
	ShopAdsType          map[v13.ShoppingAdsType]PolicyRule
	OptimizationLocation map[string]PolicyRule
}

var POLICY_RULE = map[string]PolicyRule{
	campaignEnums.PRODUCT_SALES: {
		ShopAdsType: map[v13.ShoppingAdsType]PolicyRule{
			adgroupEnums.ShoppingAdsTypeVSA:     {Visible: false},
			adgroupEnums.ShoppingAdsTypeLive:    {Visible: false},
			adgroupEnums.ShoppingAdsTypeProduct: {Visible: false},
		},
	},
	campaignEnums.VIDEO_VIEWS: {Visible: true},
	campaignEnums.TRAFFIC: {
		OptimizationLocation: map[string]PolicyRule{
			adgroupEnums.PROMOTION_TYPE_WEBSITE: {Visible: true},
			adgroupEnums.PROMOTION_TYPE_APP:     {Visible: true},
		},
	},
	campaignEnums.REACH: {Visible: true},
}

func shouldShowPolicy(ad *responses.DetailAdResp) bool {
	if ad == nil || ad.Campaign == nil || ad.AdGroup == nil {
		return false
	}

	rule, ok := POLICY_RULE[ad.Campaign.ObjectiveType]
	if !ok {
		return false
	}

	if ad.AdGroup.ShoppingAdsType != "" && rule.ShopAdsType != nil {
		if subRule, ok := rule.ShopAdsType[ad.AdGroup.ShoppingAdsType]; ok {
			return subRule.Visible
		}
	}

	if ad.AdGroup.PromotionType != "" && rule.OptimizationLocation != nil {
		if subRule, ok := rule.OptimizationLocation[ad.AdGroup.PromotionType]; ok {
			return subRule.Visible
		}
	}

	return rule.Visible
}
