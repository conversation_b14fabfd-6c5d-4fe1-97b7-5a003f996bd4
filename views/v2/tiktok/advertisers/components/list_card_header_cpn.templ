package components

import "godsp/modules/tiktok/advertiser/transport/responses"

templ ListCardHeaderCpn(data *responses.ListAdvertiserResp) {
	<div class="card-header border-0">
		<div class="row g-4 align-items-center">
			<div class="col-sm-3">
				<div class="search-box">
					<input
						type="text"
						class="form-control search"
						id="searchUser"
						placeholder="Search for..."
					/>
					<i class="ri-search-line search-icon"></i>
				</div>
			</div>
			<div class="col-sm-9 ms-auto d-flex justify-content-end gap-3">
				<button
					class="btn btn-danger"
					id="remove-actions"
					data-url=""
					data-bs-toggle="modal"
					data-bs-target="#delete_modal"
					style="display: none;"
				>
					<i class="ri-delete-bin-2-line"></i>
				</button>
				<div class="col-3 d-flex align-items-center gap-2" id="status_filter">
					<span class="col-3 text-muted" data-key="t-status">Status<span>:</span></span>
					<div class="col-9">
						@selectStatusOption(data.Statuses)
					</div>
				</div>
				<button type="button" class="btn btn-secondary add-btn" data-bs-toggle="modal" data-bs-target="#create_user_modal" id="create-btn">
					<i class="mdi mdi-plus"></i>
					<span data-key="t-add-user">Add User</span>
				</button>
			</div>
		</div>
	</div>
}

templ selectStatusOption(statuses map[string]string) {
	<select class="form-control mb-0 search_by_status" id="status_id" data-choices data-choices-search-false>
		<option value="" data-key="t-all">All</option>
		for key,name := range statuses {
			<option value={ key }>{ name }</option>
		}
	</select>
}
