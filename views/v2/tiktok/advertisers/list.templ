package advertisers

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/tiktok/advertiser/transport/responses"
	"godsp/views/v2/layouts"
	"godsp/views/v2/layouts/masters"
	"godsp/views/v2/tiktok/advertisers/components"
)

templ ListAdvertisers(data *responses.ListAdvertiserResp) {
	{{
		dataLayoutMaster := masters.LayoutMasterData{
			AuthPermission: data.AuthPermission,
			UserInfo:       data.UserInfo,
		}
	}}
	@layouts.Master(dataLayoutMaster, nil, scriptAdvertiser()) {
		@components.ListBreadcrumdCpn()
		if data != nil && data.FlashMsg != "" {
			@components.FlashMsgCpn(data.FlashMsg)
		}
		<div class="row">
			<div class="col-xxl-9">
				<div class="col-lg-12">
					<div class="card">
						@components.ListCardHeaderCpn(data)
						@components.ListCardBodyCpn()
					</div>
				</div>
			</div>
			<div class="col-xxl-3">
				@components.ListRightCpn()
			</div>
		</div>
		@components.HtmlBarCop()
	}
}

templ scriptAdvertiser() {
	<script type="text/javascript" src={ templates.AssetURL("/static/assets/libs/datatables/datatables.min.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/tiktok/advertisers/list_table_advertiser.js") } defer></script>
}
