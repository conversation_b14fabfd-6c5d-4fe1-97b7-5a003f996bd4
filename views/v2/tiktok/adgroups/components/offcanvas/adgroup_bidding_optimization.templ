package offcanvas

import "godsp/views/v2/tiktok/adgroups/components/offcanvas/bidding_optimization"

templ AdGroupBiddingOptimization() {
	<div class="card">
		<div class="card-body p-4">
			<div class="d-flex gap-3 flex-column">
				<!-- Optimization Goal -->
				@bidding_optimization.OptimizationGoal()
				<!-- Frequency Cap  -->
				<div id="optionByOptimizationGoal" style="display: none;"></div>
				<!-- Target ROAS -->
				<div id="roasGoal">
					<lable class="fw-bold">
						<span id="bidControlLabel">Target ROAS</span>
						<button
							class="btn p-0 "
							type="button"
							role="button"
							data-bs-toggle="popover"
							data-bs-trigger="focus"
							data-bs-html="true"
							data-bs-content="Target ROAS is required."
						>
							<i class="ri-information-line"></i>
						</button>
					</lable>
					<div class="d-flex flex-column gap-3">
						<span class="mb-0 text-muted font-italic" for="roasBid">Aims to hit your target return on ad spend (ROAS) of while maximizing your gross revenue. Your target ROAS bid should be consistent with your attribution settings. View details</span>
						<div class="col-8 d-flex gap-2 align-items-center">
							<input
								type="number"
								inputmode="decimal"
								lang="en"
								class="form-control number-format"
								id="roasBid"
								value=""
								placeholder="Enter a value ..."
							/>
						</div>
					</div>
				</div>
				<!-- Bid Control -->
				<div class="mt-2" id="bidControl">
					<label class="fw-bold">
						<span id="bidControlLabel">Bid control (optional)</span>
						<button
							class="btn p-0 "
							type="button"
							role="button"
							data-bs-toggle="popover"
							data-bs-trigger="focus"
							data-bs-html="true"
							data-bs-content="Keep your average cost at or below the amount you specify, regardless of budget."
						>
							<i class="ri-information-line"></i>
						</button>
					</label>
					<div class="d-flex flex-column gap-3">
						<div class="col-8 d-flex gap-2 align-items-center">
							<input
								type="number"
								class="form-control number-format"
								id="bidAmount"
								value="12000"
								placeholder="Enter bid amount ..."
							/>
							<span class="mb-0 text-nowrap" for="bidAmount">VND/Thousand Impressions</span>
						</div>
						<p class="text-muted font-italic">
							We suggest a bid of 15,212VND to ensure your cost stays at or below this amount. For
							maximum
							delivery bidding, remove this amount so bidding can be done at any price.
						</p>
					</div>
				</div>
				<!-- Advanced Setting  -->
				@bidding_optimization.BiddingOptimizationAdvancedSettingCpn()
			</div>
		</div>
	</div>
	<template id="tiktokAdgroupFrequencyCapHtml">
		@bidding_optimization.BiddingOptimizationFrequencyCap()
	</template>
}
