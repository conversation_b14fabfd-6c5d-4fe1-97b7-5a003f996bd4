package offcanvas

import "godsp/views/v2/tiktok/adgroups/components/offcanvas/optimization_location"

templ AdGroupOptimizationLocation() {
	<div class="card" id="optimizationLocationCard">
		<div class="card-body p-4">
			<h6>
				<div for="">
					<span class="fw-bold fs-16" id="optimizationLocationTitle">Optimization location</span>
				</div>
				<p class="text-muted" id="optimizationLocationDes">Select where you'd like to direct traffic</p>
			</h6>
			<!-- Option optimization location by Outcome Objective -->
			<div id="optionOptimizationLocationByOutcomeObjective"></div>
		</div>
	</div>
	<template id="optionOptimizationLocationHtmlByTRAFFIC">
		@optimization_location.WithTRAFFIC()
	</template>
	<template id="optionOptimizationLocationHtmlByLEAD_GENERATION">
		@optimization_location.WithLEAD_GENERATION()
	</template>
}
