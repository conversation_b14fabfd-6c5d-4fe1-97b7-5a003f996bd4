package bidding_optimization

templ BiddingOptimizationFrequencyCap() {
	<div class="">
		<div>
			<label>
				<span class="fw-bold">Frequency cap</span>
				<button
					class="btn p-0 "
					type="button"
					role="button"
					data-bs-toggle="popover"
					data-bs-trigger="focus"
					data-bs-html="true"
					data-bs-content="Select an option to control how often people see your ad based on your objectives. If you choose to set a custom frequency cap, you can manually balance the tradeoffs between how often your ad is seen and how many people see it"
				>
					<i class="ri-information-line"></i>
				</button>
			</label>
		</div>
		<div class="d-flex flex-column gap-2">
			<div class="mt-2">
				<input type="radio" name="frequency_cap" id="frequencyCap3Times7Days" value="3_times_7_days" class="form-check-input mt-0" checked/>
				<label for="frequencyCap3Times7Days" class="mb-0 ms-1">Show ads no more than 3 times every 7 days</label>
			</div>
			<div class="mt-2">
				<input type="radio" name="frequency_cap" id="frequencyCap1Time1Day" value="1_time_1_day" class="form-check-input mt-0"/>
				<label for="frequencyCap1Time1Day" class="mb-0 ms-1">Show ads no more than once a day</label>
			</div>
			<div class="mt-2">
				<input type="radio" name="frequency_cap" id="frequencyCapCustom" value="custom" class="form-check-input mt-0"/>
				<label for="frequencyCapCustom" class="mb-0 ms-1">Custom frequency cap</label>
				<div id="frequencyCapInputControl" class="col-12  p-3 mt-3" style="background-color: #dadada33;border-radius: 8px; display: none;">
					<lable>Frequency cap</lable>
					<div class="d-flex gap-2 align-items-center mt-2">
						<input
							name="frequency_cap_custom_times"
							type="number"
							class="form-control"
							value="5"
							id="frequencyCapCustomTimes"
							placeholder="0"
							min="1"
						/>
						<div style="flex: none">impressions / </div>
						<input
							name="frequency_cap_custom_days"
							type="number"
							class="form-control"
							value="20"
							id="frequencyCapCustomDays"
							placeholder="0"
							min="1"
						/>
						<div>days</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script>
   document.addEventListener("DOMContentLoaded", function () {
      const $customDayOption = $("#frequencyCapCustom");
      $customDayOption.on('change', function () {
         if ($(this).is(':checked')) {
            $("#frequencyCapInputControl").show();
         } else {
            $("#frequencyCapInputControl").hide();
         }
      })
   });
</script>
}
