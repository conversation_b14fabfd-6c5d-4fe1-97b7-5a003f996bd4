package bidding_optimization

templ BiddingOptimizationAdvancedSettingCpn() {
	<div class="accordion-item trd-custom border rounded border-opacity-50">
		<div class="accordion-header accordion-button justify-content-between ps-0 pe-2">
			<div class="d-flex gap-2 align-items-center py-2 flex-1">
				<span
					class="avatar-title bg-soft-light fs-5 px-2 py-1 border-end btn-toggle collapsed"
					data-bs-toggle="collapse"
					data-bs-target="#biddingOptimizationAdvancedSetting"
					aria-expanded="false"
					style="flex: 0 1 0%;"
				>
					<i
						class="ri-play-fill text-black-50"
					></i>
				</span>
				<div class="fs-14 fw-bold">
					<p class="mb-0">Advanced Setting </p>
				</div>
			</div>
		</div>
		<div id="biddingOptimizationAdvancedSetting" class="accordion-collapse multi-collapse collapse show">
			<div class="accordion-body p-3 d-flex flex-column gap-3">
				<div class="input-group flex-column">
					<div>
						<label class="form-label fw-bold">Billing event</label>
						<button
							class="btn p-0 "
							type="button"
							role="button"
							data-bs-toggle="popover"
							data-bs-trigger="focus"
							data-bs-html="true"
							data-bs-content="Determines when you pay for your ad"
						>
							<i class="ri-information-line"></i>
						</button>
					</div>
					<div class="flex-1">
						<input
							type="text"
							class="form-control border-0 p-0 fs-14 text-success"
							id="billingEvent"
							value="Impression (CPM)"
							name="billing_event"
							placeholder="Impressions"
							readonly
						/>
					</div>
				</div>
				<div class="input-group flex-column">
					<div class="">
						<label class="form-label fw-bold">Delivery type</label>
						<button
							class="btn p-0 "
							type="button"
							role="button"
							data-bs-toggle="popover"
							data-bs-trigger="focus"
							data-bs-html="true"
							data-bs-content="Your choice of ad delivery type determines the speed at which your daily budget is used."
						>
							<i class="ri-information-line"></i>
						</button>
					</div>
					<div class="flex-1">
						<input
							type="text"
							class="form-control border-0 p-0 fs-14 text-success"
							id="deliveryType"
							name="billing_event"
							value="Standard"
							placeholder="Impressions"
							readonly
						/>
					</div>
					<p class="fs-12 mt-2 text-muted">
						Your budget will be used as evenly as possible depending on market demand and peaktime rates.
						This
						delivery type is suitable
						for advertisers who prefer steady spending.
					</p>
				</div>
			</div>
		</div>
	</div>
}
