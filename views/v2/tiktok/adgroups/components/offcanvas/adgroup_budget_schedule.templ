package offcanvas

import "godsp/views/v2/tiktok/adgroups/components/offcanvas/budget_and_schedule"

templ AdGroupBudgetSchedule() {
	<div class="card">
		<div class="card-body p-4">
			<h6>
				<label>
					<span class="fw-bold fs-16 ">Budget & schedule</span>
				</label>
			</h6>
			<div class="d-flex flex-column gap-3">
				<div class="mt-3" id="adgroupBaseBudgetTypeContainer">
					<div class="control-budget-adset">
						<div>
							<label for="adsetBaseBudgetType" class="required-field fw-bold">Budget</label>
						</div>
						<div id="adgroupBaseBudgetSetup"></div>
						// <div class="input-group">
						// 	<select id="adsetBaseBudgetType" class="form-select" aria-label="Budget" style="width: 180px; flex: none;">
						// 		<option value="daily" selected>Daily budget</option>
						// 		<option value="life">Lifetime budget</option>
						// 	</select>
						// 	<input
						// 		id="adsetBaseBudgetAmount"
						// 		type="text"
						// 		value="500000"
						// 		data-comment="affect by budget-type"
						// 		name="amount_budget"
						// 		placeholder="Budget"
						// 		aria-label="Budget"
						// 		class="form-control number-format"
						// 	/>
						// 	<span class="input-group-text">VND</span>
						// </div>
					</div>
				</div>
				<div class="">
					<div>
						<label class="fw-bold">Schedule</label>
						<button
							class="btn p-0"
							role="button"
							data-bs-toggle="popover"
							data-bs-trigger="focus"
							data-bs-html="true"
							data-bs-content="<b>Schedule</b><br />You can choose to run your ads continuously starting today or only during a specific date range.<br/><b>When using a lifetime campaign budget</b><br/>Ad set schedules affect the distribution of a lifetime campaign budget. Days with more opportunities receive more budget, so the amount spent per day will fluctuate.<br/><a href='https://www.facebook.com/business/help/1381935425400769' target='_blank'>Learn more</a>"
						>
							<i class="ri-information-line"></i>
						</button>
						<div class="form-check form-check-secondary mb-3 ms-2">
							<input class="form-check-input" type="checkbox" id="set-an-end-date"/>
							<label class="form-check-label pt-1" for="set-an-end-date">Set an end date</label>
						</div>
					</div>
					<div class="row mt-3">
						<div class="col-md-6">
							<label class="form-label mb-1">Start date</label>
							<input type="text" name="start_time" id="start-time-field" class="form-control" data-datetime-format/>
							<!-- <input type="hidden" name="start_time" class="form-control" /> -->
						</div>
						<div class="col-md-6">
							<label class="form-label mb-1">End date</label>
							<input type="text" name="end_time" id="end-time-field" class="form-control" data-datetime-format/>
							<!-- <input type="hidden" name="end_time" class="form-control" /> -->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<template id="adgroupBaseBudgetSetup_CAMP">
		@budget_and_schedule.BudgetSetupCamp()
	</template>
	<template id="adgroupBaseBudgetSetup_ADGROUP">
		@budget_and_schedule.BudgetSetupAd()
	</template>
}
