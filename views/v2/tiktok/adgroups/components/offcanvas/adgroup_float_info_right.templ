package offcanvas

templ AdGroupFloatRightInfo() {
	<div class="card">
		<div class="card-body p-4">
			<h6>
				<label for="adgroupName">
					<span class="fw-bold">Available audience</span>
				</label>
			</h6>
			<div class="d-flex flex-column gap-2">
				<div class="progress progress-sm">
					<div class="progress-bar" role="progressbar" style="width: 25%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
				</div>
				<p><span class="badge badge-soft-success">Balanced</span></p>
				<h6>53,073,000-64,869,000</h6>
				<div class="alert alert-success alert-dismissible alert-additional fade show" role="alert">
					<div class="alert-body">
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
						<div class="d-flex">
							<div class="flex-shrink-0 me-3">
								<i class="ri-user-smile-line fs-16 align-middle"></i>
							</div>
							<div class="flex-grow-1">
								<h5 class="alert-heading">Well done !</h5>
								<p class="mb-0">Aww yeah, you successfully read this important alert message. </p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
