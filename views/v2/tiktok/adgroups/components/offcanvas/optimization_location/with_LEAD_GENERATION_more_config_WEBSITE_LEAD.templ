package optimization_location

templ LEAD_GENERATIONMoreConfigWEBSITE_LEAD() {
	<div class="" id="optimizationLocationOptionLEAD_GENERATIONMoreConfigWEBSITE_LEAD">
		<!-- Goal -->
		<div class="d-flex gap-2 flex-column col-8">
			<label for="iconInput" class="form-label mp-0 fw-bold">Goal </label>
			<input
				type="text"
				class="form-control form-control-icon fs-14 text-success"
				id="appInfo"
				name="optimization_goal"
				value="Conversion"
				placeholder="Not set!"
				disabled
			/>
		</div>
		<!-- Event -->
		<div class="d-flex gap-2 flex-column mt-3">
			<h6 class="form-label mp-0 fw-bold">Event </h6>
			<div class="col-12  p-3" style="background-color: #dadada33;border-radius: 8px;">
				<div class="col-8">
					<label for="moreConfigDataConnectionWEBSITE" class="form-label">Data connection</label>
					<input
						type="text"
						id="moreConfigDataConnectionWEBSITE"
						class="form-control"
						name="data_connection"
						value="data connection value"
						disabled
					/>
				</div>
				<div class="col-8 mt-3">
					<label for="moreConfigOptimizationEventWEBSITE" class="form-label">Optimization event</label>
					<select
						class="form-select mb-3"
						aria-label="Default select example"
						id="moreConfigOptimizationEventWEBSITE"
						name="optimization_event"
						disabled
					>
						<option selected>Add to card</option>
					</select>
				</div>
				<div class="col-8">
					<hr/>
				</div>
				<div class="col-8 mt-3">
					<div class="form-check form-switch form-switch-lg ms-3">
						<input
							type="checkbox"
							class="form-check-input "
							name="user_comment"
							id="moreConfigOptimizationDeepFunelOptimizationWEBSITE"
							checked=""
						/>
						<label class="form-check-label" for="moreConfigOptimizationDeepFunelOptimizationWEBSITE">Deep funnel optimization</label>
					</div>
					<p class="text-muted mt-1">
						Select an additional event to apply ranking strategies for deep funnel optimizations.
					</p>
				</div>
			</div>
		</div>
		<!-- Advanced Setting -->
		<div class="accordion custom-accordionwithicon mt-3">
			<div class="accordion-item">
				<h2 class="accordion-header">
					<button
						class="accordion-button p-2 collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#optimizationLocationAdvancedSettings"
						aria-expanded="false"
						aria-controls="optimizationLocationAdvancedSettings"
					>
						<span class="avatar-title bg-soft-light fs-5 px-2 py-1" style="flex: 0 1 0%;">
							<i
								class=" ri-more-2-fill text-black-50"
							></i>
						</span>
						<span>Advanced settings</span>
					</button>
				</h2>
				<div
					id="optimizationLocationAdvancedSettings"
					class="accordion-collapse p-3 collapse show"
					aria-labelledby="heading-optimizationLocationAdvancedSettings"
				>
					<!-- Attr Settings -->
					<div class="col-8">
						<label class="fw-bold">Attribution settings</label>
					</div>
					<!-- Attr Window -->
					<div class="col-8 mt-3">
						<label for="moreConfigAttrWindowWEBSITE" class="form-label">Attribution window</label>
						<select
							class="form-select mb-3"
							aria-label="Default select example"
							id="moreConfigAttrWindowWEBSITE"
							name="optimization_attribution_window"
							disabled
						>
							<option selected>28-day click or 7-day view</option>
						</select>
					</div>
					<!-- Event Count -->
					<div class="form-check form-radio-primary mb-3 ps-0">
						<label class="form-label fw-bold">Event count</label>
						<div class="d-flex gap-3 ms-4 ps-2">
							<div class="me-6" style="width: 70px;">
								<input
									class="form-check-input"
									type="radio"
									name="event_count"
									value="EVERY"
									id="moreConfigEventCountEveryWEBSITE"
									checked="true"
								/>
								<label class="form-check-label mt-1" for="moreConfigEventCountEveryWEBSITE">
									Every
								</label>
							</div>
							<div class="">
								<input
									class="form-check-input"
									type="radio"
									name="event_count"
									value="ONCE"
									id="moreConfigEventCountOnceWEBSITE"
									checked="true"
								/>
								<label class="form-check-label mt-1" for="moreConfigEventCountOnceWEBSITE">
									Once
								</label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
