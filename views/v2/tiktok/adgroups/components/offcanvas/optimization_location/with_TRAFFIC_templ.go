// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.865
package optimization_location

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

func WithTRAFFIC() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<div class=\"\" id=\"optimizationLocationOptionTRAFFIC\"><div class=\"d-flex flex-wrap flex-column\"><div class=\"form-check form-radio-primary mb-3 ms-3\"><input class=\"form-check-input\" type=\"radio\" name=\"optimization_location\" value=\"WEBSITE\" id=\"optimizationLocationWEBSITE\" checked> <label class=\"form-check-label mt-1\" for=\"optimizationLocationWEBSITE\"><p class=\"mb-0\">Website</p><p class=\"text-muted fw-normal mb-0\">Send traffic to your website </p></label></div><div class=\"form-check form-radio-primary mb-3 ms-3\"><input class=\"form-check-input\" type=\"radio\" name=\"optimization_location\" id=\"optimizationLocationAPP\" value=\"APP\"> <label class=\"form-check-label mt-1\" for=\"optimizationLocationAPP\"><p class=\"mb-0\">App</p><p class=\"text-muted fw-normal mb-0\">Traffic to your app </p></label></div><!-- App Value --><div id=\"optimizationLocationContainerAPP\" class=\"flex-1 ps-5\" style=\"display: none;\"><div class=\"d-flex gap-2 align-items-center\"><label for=\"iconInput\" class=\"form-label mp-0\">App selected: </label><div class=\"form-icon\"><input type=\"text\" class=\"form-control form-control-icon fs-14 text-success\" id=\"appInfo\" name=\"app_info\" value=\"Facebook App\" placeholder=\"Not set!\" disabled> <i class=\" ri-function-line fs-14 text-success\"></i></div></div></div></div></div>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
