package optimization_location

templ LEAD_GENERATIONMoreConfigTIKTOK_DIRECT_MESSAGES() {
	<div class="" id="optimizationLocationOptionLEAD_GENERATIONMoreConfigTIKTOK_DIRECT_MESSAGES">
		<!-- Goal -->
		<div class="d-flex gap-2 flex-column col-8">
			<h6 class="form-label mp-0 fw-bold">Goal </h6>
			<input
				type="text"
				class="form-control form-control-icon fs-14 text-success"
				id="appInfo"
				name="optimization_goal"
				value="Conversion"
				placeholder="Not set!"
				disabled
			/>
		</div>
		<!-- Advanced Setting -->
		<div class="accordion custom-accordionwithicon mt-3">
			<div class="accordion-item">
				<h2 class="accordion-header">
					<button
						class="accordion-button p-2 collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#optimizationLocationAdvancedSettings"
						aria-expanded="false"
						aria-controls="optimizationLocationAdvancedSettings"
					>
						<span class="avatar-title bg-soft-light fs-5 px-2 py-1" style="flex: 0 1 0%;">
							<i
								class=" ri-more-2-fill text-black-50"
							></i>
						</span>
						<span>Advanced settings</span>
					</button>
				</h2>
				<div
					id="optimizationLocationAdvancedSettings"
					class="accordion-collapse p-3 collapse show"
					aria-labelledby="heading-optimizationLocationAdvancedSettings"
				>
					<!-- Attr Settings -->
					<div class="col-8">
						<label class="fw-bold">Attribution settings</label>
					</div>
					<!-- Attr Window -->
					<div class="col-8 mt-3">
						<label for="moreConfigAttrWindowWEBSITE" class="form-label">Attribution window</label>
						<select
							class="form-select mb-3"
							aria-label="Default select example"
							id="moreConfigAttrWindowWEBSITE"
							name="optimization_attribution_window"
							disabled
						>
							<option selected>28-day click or 7-day view</option>
						</select>
					</div>
				</div>
			</div>
		</div>
	</div>
}
