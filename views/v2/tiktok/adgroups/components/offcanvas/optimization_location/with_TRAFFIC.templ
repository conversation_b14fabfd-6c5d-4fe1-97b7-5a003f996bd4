package optimization_location

templ WithTRAFFIC() {
	<div class="" id="optimizationLocationOptionTRAFFIC">
		<div class="d-flex flex-wrap flex-column">
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input
					class="form-check-input"
					type="radio"
					name="optimization_location"
					value="WEBSITE"
					id="optimizationLocationWEBSITE"
					checked
				/>
				<label class="form-check-label mt-1" for="optimizationLocationWEBSITE">
					<p class="mb-0">Website</p>
					<p class="text-muted fw-normal mb-0">Send traffic to your website </p>
				</label>
			</div>
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input class="form-check-input" type="radio" name="optimization_location" id="optimizationLocationAPP" value="APP"/>
				<label class="form-check-label mt-1" for="optimizationLocationAPP">
					<p class="mb-0">App</p>
					<p class="text-muted fw-normal mb-0">Traffic to your app </p>
				</label>
			</div>
			<!-- App Value -->
			<div id="optimizationLocationContainerAPP" class="flex-1 ps-5" style="display: none;">
				<div class="d-flex gap-2 align-items-center">
					<label for="iconInput" class="form-label mp-0">App selected: </label>
					<div class="form-icon">
						<input
							type="text"
							class="form-control form-control-icon fs-14 text-success"
							id="appInfo"
							name="app_info"
							value="Facebook App"
							placeholder="Not set!"
							disabled
						/>
						<i class=" ri-function-line fs-14 text-success"></i>
					</div>
				</div>
			</div>
		</div>
	</div>
}
