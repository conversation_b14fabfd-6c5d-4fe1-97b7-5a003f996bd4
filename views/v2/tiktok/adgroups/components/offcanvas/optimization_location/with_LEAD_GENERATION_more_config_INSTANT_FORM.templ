package optimization_location

templ LEAD_GENERATIONMoreConfigINSTANT_FORM() {
	<div class="" id="optimizationLocationOptionLEAD_GENERATIONMoreConfigINSTANT_FORM">
		<!-- Goal -->
		<div class="d-flex gap-2 flex-column col-8">
			<label for="moreConfigGoalINSTANT_FORM" class="form-label mp-0 fw-bold">Goal </label>
			<input
				type="text"
				class="form-control form-control-icon fs-14 text-success"
				id="moreConfigGoalINSTANT_FORM"
				name="optimization_goal"
				value="Form submission"
				placeholder="Not set!"
				disabled
			/>
		</div>
		<!-- Event -->
		<div class="d-flex gap-2 flex-column mt-3">
			<h6 class="form-label mp-0 fw-bold">Event </h6>
			<div class="col-12  p-3 mt-0" style="background-color: #dadada33;border-radius: 8px;">
				<div class="col-8 mt-0">
					<div class="form-check form-switch form-switch-lg ms-3">
						<input
							type="checkbox"
							class="form-check-input "
							name="user_comment"
							id="moreConfigOptimizationDeepFunelOptimizationWEBSITE"
							checked=""
						/>
						<label class="form-check-label" for="moreConfigOptimizationDeepFunelOptimizationWEBSITE">Deep funnel optimization</label>
					</div>
					<p class="text-muted mt-1 mb-0">
						Select an additional event to apply ranking strategies for deep funnel optimizations.
					</p>
				</div>
			</div>
		</div>
	</div>
}
