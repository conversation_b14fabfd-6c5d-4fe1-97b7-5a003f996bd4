package optimization_location

templ WithLEAD_GENERATION() {
	<div class="" id="optimizationLocationOptionLEAD_GENERATION">
		<div class="d-flex flex-wrap flex-column">
			<h6 class="fw-bold">Location</h6>
			<!-- WEBSITE -->
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input
					class="form-check-input"
					type="radio"
					name="optimization_location"
					value="WEBSITE_LEAD"
					id="optimizationLocationWEBSITE"
					checked="true"
				/>
				<label class="form-check-label mt-1" for="optimizationLocationWEBSITE">
					<p class="mb-0">Website</p>
					<p class="text-muted fw-normal mb-0">Collect leads through your website</p>
				</label>
			</div>
			<!-- INSTANT FORM -->
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input
					class="form-check-input"
					type="radio"
					name="optimization_location"
					id="optimizationLocationINSTANT_FORM"
					value="INSTANT_FORM"
				/>
				<label class="form-check-label mt-1" for="optimizationLocationINSTANT_FORM">
					<p class="mb-0">Instant Form</p>
					<p class="text-muted fw-normal mb-0">Collection leads through an instant form </p>
				</label>
			</div>
			<!-- TIKTOK DIRECT MESSAGE -->
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input
					class="form-check-input"
					type="radio"
					name="optimization_location"
					id="optimizationLocationTIKTOK_DIRECT_MESSAGES"
					value="TIKTOK_DIRECT_MESSAGES"
				/>
				<label class="form-check-label mt-1" for="optimizationLocationTIKTOK_DIRECT_MESSAGES">
					<p class="mb-0">Tiktok direct messages</p>
					<p class="text-muted fw-normal mb-0">Collect and connect with leads through TikTok direct messages</p>
				</label>
			</div>
			<!-- INSTANT MESSAGING APPS -->
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input
					class="form-check-input"
					type="radio"
					name="optimization_location"
					id="optimizationLocationINSTANT_MESSAGING_APPS"
					value="INSTANT_MESSAGING_APPS"
				/>
				<label class="form-check-label mt-1" for="optimizationLocationINSTANT_MESSAGING_APPS">
					<p class="mb-0">Instant messaging apps</p>
					<p class="text-muted fw-normal mb-0">Start conversation on with WhatsApp, Messagenger, and other messaging apps</p>
				</label>
			</div>
			<!-- More config for location choose -->
			<div id="moreConfigContainerByLocation" class="flex-1"></div>
		</div>
	</div>
	<template id="moreConfigContainerByLocationWEBISTE_LEAD">
		@LEAD_GENERATIONMoreConfigWEBSITE_LEAD()
	</template>
	<template id="moreConfigContainerByLocationINSTANT_FORM">
		@LEAD_GENERATIONMoreConfigINSTANT_FORM()
	</template>
	<template id="moreConfigContainerByLocationTIKTOK_DIRECT_MESSAGES">
		@LEAD_GENERATIONMoreConfigTIKTOK_DIRECT_MESSAGES()
	</template>
	<template id="moreConfigContainerByLocationINSTANT_MESSAGING_APPS">
		@LEAD_GENERATIONMoreConfigINSTANT_MESSAGING_APPS()
	</template>
}
