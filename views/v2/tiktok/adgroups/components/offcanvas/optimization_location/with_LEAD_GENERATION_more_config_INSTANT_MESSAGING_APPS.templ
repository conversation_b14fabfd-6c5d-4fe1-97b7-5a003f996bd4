package optimization_location

import "github.com/dev-networldasia/dspgos/gos/templates"

templ LEAD_GENERATIONMoreConfigINSTANT_MESSAGING_APPS() {
	<div class="" id="optimizationLocationOptionLEAD_GENERATIONMoreConfigINSTANT_MESSAGING_APPS">
		<!-- Goal -->
		<div class="d-flex gap-2 flex-column col-8">
			<label for="moreConfigOptimizationGoalWEBSITE" class="form-label mp-0 fw-bold">Goal </label>
			<select class="form-select" aria-label="Default select example" id="moreConfigOptimizationGoalWEBSITE" name="optimization_goal">
				<option selected value="conversation">Click</option>
				<option value="conversation">Conversation</option>
			</select>
		</div>
		<!-- Event -->
		<div class="d-flex gap-2 flex-column mt-3">
			<label class="form-label mp-0 fw-bold">Event </label>
			<div class="col-12  p-3" style="background-color: #dadada33;border-radius: 8px;">
				<div class="col-8">
					<label for="moreConfigAppINSTANT_MESSAGING_APPS" class="form-label">Select your app</label>
					<select class="form-select mb-3" aria-label="Default select example" id="moreConfigAppINSTANT_MESSAGING_APPS" name="app">
						<option selected value="WHATSAPP">WhatsApp</option>
						<option value="ZALO">Zalo</option>
						<option value="LINE">Line</option>
						<option value="INSTANT_MESSAGING">Instant Messaging URL</option>
					</select>
				</div>
				<div class="col-8 mt-3">
					<label for="moreConfigNumberPhoneWEBSITE" class="form-label">WhatsApp phone number</label>
					<div class="input-group" data-input-flag>
						<button class="btn btn-light border" type="button" data-bs-toggle="dropdown" aria-expanded="false">
							<img
								src={ templates.AssetURL("/static/images/flags/vn.svg") }
								alt="flag img"
								height="20"
								class="country-flagimg rounded"
							/><span class="ms-2 country-codeno">+ 84</span>
						</button>
						<input
							id="moreConfigNumberPhoneWEBSITE"
							type="text"
							class="form-control rounded-end flag-input"
							value=""
							placeholder="Enter number"
							oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
						/>
						<div class="dropdown-menu w-100">
							<div class="p-2 px-3 pt-1 searchlist-input">
								<input
									type="text"
									class="form-control form-control-sm border search-countryList"
									placeholder="Search country name or country code..."
								/>
							</div>
							<ul class="list-unstyled dropdown-menu-list mb-0"></ul>
						</div>
					</div>
				</div>
				<div class="col-8 mt-3">
					<label for="moreConfigOptimizationGoalINSTANT_MESSAGING_APPS" class="form-label mp-0">Message event set </label>
					<select
						class="form-select mb-3"
						aria-label="Default select example"
						id="moreConfigOptimizationGoalINSTANT_MESSAGING_APPS"
						name="message_event_set"
					>
						<option selected value="conversation">Click</option>
						<option value="conversation">Conversation</option>
					</select>
				</div>
				<div class="col-8">
					<hr/>
				</div>
				<div class="col-8 mt-3">
					<div class="form-check form-switch form-switch-lg ms-3">
						<input
							type="checkbox"
							class="form-check-input "
							name="user_comment"
							id="moreConfigOptimizationDeepFunelOptimizationWEBSITE"
							checked=""
						/>
						<label class="form-check-label" for="moreConfigOptimizationDeepFunelOptimizationWEBSITE">Deep funnel optimization</label>
					</div>
					<p class="text-muted mt-1">
						Select an additional event to apply ranking strategies for deep funnel optimizations.
					</p>
				</div>
			</div>
		</div>
		<!-- Advanced Setting -->
		<div class="accordion custom-accordionwithicon mt-3">
			<div class="accordion-item">
				<h2 class="accordion-header">
					<button
						class="accordion-button p-2 collapsed"
						type="button"
						data-bs-toggle="collapse"
						data-bs-target="#optimizationLocationAdvancedSettings"
						aria-expanded="false"
						aria-controls="optimizationLocationAdvancedSettings"
					>
						<span class="avatar-title bg-soft-light fs-5 px-2 py-1" style="flex: 0 1 0%;">
							<i
								class=" ri-more-2-fill text-black-50"
							></i>
						</span>
						<span>Advanced settings</span>
					</button>
				</h2>
				<div
					id="optimizationLocationAdvancedSettings"
					class="accordion-collapse p-3 collapse show"
					aria-labelledby="heading-optimizationLocationAdvancedSettings"
				>
					<!-- Attr Settings -->
					<div class="col-8">
						<label class="fw-bold">Attribution settings</label>
					</div>
					<!-- Attr Window -->
					<div class="col-8 mt-3">
						<label for="moreConfigAttrWindowWEBSITE" class="form-label">Attribution window</label>
						<select
							class="form-select mb-3"
							aria-label="Default select example"
							id="moreConfigAttrWindowWEBSITE"
							name="optimization_attribution_window"
							disabled
						>
							<option selected>28-day click or 7-day view</option>
						</select>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script src="/static/js/common/flag-input.js" defer></script>
}
