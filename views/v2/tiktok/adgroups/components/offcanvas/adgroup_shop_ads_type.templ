package offcanvas

templ AdGroupShopAdsType() {
	<!-- Ads Type Cards -->
	<div class="card" id="ads-type-cards">
		<div class="card-body p-4">
			<!-- Shop Ads Type Section -->
			<div class="mb-4">
				<h5 class="card-title mb-3">Shop Ads type</h5>
				<div class="row px-3 gap-3">
					<!-- Video Shopping Ads -->
					<div class="border shadow-none ads-card flex-1 rounded" data-type="video">
						<div class="card-body text-center">
							<h5 class="card-title">Video Shopping Ads</h5>
							<p class="text-muted mb-0">Use a video or image to promote your products.</p>
						</div>
					</div>
					<!-- LIVE Shopping Ads -->
					<div class="border shadow-none ads-card flex-1 rounded active" data-type="live">
						<div class="card-body text-center">
							<h5 class="card-title">LIVE Shopping Ads</h5>
							<p class="text-muted mb-0">Use a real-time stream to promote and sell your products.</p>
						</div>
					</div>
					<!-- Product Shopping Ads -->
					<div class="border shadow-none ads-card flex-1 rounded" data-type="product">
						<div class="card-body text-center">
							<h5 class="card-title">Product Shopping Ads</h5>
							<p class="text-muted mb-0">Use images to promote your products.</p>
						</div>
					</div>
				</div>
			</div>
			<!-- Identity Selection Section -->
			<div id="section-identity">
				<h6 class="card-title mb-2">Identity</h6>
				<p class="text-muted">
					Select the TikTok account that you'll use to go LIVE as your identity. Your ad will start running when a
					LIVE begins on this account during the scheduled ad time.
				</p>
				<div class="row align-items-center">
					<div class="col-md-6">
						<select class="form-select" aria-label="Select identity" id="identity_select">
							<option selected>Select an identity</option>
						</select>
					</div>
					<div class="col-md-1">
						<button type="button" class="btn btn-light border"><i class="ri-refresh-line"></i></button>
					</div>
					<div class="col-md-1">
						<button type="button" class="btn btn-light border"><i class="ri-file-add-line"></i></button>
					</div>
				</div>
			</div>
			<!-- TikTok Shop Section -->
			<div id="section-shop" class="d-none mt-4">
				<h6 class="card-title mb-2">Product source details</h6>
				<div class="form-check mb-3">
					<input class="form-check-input" type="radio" name="shop-source" id="shopTikTok" checked/>
					<label class="form-check-label" for="shopTikTok">TikTok Shop</label>
				</div>
				<div class="bg-light border rounded p-3 mb-3">
					<div class="d-flex align-items-start">
						<i class="ri-store-line fs-3 me-2 text-info"></i>
						<div>
							<p class="mb-1 fw-semibold">Create or connect a TikTok Shop to drive more sales</p>
							<p class="text-muted mb-2 small">
								Reach the right audience for your products and gain powerful customer insights when you offer a seamless shopping experience to over 150 million active users on TikTok.
							</p>
							<button class="btn btn-soft-primary btn-sm">Create Shop</button>
							<a href="#" class="ms-2 text-decoration-underline">To connect a Shop, follow the steps in Help Center</a>
						</div>
					</div>
				</div>
				<!-- Showcase Option -->
				<div id="showcase-option" class="form-check">
					<input class="form-check-input" type="radio" name="shop-source" id="showcaseOption"/>
					<label class="form-check-label" for="showcaseOption">Showcase</label>
				</div>
			</div>
		</div>
	</div>
	// <script>
	//     const cards = document.querySelectorAll('.ads-card');
	//     const identitySection = document.getElementById('section-identity');
	//     const shopSection = document.getElementById('section-shop');
	//     const showcaseOption = document.getElementById('showcase-option');
	//     cards.forEach(card => {
	//         card.addEventListener('click', () => {
	//             // Xóa trạng thái active và text-success khỏi tất cả cards
	//             cards.forEach(c => {
	//                 c.classList.remove('active', 'card-border-success');
	//                 c.querySelector('.card-title')?.classList.remove('text-success');
	//             });
	//             // Thêm trạng thái active + text-success cho card được click
	//             card.classList.add('active', 'card-border-success');
	//             card.querySelector('.card-title')?.classList.add('text-success');
	//             // Show section theo data-type
	//             const type = card.dataset.type;
	//             if (type === 'live') {
	//                 identitySection.classList.remove('d-none');
	//                 shopSection.classList.add('d-none');
	//             } else if (type === 'video') {
	//                 identitySection.classList.add('d-none');
	//                 shopSection.classList.remove('d-none');
	//                 showcaseOption.classList.remove('d-none');
	//             } else if (type === 'product') {
	//                 identitySection.classList.add('d-none');
	//                 shopSection.classList.remove('d-none');
	//                 showcaseOption.classList.add('d-none');
	//             }
	//         });
	//     });
	// </script>
}
