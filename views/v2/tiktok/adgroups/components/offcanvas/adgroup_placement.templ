package offcanvas

import "godsp/views/v2/tiktok/adgroups/components/offcanvas/placement"

templ AdGroupPlacement() {
	<div class="card">
		<div class="card-body p-4">
			<h6>
				<label for="adgroupName">
					<span class="fw-bold fs-16">Placements</span>
				</label>
			</h6>
			<!-- Option placement by Outcome Objective -->
			<div id="optionPlacementByOutcomeObjective"></div>
			<!-- Advanced settings -->
			@placement.PlacementAdvancedSetting()
		</div>
	</div>
	<template id="optionPlacementHtmlByREACH">
		@placement.PlacementOptionREACH()
	</template>
	<template id="optionPlacementHtmlByTRAFFIC">
		@placement.PlacementOptionTRAFFIC()
	</template>
	<template id="optionPlacementHtmlBySALES">
		@placement.PlacementOptionSALES()
	</template>
}
