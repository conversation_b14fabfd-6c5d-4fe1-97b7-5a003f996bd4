package offcanvas

import "godsp/views/v2/tiktok/adgroups/components/offcanvas/targeting"

templ AdGroupTargeting() {
	<div class="card">
		<div class="card-body p-4">
			<h6>
				<label for="adgroupName">
					<span class="fw-bold fs-16 ">Targeting</span>
				</label>
			</h6>
			<div class="d-flex flex-column gap-3">
				<!-- Demographics Targeting -->
				<div class="accordion-item trd-custom border rounded border-opacity-50">
					<div class="accordion-header accordion-button justify-content-between ps-0 pe-2">
						<div class="d-flex gap-2 align-items-center py-2 flex-1">
							<span
								class="avatar-title bg-soft-light fs-5 px-2 py-1 border-end btn-toggle collapsed"
								data-bs-toggle="collapse"
								data-bs-target="#demographicsTargetingGroup"
								aria-expanded="false"
								style="flex: 0 1 0%;"
							>
								<i
									class="ri-play-fill text-black-50"
								></i>
							</span>
							<div class="fs-14 fw-bold">
								<p class="mb-0">Demographics</p>
							</div>
						</div>
					</div>
					<div id="demographicsTargetingGroup" class="accordion-collapse multi-collapse collapse">
						<div class="accordion-body p-3 d-flex flex-column gap-3">
							<!-- Location Targeting -->
							@targeting.AdGroupTargetingLocation()
							<!-- Age Targeting -->
							@targeting.AdGroupTargetingAge()
							<!-- Gender Targeting -->
							@targeting.AdGroupTargetingGender()
							<!-- Language Targeting -->
							@targeting.AdGroupTargetingLanguages()
							<!-- Spending Targeting -->
							@targeting.AdGroupTargetingPendingPower()
						</div>
					</div>
				</div>
				<!-- Audience Targeting -->
				<div class="accordion-item trd-custom border rounded border-opacity-50">
					<div class="accordion-header accordion-button justify-content-between ps-0 pe-2">
						<div class="d-flex gap-2 align-items-center py-2 flex-1">
							<span
								class="avatar-title bg-soft-light fs-5 px-2 py-1 border-end btn-toggle collapsed"
								data-bs-toggle="collapse"
								data-bs-target="#audienceTargetingGroup"
								aria-expanded="false"
								style="flex: 0 1 0%;"
							>
								<i
									class="ri-play-fill text-black-50"
								></i>
							</span>
							<div class="fs-14 fw-bold">
								<p class="mb-0">Audiences</p>
							</div>
						</div>
					</div>
					<div id="audienceTargetingGroup" class="accordion-collapse multi-collapse collapse show">
						<div class="accordion-body p-3 d-flex flex-column gap-3">
							<!-- Audience Targeting -->
							@targeting.AdGroupTargetingAudience()
						</div>
					</div>
				</div>
				<!-- Interest & Behavior Targeting -->
				<div class="accordion-item trd-custom border rounded border-opacity-50">
					<div class="accordion-header accordion-button justify-content-between ps-0 pe-2">
						<div class="d-flex gap-2 align-items-center py-2 flex-1">
							<span
								class="avatar-title bg-soft-light fs-5 px-2 py-1 border-end btn-toggle collapsed"
								data-bs-toggle="collapse"
								data-bs-target="#interestBehaviorTargetingGroup"
								aria-expanded="false"
								style="flex: 0 1 0%;"
							>
								<i
									class="ri-play-fill text-black-50"
								></i>
							</span>
							<div class="fs-14 fw-bold">
								<p class="mb-0">Interests & Beshaviors</p>
							</div>
						</div>
					</div>
					<div id="interestBehaviorTargetingGroup" class="accordion-collapse multi-collapse collapse show">
						<div class="accordion-body p-3 d-flex flex-column gap-3">
							<!-- Behavior Targeting -->
							@targeting.AdGroupTargetingInterestBehavior()
						</div>
					</div>
				</div>
				<!-- Device Targeting -->
				<div class="accordion-item trd-custom border rounded border-opacity-50">
					<div class="accordion-header accordion-button justify-content-between ps-0 pe-2">
						<div class="d-flex gap-2 align-items-center py-2 flex-1">
							<span
								class="avatar-title bg-soft-light fs-5 px-2 py-1 border-end btn-toggle collapsed"
								data-bs-toggle="collapse"
								data-bs-target="#deviceTargetingGroup"
								aria-expanded="false"
								style="flex: 0 1 0%;"
							>
								<i
									class="ri-play-fill text-black-50"
								></i>
							</span>
							<div class="fs-14 fw-bold">
								<p class="mb-0">Device</p>
							</div>
						</div>
					</div>
					<div id="deviceTargetingGroup" class="accordion-collapse multi-collapse collapse show">
						<div class="accordion-body p-3 d-flex flex-column gap-3">
							<!-- Operating System Targeting -->
							@targeting.AdGroupTargetingOperatingSystem()
							<!-- Connection Type Targeting -->
							@targeting.AdGroupTargetingConnectionType()
							<!-- Carrier Targeting -->
							@targeting.AdGroupTargetingCarriers()
							<!-- Internet Provider Targeting -->
							@targeting.AdGroupTargetingInternetProvider()
							<!-- Device Price Targeting -->
							@targeting.AdGroupTargetingDevicePrice()
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
