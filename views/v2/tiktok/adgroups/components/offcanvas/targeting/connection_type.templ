package targeting

templ AdGroupTargetingConnectionType() {
	<div class="" id="connectionTypeTargeting">
		<label class="fw-bold ">
			Connection Type
		</label>
		<div class="selection-targeting d-flex align-items-center flex-wrap">
			<label class="selection-option">
				<input type="checkbox" name="connection_type" value="" hidden/>
				<span>All</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="connection_type" value="WIFI" hidden/>
				<span>Wifi</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="connection_type" value="2G" hidden/>
				<span>2G</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="connection_type" value="3G" hidden/>
				<span>3G</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="connection_type" value="4G" hidden/>
				<span>4G</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="connection_type" value="5G" hidden/>
				<span>5G</span>
			</label>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        const $container = $('#connectionTypeTargeting');
        const $checkboxes = $container.find('.selection-option input[type="checkbox"]');
        const $checkboxAll = $container.find('input[name="age"][value=""]');

        $checkboxes.on('change', function () {
            const $this = $(this);
            const selectedValues = $checkboxes.filter(':checked').map(function () {
                return this.value;
            }).get();


            if ($this.val() === 'all') {
                // Nếu "All" được chọn → bỏ chọn các checkbox khác
                $checkboxes.not($this).prop('checked', false);
            } else {
                if (selectedValues.length === 0) {
                    // Nếu không có gì được chọn → chọn lại "All"
                    $checkboxAll.prop('checked', true);
                } else {
                    // Nếu có item khác được chọn → bỏ chọn "All"
                    $checkboxAll.prop('checked', false);
                }
            }
        });
    });
</script>
}
