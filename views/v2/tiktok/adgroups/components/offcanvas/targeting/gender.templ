package targeting

templ AdGroupTargetingGender() {
	<div id="genderTargeting">
		<label class="fw-bold required-field">
			Gender
		</label>
		<div class="selection-targeting d-flex align-items-center flex-wrap">
			<label class="selection-option">
				<input type="checkbox" name="render" value="GENDER_UNLIMITED" hidden/>
				<span>All</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="render" value="GENDER_MALE" hidden/>
				<span>Male</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="render" value="GENDER_FEMALE" hidden/>
				<span>Female</span>
			</label>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        const $container = $('#genderTargeting');
        const $checkboxes = $container.find('.selection-option input[type="checkbox"]');
        const $checkboxAll = $container.find('input[name="age"][value="GENDER_UNLIMITED"]');

        $checkboxes.on('change', function () {
            const $this = $(this);
            const selectedValues = $checkboxes.filter(':checked').map(function () {
                return this.value;
            }).get();


            if ($this.val() === 'GENDER_UNLIMITED') {
                // Nếu "All" được chọn → bỏ chọn các checkbox khác
                $checkboxes.not($this).prop('checked', false);
            } else {
                if (selectedValues.length === 0) {
                    // Nếu không có gì được chọn → chọn lại "All"
                    $checkboxAll.prop('checked', true);
                } else {
                    // Nếu có item khác được chọn → bỏ chọn "All"
                    $checkboxAll.prop('checked', false);
                }
            }
        });
    });
</script>
}
