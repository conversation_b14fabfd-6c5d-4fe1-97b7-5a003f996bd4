package targeting

templ AdGroupTargetingCarriers() {
	<div class="" id="carrierTargeting">
		<label class="fw-bold ">
			Carriers
		</label>
		<div>
			<select
				class="form-select form-control w-75"
				id="carrierChoices"
				name="carrier"
				multiple
				data-placeholder="--- Choose carrier ---"
				style="width: 100%"
			>
				// <option value="" disabled selected hidden>All</option>
			</select>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        // Render Choices
        const data = [
            { id: "", value: '', label: 'All', selected: true },
            { id: "vn_mobi_fone", value: 'vn_mobi_fone', label: 'VN: MobiFone' },
            { id: "vn_viettel_mobile", value: 'vn_viettel_mobile', label: 'VN: Viettel Mobile' },
            { id: "vn_vinaphone", value: 'vn_vinaphone', label: 'VN: Vinaphone' },
        ]

        const element = document.getElementById('carrierChoices');


        const choices = new Choices(element, {
            shouldSort: false,
            allowHTML: true,
            removeItemButton: true,
            renderSelectedChoices: "auto",
            placeholder: true,
            searchEnabled: true,
            classNames: {
                containerOuter: "choices tiktok-custom",
            },
            choices: data,
        });
    });
</script>
}
