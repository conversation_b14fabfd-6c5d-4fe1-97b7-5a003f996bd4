package targeting

templ AdGroupTargetingLocation() {
	<div id="locationTargeting" class="tiktok-custom locations-targeting-blk targetings-blk__item" style="position: relative;">
		<label for="locations-targeting" class="fw-bold required-field">
			Locations
			<button
				class="btn p-0 "
				type="button"
				id="btn-clear-value-input-search-location"
				role="button"
				data-bs-toggle="popover"
				data-bs-trigger="focus"
				data-bs-html="true"
				data-bs-content="Choosing a broad area to show your ads within can improve results. For example, by adding a country instead of several cities."
			>
				<i class="ri-information-line"></i>
			</button>
		</label>
		<p>Reach people living in or recently in this location.</p>
		<div class="results-selected-multiple-group" id="geo_location_results_selected" style="display: block;">
			<div class="group-item" data-value="VN">
				<div class="header text-secondary">Vietnam</div>
				<div class="selected-item" data-id="VN" data-group="VN" data-name="Vietnam">
					<div class="">Vietnam</div>
					<i class="ri-close-circle-fill btn-remove-item"></i>
				</div>
			</div>
		</div>
		<div class="form-icon right input-search-control d-flex flex-row gap-2 mt-0" id="input-control-search-locations">
			<input
				data-value='{"COUNTRY":["1562822"]}'
				name="targeting[geo_locations][]"
				type="text"
				id="selectedGeoLocations"
				class="form-control"
				placeholder="Choose locations"
				autocomplete="off"
			/>
			<i class="ri-close-circle-fill icon-close btn-clear-value-input-source"></i>
		</div>
		<div id="searchSearchLocationsPopover" class="popover-search popover bs-popover-bottom shadow" style="display: none;">
			<div class="popover-arrow" style="left: calc(50% - 10px);"></div>
			<div class="popover-body" style="width:100%">
				<div id="region-container"></div>
			</div>
		</div>
		<!-- <div class="my-3">
        <select id="locations-targeting" class="form-select form-control" name="targeting[geo_locations][countries][]" multiple="multiple"
            data-placeholder="Choose locations" style="width: 100%"></select>
    </div>
    <a href="#">Add locations in bulk</a> -->
		<!-- Include Audience -->
		<!-- <div class="position-relative search-source-audience-control">
        <label class="fw-light">
            Reach people living in or recently in this location
        </label>

    </div> -->
	</div>
}
