package targeting

templ AdGroupTargetingAge() {
	<div id="ageTargeting">
		<label class="fw-bold required-field">
			Age
		</label>
		<div class="selection-targeting d-flex align-items-center flex-wrap">
			<label class="selection-option">
				<input type="checkbox" name="age" value="all" hidden/>
				<span>All</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="age" value="13-17" hidden/>
				<span>13–17</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="age" value="18-24" checked hidden/>
				<span>18–24</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="age" value="25-34" checked hidden/>
				<span>25–34</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="age" value="35-44" checked hidden/>
				<span>35–44</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="age" value="45-54" hidden/>
				<span>45–54</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="age" value="55+" hidden/>
				<span>55+</span>
			</label>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        const $container = $('#ageTargeting');
        const $checkboxes = $container.find('.selection-option input[type="checkbox"]');
        const $checkboxAll = $container.find('input[name="age"][value="all"]');

        $checkboxes.on('change', function () {
            const $this = $(this);
            const selectedValues = $checkboxes.filter(':checked').map(function () {
                return this.value;
            }).get();

            console.log('Selected Ages:', selectedValues);

            if ($this.val() === 'all') {
                $checkboxes.not($this).prop('checked', false);
            } else {
                if (selectedValues.length === 0) {
                    $checkboxAll.prop('checked', true);
                } else {
                    $checkboxAll.prop('checked', false);
                }
            }
        });
    });
</script>
}
