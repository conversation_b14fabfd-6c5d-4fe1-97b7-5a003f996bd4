package targeting

templ AdGroupTargetingDevicePrice() {
	<div id="devicePriceTargeting">
		<label class="fw-bold ">
			Device price
		</label>
		<div class="ps-3">
			<div class="form-check form-radio-primary mb-3">
				<input class="form-check-input" type="radio" name="device_price" value="" id="optionPriceAny" checked/>
				<label class="form-check-label mt-1" for="optionPriceAny">
					Any price
				</label>
			</div>
			<div class="form-check form-radio-primary mb-3">
				<input class="form-check-input" type="radio" name="device_price" value="specific" id="optionPriceSpecific"/>
				<div class="d-flex flex-column">
					<label class="form-check-label mt-1" for="optionPriceSpecific">
						Specific range
					</label>
					<div id="specificRange" style="display: none;" class="container p-0 mt-3 was-validated">
						<div class="d-flex align-items-center gap-2">
							<div class="input-group price-select-group">
								<select class="form-select" id="minPrice"></select>
								<span class="input-group-text">USD</span>
							</div>
							<span class="fw-semibold">-</span>
							<div class="input-group price-select-group">
								<select class="form-select" id="maxPrice"></select>
								<span class="input-group-text">USD</span>
							</div>
						</div>
						<div class="invalid-feedbac">
							<div class="invalid-feedback" id="priceError" style="display: none;">
								Device price: Select a valid range
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	@adGroupTargetingDevicePriceScript()
}

templ adGroupTargetingDevicePriceScript() {
	<script>
    document.addEventListener("DOMContentLoaded", function () {
       
    });
</script>
}
