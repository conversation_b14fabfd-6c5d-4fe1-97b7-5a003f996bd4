package targeting

templ AdGroupTargetingInterestBehavior() {
	<div id="interestBehaviorTargeting">
		<!-- Include Audience -->
		<div class="position-relative search-source-audience-control">
			<label class="fw-light">
				<b>Interests & Beshaviors</b>
				<p clas="text-muted font-italic">
					Target an audience based on their long-term interests and interactions with content on TikTok.
				</p>
			</label>
			<!-- List Results -->
			<div class="results-selected-multiple-group tiktok-custom" id="detailTargetingResultsSelected" style="display: none;"></div>
			<!-- Search Box -->
			<div id="inputControlSearchDetatlTargeting" class="form-icon right d-flex flex-row  mt-2 input-search-control input-group">
				<input
					data-value="{}"
					name="targeting[interestsAndBehaviors][]"
					type="text"
					id="selectedDetailTargeting"
					class="form-control"
					placeholder="Search or select interests & behaviors by click Browser"
					autocomplete="off"
				/>
				<button type="button" class="btn btn-success">
					<span id="btnDetailTargetingBrowse">Browse</span>
				</button>
			</div>
			<!-- Browse Interests & Behaviors -->
			<div id="browseDetailTargetingPopover" class="popover-search popover bs-popover-bottom shadow" style="display: none;">
				<div class="popover-arrow" style="left: calc(50% - 10px);"></div>
				<div class="popover-body" style="width:100%">
					<div class="row">
						<div class="col-md-4 border-end">
							<div class="nav flex-column nav-pills nav-success" id="interest-and-behaviors-tab" role="tablist" aria-orientation="vertical">
								<a class="nav-link mb-2 active" id="interest-and-behaviors-interests-tab" data-bs-toggle="pill" href="#interest-and-behaviors-interests" role="tab" aria-controls="interest-and-behaviors-interests" aria-selected="true">Interests</a>
								<a class="nav-link mb-2" id="interest-and-behaviors-video-interactions-tab" data-bs-toggle="pill" href="#interest-and-behaviors-video-interactions" role="tab" aria-controls="interest-and-behaviors-video-interactions" aria-selected="false" tabindex="-1">Video interactions</a>
								<a class="nav-link mb-2" id="interest-and-behaviors-creator-interactions-tab" data-bs-toggle="pill" href="#interest-and-behaviors-creator-interactions" role="tab" aria-controls="interest-and-behaviors-creator-interactions" aria-selected="false" tabindex="-1">Creator interactions</a>
								<a class="nav-link" id="interest-and-behaviors-hashtag-interactions-tab" data-bs-toggle="pill" href="#interest-and-behaviors-hashtag-interactions" role="tab" aria-controls="interest-and-behaviors-hashtag-interactions" aria-selected="false" tabindex="-1">Hashtag interactions</a>
							</div>
						</div><!-- end col -->
						<div class="col-md-8" style="overflow-y:auto; max-height:50vh">
							<div class="tab-content text-muted mt-4 mt-md-0" id="interest-and-behaviors-tabContent">
								<div class="tab-pane fade active show" id="interest-and-behaviors-interests" role="tabpanel" aria-labelledby="interest-and-behaviors-interests-tab"></div>
								<div class="tab-pane fade" id="interest-and-behaviors-video-interactions" role="tabpanel" aria-labelledby="interest-and-behaviors-video-interactions-tab"></div>
								<div class="tab-pane fade" id="interest-and-behaviors-creator-interactions" role="tabpanel" aria-labelledby="interest-and-behaviors-creator-interactions-tab"></div>
								<div class="tab-pane fade" id="interest-and-behaviors-hashtag-interactions" role="tabpanel" aria-labelledby="interest-and-behaviors-hashtag-interactions-tab"></div>
							</div>
						</div><!--  end col -->
					</div>
				</div>
			</div>
			<div id="searchDetailTargetingPopover" class="popover-search popover bs-popover-bottom shadow" style="display: none;">
				<div class=" popover-arrow" style="left: calc(50% - 10px);"></div>
				<div class="popover-body" style="width:100%">
					<div id="content_tab_detail_targeting">
						<ul class="p-0"></ul>
					</div>
				</div>
			</div>
		</div>
	</div>
}
