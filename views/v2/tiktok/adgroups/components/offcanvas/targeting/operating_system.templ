package targeting

templ AdGroupTargetingOperatingSystem() {
	<div class="" id="operatingSystemTargeting">
		<label class="fw-bold required-field">
			Operating system
		</label>
		<div class="selection-targeting d-flex align-items-center flex-wrap">
			<label class="selection-option">
				<input type="checkbox" name="operating_system" value="" hidden/>
				<span>All</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="operating_system" value="ANDROID" hidden/>
				<span>Android</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="operating_system" value="IOS" hidden/>
				<span>iOS</span>
			</label>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        const $container = $('#operatingSystemTargeting');
        const $checkboxes = $container.find('.selection-option input[type="checkbox"]');
        const $checkboxAll = $container.find('input[name="age"][value=""]');

        $checkboxes.on('change', function () {
            const $this = $(this);
            const selectedValues = $checkboxes.filter(':checked').map(function () {
                return this.value;
            }).get();


            if ($this.val() === 'all') {
                // Nếu "All" được chọn → bỏ chọn các checkbox khác
                $checkboxes.not($this).prop('checked', false);
            } else {
                if (selectedValues.length === 0) {
                    // Nếu không có gì được chọn → chọn lại "All"
                    $checkboxAll.prop('checked', true);
                } else {
                    // Nếu có item khác được chọn → bỏ chọn "All"
                    $checkboxAll.prop('checked', false);
                }
            }
        });
    });
</script>
}
