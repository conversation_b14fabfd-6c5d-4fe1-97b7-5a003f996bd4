package targeting

templ AdGroupTargetingAudience() {
	<div id="audienceTargeting">
		<!-- Include Audience -->
		<div class="position-relative search-source-audience-control">
			<label class="fw-light">
				<b>Include</b>
			</label>
			<div class="results-selected-multiple-group tiktok-custom" id="audience_results_selected" style="display: none;"></div>
			<div class="form-icon right input-search-control">
				<input
					data-value="{}"
					name="targeting[custom_audiences][]"
					type="text"
					id="selectedAudiences"
					class="form-control"
					placeholder="Search and Select an existing audiences"
					autocomplete="off"
				/>
				<i class="ri-close-circle-fill icon-close btn-clear-value-input-source" id="btn-clear-include-audience-search"></i>
			</div>
			<div id="searchSourceAudiencePopover" class="popover-search popover bs-popover-bottom shadow" style="display: none;">
				<div class=" popover-arrow" style="left: calc(50% - 10px);"></div>
				<div class="popover-body" style="width:100%">
					<ul class="nav nav-tabs" id="searchTabs" role="tablist">
						<li class="nav-item" role="presentation">
							<button
								class="nav-link active fw-bold"
								id="tab_all_audience-tab"
								data-bs-toggle="tab"
								data-bs-target="#tab_all_audience"
								type="button"
								role="tab"
							>All</button>
						</li>
						<li class="nav-item" role="presentation">
							<button
								class="nav-link fw-bold"
								id="tab_custom_audience-tab"
								data-bs-toggle="tab"
								data-bs-target="#tab_custom_audience"
								type="button"
								role="tab"
							>Custom audience</button>
						</li>
						<li class="nav-item" role="presentation">
							<button
								class="nav-link fw-bold"
								id="tab_lookalike_audience-tab"
								data-bs-toggle="tab"
								data-bs-target="#tab_lookalike_audience"
								type="button"
								role="tab"
							>Lookalike audience</button>
						</li>
					</ul>
					<div class="tab-content mt-2">
						<div class="tab-pane fade show active" id="tab_all_audience" role="tabpanel">
							<ul id="content_all_audience" class="list-group container-content"></ul>
						</div>
						<div class="tab-pane fade show" id="tab_custom_audience" role="tabpanel">
							<ul id="content_custom_audience" class="list-group container-content"></ul>
						</div>
						<div class="tab-pane fade" id="tab_lookalike_audience" role="tabpanel">
							<ul id="content_lookalike_audience" class="list-group container-content"></ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Exclude Audience -->
		<div class="position-relative search-source-audience-control  mt-3">
			<label class="fw-light">
				<b>Exclude</b>
			</label>
			<div class="results-exclude-selected-multiple-group tiktok-custom" id="audience_results_exclude_selected" style="display: none;"></div>
			<div class="form-icon right input-search-control">
				<input
					data-value="{}"
					type="text"
					name="targeting[excluded_custom_audiences][]"
					id="excludeSelectedAudiences"
					class="form-control"
					placeholder="Search and Select an existing audiences"
					autocomplete="off"
				/>
				<i class="ri-close-circle-fill icon-close btn-clear-value-exclude_input-source" id="btn-clear-exclude-audience-search"></i>
			</div>
			<div id="excludeSearchSourceAudiencePopover" class="popover-search popover bs-popover-bottom shadow" style="display: none;">
				<div class=" popover-arrow" style="left: calc(50% - 10px);"></div>
				<div class="popover-body" style="width:100%">
					<ul class="nav nav-tabs" id="searchTabs" role="tablist">
						<li class="nav-item" role="presentation">
							<button
								class="nav-link active fw-bold"
								id="tab_exclude_all_audience-tab"
								data-bs-toggle="tab"
								data-bs-target="#tab_exclude_all_audience"
								type="button"
								role="tab"
							>All</button>
						</li>
						<li class="nav-item" role="presentation">
							<button
								class="nav-link fw-bold"
								id="tab_exclude_custom_audience-tab"
								data-bs-toggle="tab"
								data-bs-target="#tab_exclude_custom_audience"
								type="button"
								role="tab"
							>Custom audience</button>
						</li>
						<li class="nav-item" role="presentation">
							<button
								class="nav-link fw-bold"
								id="tab_exclude_lookalike_audience-tab"
								data-bs-toggle="tab"
								data-bs-target="#tab_exclude_lookalike_audience"
								type="button"
								role="tab"
							>
								Lookalike
								audience
							</button>
						</li>
					</ul>
					<div class="tab-content mt-2">
						<div class="tab-pane fade show active" id="tab_exclude_all_audience" role="tabpanel">
							<ul id="content_exclude_all_audience" class="list-group container-content"></ul>
						</div>
						<div class="tab-pane fade show" id="tab_exclude_custom_audience" role="tabpanel">
							<ul id="content_exclude_custom_audience" class="list-group container-content"></ul>
						</div>
						<div class="tab-pane fade" id="tab_exclude_lookalike_audience" role="tabpanel">
							<ul id="content_exclude_lookalike_audience" class="list-group container-content"></ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
