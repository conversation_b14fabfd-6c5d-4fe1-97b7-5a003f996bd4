package targeting

templ AdGroupTargetingInternetProvider() {
	<div class="" id="internetProdiverTargeting">
		<label class="fw-bold ">
			Internet service provider
		</label>
		<div>
			<select
				class="form-select form-control w-75"
				id="internetProviderChoices"
				name="internet_provider"
				multiple
				data-placeholder="--- Choose internet prodiver ---"
				style="width: 100%"
			></select>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        // Render Choices
        const data = [
            { id: "", value: '', label: 'All', selected: true },
            { id: "vn_fpt_telecom", value: 'vn_fpt_telecome', label: 'VN: FPT Telecom' },
            { id: "vn_vpt", value: 'vn_vpt', label: 'VN: VNPT' },
            { id: "vn_viettell_group", value: 'vn_viettell_group', label: 'VN: Viettel Group' },
            { id: "other", value: 'other', label: 'VN: Other' },
        ]

        const element = document.getElementById('internetProviderChoices');


        const choices = new Choices(element, {
            shouldSort: false,
            allowHTML: true,
            removeItemButton: true,
            renderSelectedChoices: "auto",
            placeholder: true,
            searchEnabled: true,
            classNames: {
                containerOuter: "choices tiktok-custom",
            },
            choices: data,
        });
    });
</script>
}
