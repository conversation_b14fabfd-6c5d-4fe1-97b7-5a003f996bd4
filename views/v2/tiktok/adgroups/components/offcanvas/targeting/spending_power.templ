package targeting

templ AdGroupTargetingPendingPower() {
	<div class="" id="spendingPowerTargeting">
		<label for="spending-power-targeting" class="fw-bold">
			Spending Power
			<button
				class="btn p-0 "
				type="button"
				id="btn-clear-value-input-search-location"
				role="button"
				data-bs-toggle="popover"
				data-bs-trigger="focus"
				data-bs-html="true"
				data-bs-content="Choose audiences to include or exclude. You can also create custom audiences by uploading an Apple random device identifier (IDFA) and Google advertising identifier (GAID), or by creating a lookalike audience based on who you'd like to show your ads to."
			>
				<i class="ri-information-line"></i>
			</button>
		</label>
		<div class="spending-power-targeting selection-targeting d-flex align-items-center flex-wrap">
			<label class="selection-option">
				<input type="checkbox" name="spending_power" value="ALL" hidden checked="true"/>
				<span>All</span>
			</label>
			<label class="selection-option">
				<input type="checkbox" name="spending_power" value="HIGH" hidden/>
				<span>High spending power</span>
			</label>
		</div>
	</div>
	<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        const $container = $('#spendingPowerTargeting');
        const $checkboxes = $container.find('.selection-option input[type="checkbox"]');
        const $checkboxAll = $container.find('input[name="age"][value="all"]');

        $checkboxes.on('change', function () {
            const $this = $(this);
            const selectedValues = $checkboxes.filter(':checked').map(function () {
                return this.value;
            }).get();


            if ($this.val() === 'all') {
                $checkboxes.not($this).prop('checked', false);
            } else {
                $checkboxAll.prop('checked', selectedValues.length === 0);
            }
        });
    });
</script>
}
