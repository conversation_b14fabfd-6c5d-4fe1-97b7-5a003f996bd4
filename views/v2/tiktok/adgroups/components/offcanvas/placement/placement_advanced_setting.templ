package placement

templ PlacementAdvancedSetting() {
	<div class="accordion custom-accordionwithicon mt-3">
		<div class="accordion-item">
			<h2 class="accordion-header" id="placementAdvancedSettings">
				<button
					class="accordion-button p-2 collapsed"
					type="button"
					data-bs-toggle="collapse"
					data-bs-target="#placementAdvancedSettingsContent"
					aria-expanded="false"
					aria-controls="placementAdvancedSettingsContent"
				>
					<span class="avatar-title bg-soft-light fs-5 px-2 py-1" style="flex: 0 1 0%;">
						<i class=" ri-more-2-fill text-black-50"></i>
					</span>
					<span>Advanced settings</span>
				</button>
			</h2>
			<div
				id="placementAdvancedSettingsContent"
				class="accordion-collapse p-3 collapse show"
				aria-labelledby="heading-placementAdvancedSettingsContent"
			></div>
		</div>
	</div>
	<template id="placementAdvancedSettingsContent_REACH">
		@optionUserComment()
		@optionVideoDownload()
		@optionVideoShare()
	</template>
	<template id="placementAdvancedSettingsContent_TRAFFIC">
		@optionUserComment()
		@optionVideoDownload()
		@optionVideoShare()
	</template>
	<template id="placementAdvancedSettingsContent_PRODUCT_SALES">
		@optionUserComment()
		@optionVideoDownload()
	</template>
}

templ optionUserComment() {
	<!-- User comment -->
	<div class="form-check form-switch form-switch-lg ms-3">
		<input type="checkbox" class="form-check-input " name="user_comment" id="placementUserComment"/>
		<label class="form-check-label" for="placementUserComment">User comment</label>
	</div>
	<p class="text-muted mt-1">
		We recommend keeping user comments on to help your ads achieve more impressions and conversions. You can hide, pin, and
		reply to comments using our comment management tools.
	</p>
}

templ optionVideoDownload() {
	<!-- Allow Video Download -->
	<div class="form-check form-switch form-switch-lg ms-3 mt-2">
		<input
			type="checkbox"
			disabled
			class="form-check-input "
			name="allow_video_download"
			id="placementAllowVideoDownload"
			checked=""
		/>
		<label class="form-check-label" for="placementAllowVideoDownload">Allow video download</label>
	</div>
}

templ optionVideoShare() {
	<!-- Allow video share -->
	<div class="form-check form-switch form-switch-lg ms-3 mt-2">
		<input type="checkbox" class="form-check-input " name="allow_video_sharing" id="placementAllowVideoSharing" checked=""/>
		<label class="form-check-label" for="placementAllowVideoSharing">Allow video sharing</label>
	</div>
}

templ optionPangleBlockList() {
	<!-- Pangle block list -->
	<div class="form-check form-switch form-switch-lg ms-3 mt-2">
		<input type="checkbox" class="form-check-input " name="pangle_block_list" id="pangleBlockList"/>
		<label class="form-check-label" for="pangleBlockList">Pangle block list</label>
	</div>
	<p class="text-muted mt-1">
		To ensure brand safety, undesired media placements on Pangle can be added to block list to help prevent your ads from
		appearing. However, this will decrease the reach of your ads.
	</p>
}
