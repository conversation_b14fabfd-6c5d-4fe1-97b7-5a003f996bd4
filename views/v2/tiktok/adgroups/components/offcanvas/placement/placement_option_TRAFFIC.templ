package placement

import "github.com/dev-networldasia/dspgos/gos/templates"

templ PlacementOptionTRAFFIC() {
	<div class="" id="placementOptionTRAFFIC">
		<div class="d-flex flex-wrap flex-column">
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input class="form-check-input" type="radio" name="placement[]" value="automatic" id="automaticPlacement" checked="true"/>
				<label class="form-check-label mt-1" for="automaticPlacement">
					<p class="mb-0">Automatic placement</p>
					<p class="text-muted fw-normal mb-0">Automatically show your ads across supported placements. </p>
				</label>
			</div>
			<div class="form-check form-radio-primary mb-3 ms-3">
				<input class="form-check-input" type="radio" name="placement[]" id="manualPlacement" value="manual"/>
				<label class="form-check-label mt-1" for="manualPlacement">
					<p class="mb-0">High spending power</p>
					<p class="text-muted fw-normal mb-0">Manually choose your targeting placement</p>
				</label>
			</div>
			<div
				id="placementSelection"
				class="col-12  p-3 flex-column gap-3"
				style="background-color: rgba(218, 218, 218, 0.2); border-radius: 8px; display: none;"
			>
				<!-- Tiktok -->
				<div class="d-flex gap-4 selection-item align-items-center ps-0 mt-2">
					<input class="form-check-input ms-0" type="checkbox" name="placement[]" value="tiktok" checked="true"/>
					<img src={ templates.AssetURL("/static/img/tiktok_icon_2.png") } alt="tiktok logo" class="img-fluid" style="width: 60px; height: 60px;"/>
					<div class="d-flex flex-column gap-1  justify-content-center">
						<div class="fs-16">
							Tiktok
						</div>
						<div class="d-flex ps-0 gap-5">
							<div class="form-check form-switch form-switch-lg ms-3">
								<input
									type="checkbox"
									class="form-check-input "
									name="include_search_result"
									id="includeSearchResult"
									checked=""
								/>
								<label class="form-check-label text-muted" for="includeSearchResult">
									Include search results
									<button
										class="btn p-0 "
										type="button"
										role="button"
										data-bs-toggle="popover"
										data-bs-trigger="focus"
										data-bs-html="true"
										data-bs-content="Show your ads to users when they search for your business on TikTok"
									>
										<i class="ri-information-line"></i>
									</button>
								</label>
							</div>
						</div>
					</div>
				</div>
				<!-- Pangle -->
				<div class="d-flex gap-4 selection-item align-items-center ps-0 mt-2">
					<input class="form-check-input ms-0" type="checkbox" name="placement[]" value="global_app_bundle" checked="true"/>
					<img
						src={ templates.AssetURL("/static/img/global_app_bundle.png") }
						alt="global app logo"
						class="img-fluid"
						style="width: 60px; height: 60px;"
					/>
					<div class="d-flex flex-column gap-1 ">
						<div class="fs-16">
							Global App Bundle
							<button
								class="btn p-0 "
								type="button"
								role="button"
								data-bs-toggle="popover"
								data-bs-trigger="focus"
								data-bs-html="true"
								data-bs-content="A series of apps covering a wide variety of consumer interests and industries."
							>
								<span class="badge badge-soft-dark">
									Powered by Pangle
								</span>
							</button>
						</div>
						<div class="d-flex ps-0 gap-5">
							<p class="text-muted mb-0">
								CapCut/Fizzo
								<button
									class="btn p-0 "
									type="button"
									role="button"
									data-bs-toggle="popover"
									data-bs-trigger="focus"
									data-bs-html="true"
									data-bs-content="A series of apps covering a wide variety of consumer interests and industries."
								>
									<i class="ri-information-line"></i>
								</button>
							</p>
						</div>
					</div>
				</div>
				<!-- Golbal App Bundle -->
				<div class="d-flex gap-4 selection-item align-items-center ps-0 mt-2">
					<input class="form-check-input ms-0" type="checkbox" name="placement[]" value="global_app_bundle" checked="true"/>
					<img src={ templates.AssetURL("/static/img/pangle.png") } alt="global app logo" class="img-fluid" style="width: 60px; height: 60px;"/>
					<div class="d-flex flex-column gap-2">
						<div class="d-flex flex-column gap-1">
							<div class="fs-16">
								Pangle
							</div>
							<div class="d-flex ps-0 gap-5">
								<p class="text-muted mb-0">
									Premium global publisher network, available in some regions.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
