package placement

import "github.com/dev-networldasia/dspgos/gos/templates"

templ PlacementOptionREACH() {
	<div class="d-flex gap-4">
		<img src={ templates.AssetURL("/static/img/tiktok_icon_2.png") } alt="tiktok logo" class="img-fluid" style="width: 60px; height: 60px;"/>
		<div class="d-flex flex-column gap-2">
			<div class="fs-16">
				Tiktok
			</div>
			<div class="d-flex ps-2 gap-5">
				<div class="form-check">
					<input class="form-check-input" name="in_feed" disabled type="checkbox" id="placementInFeed" checked/>
					<label class="form-check-label mt-1" for="placementInFeed">
						In-feed
					</label>
				</div>
				<div class="form-check">
					<input class="form-check-input" name="search_feed" type="checkbox" id="placementSearchFeed" checked/>
					<label class="form-check-label mt-1" for="placementSearchFeed">
						Search feed
					</label>
				</div>
			</div>
		</div>
	</div>
}
