package components

import "godsp/views/v2/tiktok/adgroups/components/offcanvas"

templ OffcanvasEditAdgroupCpn() {
	@offcanvas.AdGroupHeaderOffcanvas()
	<!-- // Offcanvas content -->
	<form id="adgroupForm" class="tiktok-custom">
		<input type="hidden" id="outcomeObjective" value="PRODUCT_SALES"/>
		<div class="container d-flex flex-1">
			<div class="row flex-1">
				<!-- Main Container -->
				<div class="col-8 d-flex flex-column position-relative ">
					<div class=" container-input-control d-flex flex-column mt-3 flex-1 ">
						// <!-- Ad Group Name -->
						@offcanvas.AdGroupName()
						// <!-- Shop Ads Type -->
						@offcanvas.AdGroupShopAdsType()
						// <!-- placement -->
						@offcanvas.AdGroupPlacement()
						// <!-- Optimization Location -->
						@offcanvas.AdGroupOptimizationLocation()
						// <!-- Targeting -->
						@offcanvas.AdGroupTargeting()
						// <!-- Budget & Schedule -->
						@offcanvas.AdGroupBudgetSchedule()
						// <!-- Bidding Optimization -->
						@offcanvas.AdGroupBiddingOptimization()
					</div>
					<!-- // Footer Container -->
					<div class="form-footer p-3 bg-white border-top" id="">
						<div class="d-flex justify-content-between">
							<div class="d-flex flex-row gap-2 align-items-center">
								<div class="" id="alert-auto-save">
									<span class="d-none save-success text-success fs-16">
										Auto save success
										<i class="ri-check-double-line"></i>
									</span>
									<span class="d-none save-fail text-danger">
										Auto save fail
										<i class="ri-close-circle-line"></i>
									</span>
								</div>
							</div>
							<div>
								<button id="btn-save-form-off-canvas" type="button" class="btn btn-success waves-effect waves-light">
									Save
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- Hint Messages -->
				<div class="col-4 flex-1 mt-3 ">
					<div class="position-sticky" style="top:75px">
						// {{template "audience_float_info_right"}}
						@offcanvas.AdGroupFloatRightInfo()
					</div>
				</div>
			</div>
		</div>
	</form>
}
