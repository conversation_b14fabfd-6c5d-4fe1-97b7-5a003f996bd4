package layouts

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/views/v2/layouts/masters"

templ Master(data masters.LayoutMasterData, heads []templ.Component, scripts ...templ.Component) {
	<!DOCTYPE HTML>
	<html
		lang="en"
		data-layout="vertical"
		data-topbar="light"
		data-sidebar="light"
		data-sidebar-size="sm"
		data-sidebar-image="none"
		data-preloader="disable"
		data-theme="default"
		data-theme-colors="default"
	>
		<head>
			<meta charset="utf-8"/>
			<title>Tradding Desk - Networld Solutions</title>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<meta name="api-base-url" content={ templates.AssetURL("/") }/>
			<link rel="shortcut icon" href={ templates.AssetURL("/static/img/favicon.png") } type="image/x-icon"/>
			<script src={ templates.AssetURL("/static/themes/js/layout.js") }></script>
			<link href={ templates.AssetURL("/static/themes/css/bootstrap.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/themes/css/icons.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/themes/css/app.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/themes/libs/sweetalert2/sweetalert2.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/themes/libs/select2/select2.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/assets/libs/daterangepicker/daterangepicker.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/assets/libs/datatables/datatables.min.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/themes/css/custom-theme.css") } rel="stylesheet" type="text/css"/>
			<link href={ templates.AssetURL("/static/css/style.css") } rel="stylesheet" type="text/css"/>
			if len(heads) > 0 {
				for _, head := range heads {
					@head
				}
			}
		</head>
		<body>
			<div id="layout-wrapper">
				@masters.HeaderTopbar()
				@masters.Sidebar(data)
				<div class="vertical-overlay"></div>
				<div class="main-content">
					<div class="page-content">
						<div class="container-fluid">
							{ children... }
						</div>
					</div>
				</div>
				@masters.Footer()
			</div>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/js/jquery-3.7.0.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/assets/libs/axios/axios.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/assets/libs/moment/moment.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/bootstrap/js/bootstrap.bundle.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/simplebar/simplebar.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/node-waves/waves.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/feather-icons/feather.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/js/pages/plugins/lord-icon-2.1.0.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/sweetalert2/sweetalert2.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/choices.js/public/assets/scripts/choices.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/cleave/cleave.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/cleave/cleave.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/flatpickr/flatpickr.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/toastify-js/toastify.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/select2/select2.min.js") } defer></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/js/common/utils.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/daterangepicker/daterangepicker.min.js") }></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/themes/js/app.js") } defer></script>
			// Project JS
			<script type="text/javascript" src={ templates.AssetURL("/static/js/layout/topbar.js") } defer></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/assets/libs/axios/axios.min.js") } defer></script>
			<script type="text/javascript" src={ templates.AssetURL("/static/js/common/axios.js") } defer></script>
			if len(scripts) > 0 {
				for _, script := range scripts {
					@script
				}
			}
		</body>
	</html>
}
