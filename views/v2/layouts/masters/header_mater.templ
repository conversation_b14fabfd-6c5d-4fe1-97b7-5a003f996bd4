package masters

import "github.com/dev-networldasia/dspgos/gos/templates"

templ HeaderTopbar() {
	@topbar()
	@removeNotificationModal()
}

templ topbar() {
	<header id="page-topbar">
		<div class="layout-width">
			<div class="navbar-header">
				<div class="d-flex">
					@logo()
				</div>
				<div class="d-flex align-items-center">
					@userProfile()
				</div>
			</div>
		</div>
	</header>
}

templ logo() {
	<div class="navbar-brand-box horizontal-logo">
		<a href="/" class="logo logo-dark">
			<span class="logo-sm">
				<img src={ templates.AssetURL("/static/themes/images/logo-sm.png") } alt="" height="22"/>
			</span>
			<span class="logo-lg">
				<img src={ templates.AssetURL("/static/themes/images/logo-dark.png") } alt="" height="17"/>
			</span>
		</a>
		<a href="/" class="logo logo-light">
			<span class="logo-sm">
				<img src={ templates.AssetURL("/static/themes/images/logo-sm.png") } alt="" height="22"/>
			</span>
			<span class="logo-lg">
				<img src={ templates.AssetURL("/static/themes/images/logo-light.png") } alt="" height="17"/>
			</span>
		</a>
	</div>
	<button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger" id="topnav-hamburger-icon">
		<span class="hamburger-icon">
			<span></span>
			<span></span>
			<span></span>
		</span>
	</button>
}

templ userProfile() {
	<div class="dropdown ms-sm-3 header-item topbar-user rounded" id="topbarUserProfile">
		<button
			type="button"
			class="btn "
			id="page-header-user-dropdown"
			data-bs-toggle="dropdown"
			aria-haspopup="true"
			aria-expanded="false"
		>
			<span class="d-flex align-items-center">
				<img id="topbarUserProfileAvatar" class="rounded-circle header-profile-user" src=""/>
				<span class="text-start ms-xl-2">
					<span class="d-none d-xl-inline-block ms-1 fw-medium user-name-text"></span>
				</span>
			</span>
		</button>
		<div class="dropdown-menu dropdown-menu-end ">
			<h6 class="dropdown-header" data-key="t-welcome">Welcome, <span class="text-info" id="topbarUserProfileName"></span></h6>
			<h6 class="dropdown-header" data-key="t-welcome">Email: <span class="text-info" id="topbarUserProfileEmail"></span></h6>
			<div class="dropdown-divider"></div>
			<a class="dropdown-item" id="topbarLogout" data-href={ templates.SafeURL("/logout") }>
				<i class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i>
				<span class="align-middle" data-key="t-logout">Logout</span>
			</a>
		</div>
	</div>
}

templ removeNotificationModal() {
	<div id="removeNotificationModal" class="modal fade zoomIn" tabindex="-1" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="NotificationModalbtn-close"></button>
				</div>
				<div class="modal-body">
					<div class="mt-2 text-center">
						<lord-icon
							src="https://cdn.lordicon.com/gsqxdxog.json"
							trigger="loop"
							colors="primary:#f7b84b,secondary:#f06548"
							style="width:100px;height:100px"
						></lord-icon>
						<div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
							<h4>Are you sure ?</h4>
							<p class="text-muted mx-4 mb-0">Are you sure you want to remove this Notification ?</p>
						</div>
					</div>
					<div class="d-flex gap-2 justify-content-center mt-4 mb-2">
						<button type="button" class="btn w-sm btn-light" data-bs-dismiss="modal">Close</button>
						<button type="button" class="btn w-sm btn-danger" id="delete-notification">Yes, Delete It!</button>
					</div>
				</div>
			</div>
		</div>
	</div>
}
