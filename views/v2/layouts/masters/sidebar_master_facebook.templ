package masters

import (
	"godsp/modules/facebook/iface"
)

templ SidebarMaster(userInfo iface.UserInfoFb, authPermission map[string]int) {
	@SidebarFacebook(LayoutMasterDataFacebook{
		AuthPermission: authPermission,
		UserInfo:       userInfo,
	})
}

templ SidebarFacebook(data LayoutMasterDataFacebook) {
	<div class="app-menu navbar-menu">
		@nabarLogo()
		@menusFacebook(data)
		<div class="sidebar-background"></div>
	</div>
}

templ menusFacebook(data LayoutMasterDataFacebook) {
	@styleSibarMenu()
	<div id="scrollbar" class="h-100">
		<div class="container-fluid">
			<div id="two-column-menu"></div>
			<ul class="navbar-nav navbar-nav-custom" id="navbar-nav">
				@menuItemFacebookMain(data)
			</ul>
		</div>
	</div>
}

templ menuItemFacebookMain(data LayoutMasterDataFacebook) {
	<li class="nav-item">
		<a
			class="nav-link menu-link"
			href="#sidebarFacebook"
			data-bs-toggle="collapse"
			role="button"
			aria-expanded="true"
			aria-controls="sidebarFacebook"
			style="height: 48px;"
		>
			<img
				src="/static/img/meta_icon.svg"
				width="22px"
				style="margin-right: 3px;-: 2px;min-width: 1.5em;margin-left: -2px;"
			/>
			<span class="item-text">Facebook Ads</span>
		</a>
		<div class="menu-dropdown collapse show" id="sidebarFacebook">
			<ul class="nav nav-sm flex-column">
				<li class="nav-item">
					<a href="/dsp/facebook/campaigns/list" class="nav-link">
						<i class="ri-megaphone-line"></i>
						<span>Campaigns</span>
					</a>
				</li>
				<li class="nav-item">
					<a href="/dsp/facebook/adsets/list" class="nav-link">
						<i class="ri-stack-line"></i>
						<span>Ad Sets</span>
					</a>
				</li>
				<li class="nav-item">
					<a href="/dsp/facebook/ads/list" class="nav-link">
						<i class="ri-advertisement-line"></i>
						<span>Ads</span>
					</a>
				</li>
				<li class="nav-item">
					<a href="/dsp/facebook/ad-accounts/list" class="nav-link">
						<i class="ri-account-box-line"></i>
						<span>Ad Accounts</span>
					</a>
				</li>
			</ul>
		</div>
	</li>
}
