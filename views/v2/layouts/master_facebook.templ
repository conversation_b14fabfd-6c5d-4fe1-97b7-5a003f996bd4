package layouts

import "github.com/dev-networldasia/dspgos/gos/templates"
import "godsp/views/v2/layouts/masters"

templ MasterFacebook(data masters.LayoutMasterDataFacebook, heads []templ.Component, scripts ...templ.Component) {
	<!DOCTYPE HTML>
	<html
		lang="en"
		data-layout="vertical"
		data-topbar="light"
		data-sidebar="light"
		data-sidebar-size="sm"
		data-sidebar-image="none"
		data-preloader="disable"
		data-theme="default"
		data-theme-colors="default"
	>
		<head>
			<meta charset="utf-8"/>
			<title>Facebook Ads Manager | DSP</title>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<meta content="Facebook Ads Manager" name="description"/>
			<meta content="DSP" name="author"/>
			<!-- App favicon -->
			<link rel="shortcut icon" href={ templates.AssetURL("/static/themes/images/favicon.ico") }/>
			<!-- Layout config Js -->
			<script src={ templates.AssetURL("/static/themes/js/layout.js") }></script>
			<!-- Bootstrap Css -->
			<link href={ templates.AssetURL("/static/themes/css/bootstrap.min.css") } rel="stylesheet" type="text/css"/>
			<!-- Icons Css -->
			<link href={ templates.AssetURL("/static/themes/css/icons.min.css") } rel="stylesheet" type="text/css"/>
			<!-- App Css-->
			<link href={ templates.AssetURL("/static/themes/css/app.min.css") } rel="stylesheet" type="text/css"/>
			<!-- custom Css-->
			<link href={ templates.AssetURL("/static/themes/css/custom.min.css") } rel="stylesheet" type="text/css"/>
			<!-- Common CSS -->
			<link href={ templates.AssetURL("/static/css/common.css") } rel="stylesheet" type="text/css"/>
			<!-- Facebook specific CSS -->
			<link href={ templates.AssetURL("/static/css/facebook.css") } rel="stylesheet" type="text/css"/>
			for _, head := range heads {
				@head
			}
		</head>
		<body>
			<!-- Begin page -->
			<div id="layout-wrapper">
				@masters.HeaderMaster(data.UserInfo, data.AuthPermission)
				<!-- ========== App Menu ========== -->
				@masters.SidebarMaster(data.UserInfo, data.AuthPermission)
				<!-- Left Sidebar End -->
				<!-- Vertical Overlay-->
				<div class="vertical-overlay"></div>
				<!-- ============================================================== -->
				<!-- Start right Content here -->
				<!-- ============================================================== -->
				<div class="main-content">
					<div class="page-content">
						<div class="container-fluid">
							{ children... }
						</div>
						<!-- container-fluid -->
					</div>
					<!-- End Page-content -->
					@masters.FooterMaster()
				</div>
				<!-- end main content-->
			</div>
			<!-- END layout-wrapper -->
			<!--start back-to-top-->
			<button onclick="topFunction()" class="btn btn-danger btn-icon" id="back-to-top">
				<i class="ri-arrow-up-line"></i>
			</button>
			<!--end back-to-top-->
			<!--preloader-->
			<div id="preloader">
				<div id="status">
					<div class="spinner-border text-primary avatar-sm" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			</div>
			<!-- JAVASCRIPT -->
			<script src={ templates.AssetURL("/static/themes/libs/bootstrap/js/bootstrap.bundle.min.js") }></script>
			<script src={ templates.AssetURL("/static/themes/libs/simplebar/simplebar.min.js") }></script>
			<script src={ templates.AssetURL("/static/themes/libs/node-waves/waves.min.js") }></script>
			<script src={ templates.AssetURL("/static/themes/libs/feather-icons/feather.min.js") }></script>
			<script src={ templates.AssetURL("/static/themes/js/pages/plugins/lord-icon-2.1.0.js") }></script>
			<script src={ templates.AssetURL("/static/themes/js/plugins.js") }></script>
			<!-- App js -->
			<script src={ templates.AssetURL("/static/themes/js/app.js") }></script>
			<!-- Common JS -->
			<script src={ templates.AssetURL("/static/js/common/common.js") }></script>
			<script src={ templates.AssetURL("/static/js/common/helpers.js") }></script>
			<script src={ templates.AssetURL("/static/js/common/httpService.js") }></script>
			<!-- jQuery -->
			<script src={ templates.AssetURL("/static/themes/libs/jquery/jquery.min.js") }></script>
			for _, script := range scripts {
				@script
			}
		</body>
	</html>
}
