package components

templ CustomColumnTableModalContent() {
	<div class="row">
		<div class="col-md-4">
			<div class="card">
				<div class="card-header">
					<h5 class="card-title mb-0">Available Columns</h5>
					<div class="mt-2">
						<input type="text" class="form-control" id="searchColumns" placeholder="Search columns..."/>
					</div>
					<ul class="nav nav-tabs mt-2" id="columnTypeTabs" role="tablist">
						<li class="nav-item" role="presentation">
							<button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all-columns" type="button" role="tab">All</button>
						</li>
						<li class="nav-item" role="presentation">
							<button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance-columns" type="button" role="tab">Performance</button>
						</li>
						<li class="nav-item" role="presentation">
							<button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings-columns" type="button" role="tab">Settings</button>
						</li>
						<li class="nav-item" role="presentation">
							<button class="nav-link" id="custom-tab" data-bs-toggle="tab" data-bs-target="#custom-columns" type="button" role="tab">Custom</button>
						</li>
					</ul>
				</div>
				<div class="card-body">
					<div class="tab-content" id="columnTypeContent">
						<div class="tab-pane fade show active" id="all-columns" role="tabpanel">
							<div class="available-columns-list" id="allColumnsList">
								<!-- All columns will be populated here -->
								<div class="column-item" data-column-key="impressions">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="impressions" id="column-impressions"/>
										<label class="form-check-label" for="column-impressions">
											Impressions
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="clicks">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="clicks" id="column-clicks"/>
										<label class="form-check-label" for="column-clicks">
											Clicks
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="ctr">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="ctr" id="column-ctr"/>
										<label class="form-check-label" for="column-ctr">
											CTR
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="spend">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="spend" id="column-spend"/>
										<label class="form-check-label" for="column-spend">
											Spend
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="cpc">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="cpc" id="column-cpc"/>
										<label class="form-check-label" for="column-cpc">
											CPC
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="cpm">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="cpm" id="column-cpm"/>
										<label class="form-check-label" for="column-cpm">
											CPM
										</label>
									</div>
								</div>
							</div>
						</div>
						<div class="tab-pane fade" id="performance-columns" role="tabpanel">
							<div class="available-columns-list" id="performanceColumnsList">
								<!-- Performance columns will be populated here -->
								<div class="column-item" data-column-key="impressions">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="impressions" id="perf-impressions"/>
										<label class="form-check-label" for="perf-impressions">
											Impressions
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="clicks">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="clicks" id="perf-clicks"/>
										<label class="form-check-label" for="perf-clicks">
											Clicks
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="ctr">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="ctr" id="perf-ctr"/>
										<label class="form-check-label" for="perf-ctr">
											CTR
										</label>
									</div>
								</div>
							</div>
						</div>
						<div class="tab-pane fade" id="settings-columns" role="tabpanel">
							<div class="available-columns-list" id="settingsColumnsList">
								<!-- Settings columns will be populated here -->
								<div class="column-item" data-column-key="status">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="status" id="settings-status"/>
										<label class="form-check-label" for="settings-status">
											Status
										</label>
									</div>
								</div>
								<div class="column-item" data-column-key="objective">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="objective" id="settings-objective"/>
										<label class="form-check-label" for="settings-objective">
											Objective
										</label>
									</div>
								</div>
							</div>
						</div>
						<div class="tab-pane fade" id="custom-columns" role="tabpanel">
							<div class="available-columns-list" id="customColumnsList">
								<!-- Custom columns will be populated here -->
								<div class="d-flex justify-content-center mb-3">
									<button type="button" class="btn btn-sm btn-primary" id="createCustomMetricBtn">
										<i class="ri-add-line"></i> Create Custom Metric
									</button>
								</div>
								<div class="column-item" data-column-key="custom_roas">
									<div class="form-check">
										<input class="form-check-input column-checkbox" type="checkbox" value="custom_roas" id="custom-roas"/>
										<label class="form-check-label" for="custom-roas">
											ROAS (Custom)
										</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-md-8">
			<div class="card">
				<div class="card-header d-flex justify-content-between align-items-center">
					<h5 class="card-title mb-0">Selected Columns</h5>
					<div>
						<div class="dropdown">
							<button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="presetDropdown" data-bs-toggle="dropdown" aria-expanded="false">
								Presets
							</button>
							<ul class="dropdown-menu" aria-labelledby="presetDropdown">
								<li><a class="dropdown-item" href="#" data-preset="performance">Performance</a></li>
								<li><a class="dropdown-item" href="#" data-preset="engagement">Engagement</a></li>
								<li><a class="dropdown-item" href="#" data-preset="conversion">Conversion</a></li>
								<li><hr class="dropdown-divider"/></li>
								<li><a class="dropdown-item" href="#" id="saveAsPresetBtn">Save as new preset...</a></li>
							</ul>
						</div>
					</div>
				</div>
				<div class="card-body">
					<div class="alert alert-info">
						<i class="ri-information-line me-1"></i> Drag and drop columns to reorder them. Required columns cannot be removed.
					</div>
					<div class="selected-columns-container">
						<ul class="list-group" id="selectedColumnsList">
							<!-- Selected columns will be populated here -->
							<li class="list-group-item d-flex justify-content-between align-items-center" data-column-key="status">
								<div>
									<i class="ri-drag-move-2-fill me-2 text-muted"></i>
									<span>Status</span>
									<span class="badge bg-secondary ms-2">Required</span>
								</div>
							</li>
							<li class="list-group-item d-flex justify-content-between align-items-center" data-column-key="campaign_name">
								<div>
									<i class="ri-drag-move-2-fill me-2 text-muted"></i>
									<span>Campaign Name</span>
									<span class="badge bg-secondary ms-2">Required</span>
								</div>
							</li>
							<li class="list-group-item d-flex justify-content-between align-items-center" data-column-key="objective">
								<div>
									<i class="ri-drag-move-2-fill me-2 text-muted"></i>
									<span>Objective</span>
								</div>
								<button type="button" class="btn btn-sm btn-outline-danger remove-column" data-column-key="objective">
									<i class="ri-close-line"></i>
								</button>
							</li>
							<li class="list-group-item d-flex justify-content-between align-items-center" data-column-key="impressions">
								<div>
									<i class="ri-drag-move-2-fill me-2 text-muted"></i>
									<span>Impressions</span>
								</div>
								<button type="button" class="btn btn-sm btn-outline-danger remove-column" data-column-key="impressions">
									<i class="ri-close-line"></i>
								</button>
							</li>
							<li class="list-group-item d-flex justify-content-between align-items-center" data-column-key="clicks">
								<div>
									<i class="ri-drag-move-2-fill me-2 text-muted"></i>
									<span>Clicks</span>
								</div>
								<button type="button" class="btn btn-sm btn-outline-danger remove-column" data-column-key="clicks">
									<i class="ri-close-line"></i>
								</button>
							</li>
							<li class="list-group-item d-flex justify-content-between align-items-center" data-column-key="spend">
								<div>
									<i class="ri-drag-move-2-fill me-2 text-muted"></i>
									<span>Spend</span>
								</div>
								<button type="button" class="btn btn-sm btn-outline-danger remove-column" data-column-key="spend">
									<i class="ri-close-line"></i>
								</button>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
}
