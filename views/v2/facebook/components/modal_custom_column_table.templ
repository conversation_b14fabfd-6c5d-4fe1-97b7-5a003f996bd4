package components

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
)

templ ModalCustomColumnTable() {
	<!-- Bootstrap Modal -->
	<div class="modal fade zoomIn" id="customColumnModal" tabindex="-1" aria-labelledby="customColumnModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="customColumnModalLabel">Customize Columns</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body border my-3 py-0 px-2">
					<div class="row" id="customColumnContent"></div>
				</div>
				<div class="modal-footer">
					<div class="flex-1 d-flex p-0 m-0 gap-2 align-items-end">
						<div class="form-check form-switch form-check-inline ps-0 mb-2 d-flex gap-2 align-items-center">
							<input
								class="form-check-input"
								type="checkbox"
								role="switch"
								id="switchToggleSaveNewPresetColumTable"
							/>
							<label class="form-check-label" for="switchToggleSaveNewPresetColumTable">
								Save as new preset
							</label>
						</div>
						<div class="d-none" id="footerControlPresetForm">
							<div class="d-flex gap-2 align-items-end">
								<div class="form-group">
									<label class="mb-0" for="footerNamePreset">Name</label>
									<input
										data-id=""
										type="text"
										name="namePreset"
										id="footerNamePreset"
										class="form-control"
										placeholder="Please enter name"
									/>
								</div>
								<div class="form-group">
									<label class="mb-0" for="footerNamePrivatePreset">Name private</label>
									<input
										data-id=""
										type="text"
										name="namePrivatePreset"
										id="footerNamePrivatePreset"
										class="form-control"
										placeholder="Please enter name private"
									/>
								</div>
								<button class="btn btn-danger waves-effect" id="cancleEditOrCreatePreset">Cancel</button>
							</div>
						</div>
					</div>
					<div class="">
						<button class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
						<button class="btn btn-success" id="saveColumns">Save</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Custom Metric Column Modal -->
	@ModalCustomMetricColumnTable()
	<!-- Custom Column Table Script -->
	@scriptCustomColumnTable()
	@ModalDeleteItems()
}

templ ModalCustomMetricColumnTable() {
	<!-- Bootstrap Modal -->
	<div class="modal fade zoomIn" id="customMetricColumnModal" tabindex="-1" aria-labelledby="customMetricColumnModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="customMetricColumnModalLabel">Custom Metric Column</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="customMetricColumnContent"></div>
				</div>
				<div class="modal-footer">
					<button class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
					<button class="btn btn-success" id="saveCustomMetricBtn">Save</button>
				</div>
			</div>
		</div>
	</div>
}

templ ModalDeleteItems() {
	<!-- Modal for delete confirmation -->
	<div class="modal fade zoomIn" id="modalRemoveItem" tabindex="-1" aria-labelledby="modalRemoveItemLabel" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="modalRemoveItemLabel">Confirm Delete</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<p>Are you sure you want to delete this item? This action cannot be undone.</p>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
					<button type="button" class="btn btn-danger" id="btnConfirmRemove">Delete</button>
				</div>
			</div>
		</div>
	</div>
}

templ scriptCustomColumnTable() {
	<script type="module" defer>
	import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
	import { requestApi } from "/static/js/common/httpService.js";

	/**
	 * Get custom metric modal content HTML
	 */
	function getHtmlContentCustomColumnTable() {
		const noResultHTML = `<div class="no-results p-4"><img src="/static/images/no-data.png" alt="No Results" style="max-width: 250px; margin-bottom: 10px;"><p>Empty Content</p></div>`;
		$('#customColumnContent').empty();
		$('#customMetricColumnContent').empty();
		loader();
		requestApi("POST", "/dsp/facebook/api/custom-column-table/custom-modal")
			.then((response) => {
				if (response?.modal_custom_column_html && response?.modal_custom_metric_column_html) {
					$('#customColumnContent').html(response.modal_custom_column_html);
					$('#customMetricColumnContent').html(response.modal_custom_metric_column_html);
				} else {
					$('#customColumnContent').html(noResultHTML);
				}
			})
			.catch((error) => {
				console.error("Error fetching custom column content:", error);
				$('#customColumnContent').html(noResultHTML);
			})
			.finally(() => {
				loader(false);
			});
	}

	function removeEventListeners() {
		$('#customColumnModal #customColumnContent').off().empty();
		$('#customColumnModal').find("*").addBack().filter('#saveColumns, #switchToggleSaveNewPresetColumTable, #cancleEditOrCreatePreset, input[name="namePreset"], input[name="namePrivatePreset"]').off();

		$('#customMetricColumnModal #customMetricColumnContent').off().empty();
		$('#customMetricColumnModal #saveCustomMetricBtn').off();

		$('#modalRemoveItem #btnConfirmRemove').off();
	}

	$(function () {
		$('#customColumnModal').on('show.bs.modal', function (e) {
			getHtmlContentCustomColumnTable();
		});

		$('#customColumnModal').on('hidden.bs.modal', function () {
			console.log("Modal closed");
			removeEventListeners();
		});
	});

	</script>
}
