package components

templ ModalCustomColumnTable() {
	<div class="modal fade" id="customColumnModal" tabindex="-1" aria-labelledby="customColumnModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="customColumnModalLabel">Customize Columns</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body" id="customColumnModalBody">
					<div class="d-flex justify-content-center">
						<div class="spinner-border" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
					<button type="button" class="btn btn-primary" id="saveCustomColumns">Save Changes</button>
				</div>
			</div>
		</div>
	</div>
}
