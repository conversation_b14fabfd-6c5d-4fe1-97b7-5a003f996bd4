package tableAds

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	cop "godsp/views/v2/facebook/components"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"

	"godsp/views/v2/facebook/tableAds/components"
)

func getDataLayoutMaster(data *ListTableAdsLayoutData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}
func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "Adsets", DataKey: "fb-adsets", Url: "/dsp/facebook/adsets/list"},
		{Title: "List", DataKey: "fb-adsets", Url: "/dsp/facebook/adsets/list#"},
	}
}

func getDataLayoutTable(data *ListTableAdsLayoutData) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission:     data.AuthPermission,
		UserInfo:           data.UserInfo,
		Clients:            data.Clients,
		AdAccounts:         data.AdAccounts,
		PresetsColumnTable: data.PresetsColumnTable,
	}
}

templ ListDatatableAds(data *ListTableAdsLayoutData) {
	{{
dataLayoutMaster := getDataLayoutMaster(data)
// pathBreadcrumb := getPathBreadcrumb()
dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{cssHeader()}, scriptAdGroup(), leftMenuScript()) {
		// @layoutCops.ListBreadcrumdCpn("Ad Groups", pathBreadcrumb)
		if data != nil && data.FlashMsg != "" {
			@cop.FlashMsgCop(data.FlashMsg)
		}
		@components.Table(dataLayoutTable)
		@cop.OffCanvasCpn()
		@scriptAdGroup()
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/css/tiktok.css") } rel="stylesheet" type="text/css"/>
}

templ scriptAdGroup() {
	<script type="text/javascript" src={ templates.AssetURL("/static/assets/libs/datatables/datatables.min.js") } defer></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/themes/libs/mathjs/math.min.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/router/navigo.min.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/list-table-ads/index.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/campaigns/list_table_campaigns.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/ads/list_table_ads.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/adgroups/list_table_adgroups.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/router/index.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/adgroups/offcanvas.js") } defer></script>
	<script>
	document.addEventListener("DOMContentLoaded", function () {
		numberFormat(".number-format");
	});
</script>
}

templ leftMenuScript() {
	<script type="module">
	import { successAlert, errorAlert, loader, getCookie } from "/static/js/common/helpers.js";
	import { initAdOffCanvasCpn } from "/static/js/facebook/ads/off-canvas/index.js";
	import renderAdgroupDataModule from "/static/js/facebook/adgroups/render_content_adgroup.js";
	import adgroupOffCanvasModule from "/static/js/facebook/adgroups/off-canvas/index.js";

	function resetUrl({ campaignID = "", adgroupID = "", adID = "" }) {
		removeParamURL("edit_campaign_id");
		removeParamURL("edit_adgroup_id");
		removeParamURL("edit_ad_id");

		if (campaignID) setParamURL("edit_campaign_id", campaignID);
		if (adgroupID) setParamURL("edit_adgroup_id", adgroupID);
		if (adID) setParamURL("edit_ad_id", adID);
	}

	async function handleCampaignClick(campaignId, $el) {
		resetUrl({ campaignID: campaignId });
		try {
			loader();
			// const response = await fetch("/dsp/facebook/api/campaign/edit", {
			const response = await fetch("/dsp/facebook/api/campaign/campaign-edit", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Accept: "application/json",
					"X-CSRF-Token": getCookie("csrf_"), // Hàm lấy CSRF token
				},
				body: JSON.stringify({ campaign_id: campaignId }),
			});

			if (!response.ok) throw new Error("Lỗi khi fetch campaign");

			const res = await response.json();
			document.getElementById("tiktok-offcanvas-content").innerHTML = res.html;
			$("#tiktok-offcanvas-content").find("input, select").prop("disabled", true);

			$(".nav-item-blk.active").removeClass("active");
			$el.addClass("active");
		} catch (error) {
			console.error("❌ Không thể tải dữ liệu chiến dịch:", error);
		} finally {
			loader(false);
		}
	}

	async function handleAdgroupClick(adgroupId, $el) {
		resetUrl({ adgroupID: adgroupId });

		try {
			loader();
			const response = await fetch("/dsp/facebook/api/adgroup/get-details", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Accept: "application/json",
					"X-CSRF-Token": getCookie("csrf_"),
				},
				body: JSON.stringify({
					adgroup_id: adgroupId,
					// advertiser_id: "7015148626926141442",
				}),
			});

			if (!response.ok) throw new Error("Lỗi khi fetch adgroup");

			const res = await response.json();
			// console.log("📦 Adgroup data:", res);
			document.getElementById("tiktok-offcanvas-content").innerHTML = res.data.html;
			adgroupOffCanvasModule.init();
			renderAdgroupDataModule.renderOffCanvasContent(res.data.data);

			$(".nav-item-blk.active").removeClass("active");
			$el.addClass("active");
		} catch (error) {
			console.error("❌ Không thể tải dữ liệu adgroup:", error);
		} finally {
			loader(false);
		}
	}

	async function handleAdClick(adId, $el) {
		resetUrl({ adID: adId });

		try {
			loader();
			const response = await fetch("/dsp/facebook/api/ad/edit", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Accept: "application/json",
					"X-CSRF-Token": getCookie("csrf_"),
				},
				body: JSON.stringify({ ad_id: adId }),
			});

			if (!response.ok) throw new Error("Lỗi khi fetch ad");

			const res = await response.json();
			document.getElementById("tiktok-offcanvas-content").innerHTML = res.html;
			initAdOffCanvasCpn({ ad: res.data });

			$(".nav-item-blk.active").removeClass("active");
			$el.addClass("active");
		} catch (error) {
			console.error("❌ Không thể tải dữ liệu quảng cáo:", error);
		} finally {
			loader(false);
		}
	}

	function setupLeftMenuListeners() {
		// Campaign level
		$(document).on("click", ".nav-lv1 > .nav-item-blk", function (e) {
			e.stopPropagation();
			const $el = $(this);
			if ($el.hasClass("active")) return;

			const campaignId = String($el.data("id"));
			if (!campaignId) return console.error("Missing campaign id");

			handleCampaignClick(campaignId, $el);
		});

		// Adgroup level
		$(document).on("click", ".nav-lv2 > .nav-item-blk", function (e) {
			e.stopPropagation();
			const $el = $(this);
			if ($el.hasClass("active")) return;

			const adgroupId = String($el.data("id"));
			if (!adgroupId) return console.error("Missing adgroup id");

			handleAdgroupClick(adgroupId, $el);
		});

		// Ad level
		$(document).on("click", ".nav-lv3 > .nav-item-blk", function (e) {
			e.stopPropagation();
			const $el = $(this);
			if ($el.hasClass("active")) return;

			const adId = String($el.data("id"));
			if (!adId) return console.error("Missing ad id");

			handleAdClick(adId, $el);
		});
	}

	document.addEventListener("DOMContentLoaded", () => {
		setupLeftMenuListeners();
	});
</script>
}
