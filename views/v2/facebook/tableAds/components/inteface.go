package components

import (
	clientE "godsp/modules/admin/client/entity"
	adAccountE "godsp/modules/facebook/ad_account/transport/response"
	cusColRes "godsp/modules/facebook/custom_column_table/transport/responses"
	"godsp/modules/facebook/iface"
)

type LayoutHeaderTableData struct {
	AuthPermission     map[string]int
	UserInfo           iface.UserInfoAuth
	Clients            []*clientE.ClientEntity
	AdAccounts         []*adAccountE.AdAccountEditClient
	PresetsColumnTable []*cusColRes.PresetColumnResp `json:"presets_column_table"`
}

type LayoutTableData struct {
	AuthPermission     map[string]int
	UserInfo           iface.UserInfoAuth
	Clients            []*clientE.ClientEntity
	AdAccounts         []*adAccountE.AdAccountEditClient
	PresetsColumnTable []*cusColRes.PresetColumnResp `json:"presets_column_table"`
}
