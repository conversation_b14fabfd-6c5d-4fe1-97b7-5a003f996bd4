package components

import ()

templ DateRangeFilterTable() {
	<div
		id="filter-search-datetime"
		class="align-items-center gap-2"
		style="min-width: 220px; white-space: nowrap; cursor: pointer;"
		class="input-group input-group-sm"
		style="margin-top: -5px; margin-bottom: -5px;"
	>
		<i class=" ri-calendar-2-line align-middle ttext-success fs-18"></i>&nbsp <span></span> <i class="ri-arrow-down-s-fill"></i>
	</div>
	@scriptDateRange()
}

templ scriptDateRange() {
	<script type="module" defer>
		import { DATETIME_FORMAT_TYPES } from "/static/js/constants/common-constant.js" ;
		const rangesDatePicker = {
			Today: [moment(), moment()],
			Yesterday: [moment().subtract(1, "days"), moment().subtract(1, "days")],
			"Last 7 days": [moment().subtract(6, "days"), moment()],
			"Last 14 days": [moment().subtract(13, "days"), moment()],
			"Last 30 days": [moment().subtract(29, "days"), moment()],
			"This week": [moment().startOf("week"), moment()],
			"This month": [moment().startOf("month"), moment()],
			"Last month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")],
		}

		function updateDate(start, end, label) {
			$("#filter-search-datetime span").html(start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY"))
			removeParamURL("page");
			document.dispatchEvent(
					new CustomEvent("onUpdateFilterChange", {
						detail: {
							start: start.format(DATETIME_FORMAT_TYPES.UTC),
							end: end.format(DATETIME_FORMAT_TYPES.UTC),
							label: label,
						},
					})
			);
		}

		function initDatePicker() {
			updateDate(moment().subtract(360, "days"), moment().subtract(1, "days"));
			$("#filter-search-datetime").daterangepicker(
					{
						timePicker: true,
						timePicker24Hour: true,
						ranges: rangesDatePicker,
						locale: {
							direction: "ltr",
							format: "MM/DD/YYYY HH:mm",
							separator: " - ",
							applyLabel: "Apply",
							cancelLabel: "Cancel",
							fromLabel: "From",
							toLabel: "To",
							customRangeLabel: "Custom",
							daysOfWeek: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
							monthNames: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
							firstDay: 1,
						},
						alwaysShowCalendars: true,
						startDate: moment().subtract(360, "days").format("MM/DD/YYYY"),
						endDate: moment().subtract(1, "days").format("MM/DD/YYYY"),
					},
					updateDate
			);
		}

		$().ready(function(){
			initDatePicker();
		})
	</script>
}
