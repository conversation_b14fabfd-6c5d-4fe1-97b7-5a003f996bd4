package components

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	clientE "godsp/modules/admin/client/entity"
	adAccountE "godsp/modules/facebook/ad_account/transport/response"
)

templ HeaderTableLeft(data LayoutHeaderTableData) {
	<div class="header-table-left d-flex flex-wrap align-items-center gap-2" id="listTableLeftHeader">
		<button
			id="btnCreateAd"
			type="button"
			disabled
			class="btn btn-info rounded btn-sm btn-animation waves-effect waves-light d-flex align-items-center gap-1"
		>
			<i class="ri-add-fill align-middle fs-16"></i> Create
		</button>
		// <button class="btn btn-light btn-sm bg-gradient waves-effect" id="delete-item-btn" data-bs-target="" data-bs-toggle="modal">
		// 	<i class="ri-delete-bin-line label-icon align-middle fs-16"></i>
		// </button>
		// {{if hasPermission $authPermission "admins/clients/edit-GET"}}
		if data.AuthPermission == nil {
			// <span class="mx-2">|</span>
			<div class="d-flex gap-2" id="LTLHAdminControlEl">
				<div class="d-flex gap-2 align-items-center fs-13" style="position: relative;z-index: 5;min-width: 370px;">
					<label class="fw-medium text-tiktok m-0">Client:</label>
					<div class="flex-1">
						<!-- <select class="flex-1" name="listTableSearchUser" id="LTLHAdminControlUserList">
                </select> -->
						<!-- Page select -->
						<select name="clientI" class="form-select" id="clientsChoices" aria-label="Default select example">
							<option value="" data-label="--- Choose Client ---"></option>
							@ListOptionClient(data.Clients)
						</select>
					</div>
				</div>
			</div>
			<div class="d-flex gap-2" id="LTLHAdminControlEl">
				<div class="d-flex gap-2 align-items-center fs-13" style="position: relative;z-index: 5;min-width: 370px;">
					<label class="fw-medium text-tiktok m-0">Advertiser:</label>
					<div class="flex-1">
						<select name="advertiser" class="form-select" id="advertiserChoices" aria-label="Default select example">
							<option value="" disabled selected>— Choose Advertiser —</option>
							@ListOptionAdvertiser(data.AdAccounts)
						</select>
					</div>
				</div>
				// <button
				// 	id="LTLHAdminControlBtnApprove"
				// 	type="button"
				// 	class="btn btn-success rounded btn-sm btn-animation waves-effect waves-light align-items-center gap-1"
				// 	data-bs-toggle="modal"
				// 	data-bs-target="#modal_confirm_approve_camp"
				// >
				// 	<i class="ri-check-line label-icon align-middle fs-16"></i> Approve
				// </button>
			</div>
		}
	</div>
}

templ ListOptionClient(clients []*clientE.ClientEntity) {
	if clients !=nil && len(clients) > 0 {
		for _, client := range clients {
			<option data-label={ client.Name } data-icon={ templates.AssetURL("/static/" + client.Logo) } value={ client.ID.Hex() }>
				{ client.Name }
			</option>
		}
	}
}

templ ListOptionAdvertiser(adAccounts []*adAccountE.AdAccountEditClient) {
	if adAccounts !=nil && len(adAccounts) > 0 {
		for _, adAccount := range adAccounts {
			<option data-label={ adAccount.Name } data-icon={ templates.AssetURL("/static/" + adAccount.AccountID) } value={ adAccount.ID.Hex() }>
				{ adAccount.Name }
			</option>
		}
	}
}
