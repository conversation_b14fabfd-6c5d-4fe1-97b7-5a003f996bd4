package apis

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/facebook/custom_column_table/common/constants"
	"godsp/modules/facebook/custom_column_table/transport/responses"
)

type CustomColumnTableTempl struct {
	ReportFields  *[]constants.ReportField // Example field to hold report fields
	MetricColumns *[]responses.MetricColumnResp
	PresetColumns *[]responses.PresetColumnResp
}

// import "github.com/dev-networldasia/dspgos/gos/templates"
templ ModalCustomColumnTableContent(data CustomColumnTableTempl) {
	@styleForCustomColumnTable()
	<!-- Bootstrap Modal -->
	<div class="col-8 border-end py-2">
		// Search box for filtering metrics and columns
		<input type="text" id="searchBox" class="form-control mb-1" placeholder="Search for metrics or column settings"/>
		// Tabs for selecting metrics and custom columns
		<ul class="nav nav-tabs mb-0">
			<li class="nav-item">
				<a class="nav-link active" href="#tab-all" data-bs-toggle="tab">All</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#tab-custom" data-bs-toggle="tab">Custom</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#tab-preset-column" data-bs-toggle="tab">Template preset column</a>
			</li>
		</ul>
		// Tab content for metrics and custom columns
		<div class="tab-content p-2 rounded bg-light">
			<div class="tab-pane fade show active" id="tab-all">
				<ul id="selectableList" class="selectable-list d-flex flex-column mb-0 ps-0" style="height: 450px; overflow-y: auto;"></ul>
			</div>
			<div class="tab-pane fade" id="tab-custom">
				<div class="d-flex align-items-center justify-content-between border-bottom pb-2">
					<span class="fw-bold">Custom metrics</span>
					<button class="btn btn-success" id="openCustomMetricModal">+ Create custom metric</button>
				</div>
				<ul id="customSelectableList" class="selectable-list d-flex flex-column ps-0 mb-0" style="height: 403px; overflow-y: auto;"></ul>
			</div>
			<div class="tab-pane fade" id="tab-preset-column">
				<ul id="presetColumnSelectableList" class="selectable-list d-flex flex-column mb-0 ps-0" style="height: 450px; overflow-y: auto;"></ul>
			</div>
		</div>
	</div>
	// List of selected columns
	<div class="col-4 py-2">
		<div class="column-box">
			<h6 class="modal-title text-success">
				<span id="selectedTitle">Current preset column</span>
				<span id="selectedTitleCount" class="fs-12">0</span>
			</h6>
			<p class="text-muted small mb-2" id="listColumnCustomView">
				Drag and drop to arrange columns as they'll appear in the table.
			</p>
			<ul id="columnList" class="column-list flex-column bg-light p-1 rounded" style=" height: 482px; max-height: 500px; overflow-y: auto;"></ul>
		</div>
	</div>
	@scriptContentCustomColumnTable(data)
}

templ scriptContentCustomColumnTable(data CustomColumnTableTempl) {
	<template id="fieldsReportStr">{ templ.JSONString(data.ReportFields) }</template>
	<template id="metricFieldsStr">{ templ.JSONString(data.MetricColumns) }</template>
	<template id="presetColumnsStr">{ templ.JSONString(data.PresetColumns) }</template>
	<!-- SortableJS CDN -->
	<script src={ templates.AssetURL("/static/themes/libs/sortable/sortable.min.js") }></script>
	//
	<script type="module" defer>
   import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
   import { requestApi } from "/static/js/common/httpService.js";

   let allReportFields = JSON.parse($("#fieldsReportStr").html()).sort((a, b) => a.title - b.title) || [];
   let allMetricFields = JSON.parse($("#metricFieldsStr").html()).sort((a, b) => a.title - b.title) || [];
   let allPresetColumn = JSON.parse($("#presetColumnsStr").html()).sort((a, b) => a.name - b.name) || [];
   console.log(" 🚀 ~ templscriptContentCustomColumnTable ~ allPresetColumn:", allPresetColumn)
   allReportFields.sort((a, b) => a.title.localeCompare(b.title));
   allMetricFields.sort((a, b) => a.title.localeCompare(b.title));
   allPresetColumn.sort((a, b) => a.name.localeCompare(b.name));
   $("#fieldsReportStr").remove();
   $("#metricFields").remove();
   $("#presetColumnsStr").remove();

   // List of custom-defined metrics
   // let allMetricFields = allMetricFields;

   // List of selected columns (checkbox checked)
   let selectedColumns = window?.adsTable?.customFields || [];

   /**
   * Render checkbox list for a given metrics list
   */
   function renderMetricList(listEl, metrics, filter = "", type = "all") {
      listEl.empty();
      metrics.forEach((metric) => {
         if (!filter || metric.title.toLowerCase().includes(filter.toLowerCase())) {
            const checked = selectedColumns.find((m) => m.key === metric.key || m.key === metric.id) ? "checked" : "";
            listEl.append(getItemCheckbox(metric, checked, type));
         }
      });
   }

   /**
   * Render checkbox list for a given template preset column list
   */
   function renderPresetColumnList(listEl, presets, filter = "") {
      presets.sort((a, b) => a.name - b.nameÏ)
      listEl.empty();
      listEl.append(getItemSelectPreset(null, true)); // Add current preset column item
      presets.forEach((preset) => {
         if (!preset || preset.name.toLowerCase().includes(filter.toLowerCase())) {
            listEl.append(getItemSelectPreset(preset));
         }
      });
   }

   /**
   * Create checkbox HTML select preset column
   */
   function getItemSelectPreset(preset, isCurrent = false) {
      const namePrivate = preset?.name_private ? `<span class="badge rounded-pill badge-soft-success"><i class="mdi mdi-circle-medium"></i> ${preset?.name_private}</span>` : "";

      if (isCurrent) {
         return `<li class="d-inline-block px-2 py-1 rounded gap-2">
                     <div class="d-flex gap-2 align-items-center">
                        <label class="flex-1 mb-0 text-primary" style="line-height: 42px;">
                        <i class=" ri-check-double-line"></i>
                        Current preset column</label>
                        <div class="d-flex align-items-center gap-2">
                        </div>
                     </div>
                  </li>`;
      }
      return `<li class="d-inline-block px-2 py-1 rounded gap-2" data-id="${preset.id}" style="height: 42px;">
                  <div class="d-flex gap-2 align-items-center">
                    <div class="flex-1">
                     <label class="flex-1 mb-0">${preset.name}</label>
                     ${namePrivate}
                    </div >
                     <div class="d-flex align-items-center gap-2">
                        <span class="edit-icon py-1 px-2" title="Edit preset" data-name="${preset.name}" data-name-private="${preset.name_private}" data-id="${preset.id}"><i class="ri-edit-line"></i></span>
                        <span class="remove-icon py-1 px-2" title="Remove preset" data-id="${preset.id}"><i class="ri-delete-bin-5-line"></i></span>
                        <button type="button" data-id="${preset.id}" class="btnApplyPresetColumn btn btn-outline-success btn-sm waves-effect waves-light material-shadow-none">Apply preset</button>
                     </div>
                  </div >
               </li > `;
   }

   /**
   * Create checkbox HTML for metric
   */
   function getItemCheckbox(metric, checked, type = "all") {
      const key = type == "all" ? metric.key : metric.id;
      const titlePrivate = metric?.title_private ? `<span span class="badge rounded-pill badge-soft-success" > <i class="mdi mdi-circle-medium"></i> ${metric?.title_private}</span > ` : "";
      return `<li li class="d-inline-block ps-4 pe-2 py-1 rounded gap-2" >
      <div class="form-check d-flex gap-2 align-items-center">
         <input class="form-check-input mb-1" type="checkbox" id="select_custom_${key}" value="${key}" ${checked}>
            <div class="flex-grow-1">
               <label class="form-check-label" for="select_custom_${key}">${metric.title}</label>
               ${titlePrivate}
            </div>
            ${type === "custom"
            ? `<div class="flex">
                              <span class="edit-icon edit-metric-column py-1 px-2" data-key="${key}"><i class="ri-edit-line"></i></span>
                              <span class="remove-icon py-1 px-2" data-key="${key}"><i class="ri-delete-bin-5-line"></i></span>
                           </div>`
            : ""
         }
      </div>
               </li > `;
   }

   // Render the selected columns (right list with drag)
   function renderSelectedColumns(title) {
      const list = $("#columnList");
      list.empty();
      $("#selectedTitleCount").text(`(${selectedColumns.length} cols)`);

      if (title) {
         $("#selectedTitle").text(title);
      }

      selectedColumns.forEach((metric) => {
         list.append(`
      <li li >
               <span class="drag-icon fw-bold pt-1"><i class="ri-more-2-fill d-inline-block" style="width: 8px;"></i><i class="ri-more-2-fill"></i></span>
               <span class="flex-grow-1 fs-12">${metric.title}</span>
               <span class="remove-icon fs-20 px-2" data-key="${metric.key}">&times;</span>
            </li >`);
      });
   }

   // Make selected columns sortable
   new Sortable(document.getElementById("columnList"), {
      animation: 150,
      // handle: '.drag-icon',
      onEnd: function (evt) {
         const moved = selectedColumns.splice(evt.oldIndex, 1)[0];
         selectedColumns.splice(evt.newIndex, 0, moved);
      },
   });

   // Convert preset column to selected columns
   function convertPresetToSelectedColumns(preset) {
      return preset.keys
         .map((key) => {
            const metric = allReportFields.find((m) => m.key === key) || allMetricFields.find((m) => m.id === key);
            return metric ? { ...metric } : null;
         })
         .filter((m) => m !== null);
   }

   // Handle Save Current Preset Columns
   function saveCurrentPresetColumns() {
      localStorage.removeItem("preset_column");
      loader();
      requestApi("PATCH", "/dsp/facebook/api/custom-column-table/update-current-preset-column", {
         keys: selectedColumns.map((m) => m.key),
      })
         .then((response) => {
            successAlert("Custom columns saved successfully!");
            // Close modal after saving
            setTimeout(() => {
               $("#customColumnModal").modal("hide");
               //Update table
               document.dispatchEvent(new CustomEvent("onChangeConfigColumnTable", {}));
            }, 1000);
         })
         .catch((error) => {
            errorAlert("Error save custom column: " + (error?.msg || "Unknown error"));
         })
         .finally(() => {
            loader(false);
         });
   }

   // Reset state preset form
   function resetPresetForm() {
      $("#footerControlPresetForm").hide(120);
      $('#customColumnModal input[name="namePreset"]').val("").attr("data-id", "");
      $('#customColumnModal input[name="namePrivatePreset"]').val("").attr("data-id", "");
      $("#switchToggleSaveNewPresetColumTable").prop("checked", false);
   }

   // Get preset Payload
   function getPresetFormData() {
      const name = $('#customColumnModal input[name="namePreset"]').val().trim();
      const name_private = $('#customColumnModal input[name="namePrivatePreset"]').val().trim();
      const id = $('#customColumnModal input[name="namePreset"]').attr("data-id") || null;
      const keys = selectedColumns.map((m) => m.key);
      return { name, name_private, keys, id };
   }


   // Handle Save New Preset Column
   function saveNewCustomPresetColumn() {
      let dataPayload = getPresetFormData();
      delete dataPayload.id
      loader();
      requestApi("POST", "/dsp/facebook/api/custom-column-table/create-preset-column", dataPayload)
         .then((res) => {
            const id = res?.data?.id
            if (!id) {
               throw new Error("Response data does not contain 'id' field");
               return;
            }
            dataPayload.id = id;
            allPresetColumn.push(dataPayload);
            renderPresetColumnList($("#presetColumnSelectableList"), allPresetColumn, "");
            successAlert("Create preset column successfully!");
            resetPresetForm();
         })
         .catch((error) => {
            errorAlert("Error creating custom column: " + (error?.msg || "Unknown error"));
         })
         .finally(() => {
            loader(false);
         });
   }

   // Handle Save Update Preset Column
   function saveUpdateCustomPresetColumn() {
      let dataPayload = getPresetFormData();
      loader();
      requestApi("PATCH", "/dsp/facebook/api/custom-column-table/update-preset-column", dataPayload)
         .then((response) => {
            successAlert("Update preset columns successfully!");
            resetPresetForm();
            const index = allPresetColumn.findIndex((m) => m.id === dataPayload.id);
            allPresetColumn[index] = dataPayload;
            renderPresetColumnList($("#presetColumnSelectableList"), allPresetColumn, "");
         })
         .catch((error) => {
            console.error("Error updating preset custom column:", error);
            errorAlert("Error update preset custom column: " + (error?.msg || "Unknown error"));
         })
         .finally(() => {
            loader(false);
         });
   }

   /**
    * Request delete metric column
    */
   async function requestDeleteCustomMetric(id) {
      loader();
      return requestApi("DELETE", "/dsp/facebook/api/custom-column-table/delete-metric-column", { id })
         .then((response) => {
            successAlert("Delete metric successfully!");
            return true;
         })
         .catch((error) => {
            errorAlert("Failed to delete metric: " + error?.msg);
            return false;
         })
         .finally(() => {
            loader(false);
            $('#modalRemoveItem #btnConfirmRemove').attr("data-action-by", "").attr("data-id", "");
            $("#modalRemoveItem").modal('hide');
         });
   }

   /**
   * Request delete preset column
   */
   async function requestDeletePresetColumn(id) {
      loader();
      return requestApi("DELETE", "/dsp/facebook/api/custom-column-table/delete-preset-column", { id })
         .then((response) => {
            successAlert("Delete preset successfully!");
            return true;
         })
         .catch((error) => {
            errorAlert("Failed to delete preset");
            return false;
         })
         .finally(() => {
            loader(false);
            $('#modalRemoveItem #btnConfirmRemove').attr("data-action-by", "").attr("data-id", "");
            $("#modalRemoveItem").modal('hide');
         });
   }

   // Handle checkbox (both tabs)
   $("#customColumnModal").on("change", '#selectableList input[type="checkbox"], #customSelectableList input[type="checkbox"]', function () {
      const key = $(this).val();
      const isChecked = this.checked;
      const metric = allReportFields.find((m) => m.key === key) || allMetricFields.find((m) => m.id === key);
      if ("metric" in metric) {
         metric.key = metric.id; // Ensure the key is set correctly
      }
      if (!metric) return;

      if (isChecked) {
         if (!selectedColumns.some((m) => m.key === key)) selectedColumns.push(metric);
      } else {
         selectedColumns = selectedColumns.filter((m) => m.key !== key);
      }
      renderSelectedColumns();
   });

   // Handle remove button in selected columns
   $("#columnList").on("click", ".remove-icon", function () {
      const key = $(this).data("key");
      selectedColumns = selectedColumns.filter((m) => m.key !== key);
      renderMetricList($("#selectableList"), allReportFields, $("#searchBox").val());
      renderMetricList($("#customSelectableList"), allMetricFields, $("#searchBox").val(), "custom");
      renderSelectedColumns();
   });

   // Handle search input
   $("#searchBox").on("input", function () {
      const filter = $(this).val();
      renderMetricList($("#selectableList"), allReportFields, filter);
      renderMetricList($("#customSelectableList"), allMetricFields, filter, "custom");
      renderPresetColumnList($("#presetColumnSelectableList"), allPresetColumn, filter);
   });

   // On Toggle Save Preset Columns
   $("#switchToggleSaveNewPresetColumTable").on("change", function () {
      $('#customColumnModal input[name="namePreset"]').val("").attr("data-id", "");
      $('#customColumnModal input[name="namePrivatePreset"]').val("").attr("data-id", "");
      const isChecked = this.checked;
      if (isChecked) {
         // Show input for preset name
         $('input[name="namePreset"]').show().focus();
         $("#footerControlPresetForm").show(120);
      } else {
         // Hide input for preset name
         $('input[name="namePreset"]').val("");
         $('input[name="namePrivatePreset"]').val("");
         $("#footerControlPresetForm").hide(120);
      }
   });

   // Handle click on "Apply preset" button
   $("#presetColumnSelectableList").on("click", ".btnApplyPresetColumn", function () {
      const id = $(this).data("id");
      localStorage.setItem("preset_column", id);
      $("#customColumnModal").modal("hide");
      document.dispatchEvent(new CustomEvent("onChangeConfigColumnTable", {}));
   });

   // Hover effect on preset column li
   $("#presetColumnSelectableList").on("mouseenter", "li", function () {
      if ($("#presetColumnSelectableList li.active").length > 0) return;
      const id = $(this).data("id");
      if (id) {
         const presetDetault = allPresetColumn.find((m) => m.id === id);
         selectedColumns = convertPresetToSelectedColumns(presetDetault);
      } else {
         selectedColumns = window?.adsTable?.customFields || [];
      }

      // renderMetricList($("#selectableList"), allReportFields);
      // renderMetricList($("#customSelectableList"), allMetricFields, "", "custom");
      renderSelectedColumns($(this).find("label").text());
   });

   // Handle click on "Edit preset columns" button
   $("#presetColumnSelectableList").on("click", ".edit-icon", function () {
      const id = $(this).data("id");
      const name = $(this).data("name");
      const namePrivate = $(this).data("name-private");

      $("#switchToggleSaveNewPresetColumTable").prop("checked", false);
      $("#presetColumnSelectableList li").removeClass("active");
      $(this).closest("li").addClass("active");

      $('#customColumnModal input[name="namePreset"]').val(name).attr("data-id", id).focus();
      $('#customColumnModal input[name="namePrivatePreset"]').val(namePrivate).attr("data-id", id).focus();
      $("#footerControlPresetForm").show(120);

      const presetDetault = allPresetColumn.find((m) => m.id === id);
      selectedColumns = convertPresetToSelectedColumns(presetDetault);
      renderMetricList($("#selectableList"), allReportFields);
      renderMetricList($("#customSelectableList"), allMetricFields, "", "custom");
      renderSelectedColumns($(this).siblings("label").text());
   });

   //Handle Edit or Create Preset Column
   $("#cancleEditOrCreatePreset").on("click", function () {
      resetPresetForm();

      selectedColumns = window.adsTable["customFields"] || [];
      renderMetricList($("#selectableList"), allReportFields);
      renderMetricList($("#customSelectableList"), allMetricFields, "", "custom");
      renderSelectedColumns();
   });

   // Save custom columns button
   $("#saveColumns").on("click", function () {
      if (selectedColumns.length === 0) {
         errorAlert("Please select at least one column to save.");
         return;
      }

      if ($('#footerControlPresetForm').is(":visible")) {
         const name = $('#customColumnModal input[name="namePreset"]').val().trim();
         if (!name) {
            errorAlert("Please enter a name for the preset column.");
            return;
         }
         if ($('#customColumnModal input[name="namePreset"]').attr("data-id")) {
            saveUpdateCustomPresetColumn();
         } else {
            saveNewCustomPresetColumn();
         }
      } else {
         saveCurrentPresetColumns();
      }
   });

   // Handle edit metric column
   $("#customSelectableList").on("click", ".edit-metric-column", function () {
      $("#customMetricColumnModal").modal("show");
      const key = $(this).data("key");
      const metric = allMetricFields.find((m) => m.id === key);
      if (window.customMetricColumnContentTable) {
         window.customMetricColumnContentTable.renderEditMetricContent(metric);
      }
   });

   // Modal show event
   $("#customColumnModal").on("shown.bs.modal", function () {
      renderMetricList($("#selectableList"), allReportFields);
      renderMetricList($("#customSelectableList"), allMetricFields, "", "custom");
      renderPresetColumnList($("#presetColumnSelectableList"), allPresetColumn, "");
      renderSelectedColumns();
   });

   // On Close Modal
   $("#customColumnModal").on("hidden.bs.modal", function () {
      selectedColumns = [];
      $("#searchBox").val("");
      $("#switchToggleSaveNewPresetColumTable").prop("checked", false);
      $('#customColumnModal input[name="namePreset"]').val("");
      $('#customColumnModal input[name="namePrivatePreset"]').val("");
      $('#footerControlPresetForm').hide();
      renderMetricList($("#selectableList"), allReportFields);
      renderMetricList($("#customSelectableList"), allMetricFields, "", "custom");
      renderSelectedColumns();

      window.customColumnModalContent = null;
   });

   // Handle confirm remove item
   $("#btnConfirmRemove").on("click", async function () {
      const actionBy = $(this).attr("data-action-by");
      const id = $(this).attr("data-id");
      if (actionBy === "metric") {
         const result = await requestDeleteCustomMetric(id);
         if (!result) return;

         allMetricFields = allMetricFields.filter((m) => m.id !== id);
         selectedColumns = selectedColumns.filter((m) => m.key !== id);
         renderMetricList($("#customSelectableList"), allMetricFields, $("#searchBox").val(), "custom");
         renderSelectedColumns();

      } else if (actionBy === "preset") {
         const result = await requestDeletePresetColumn(id);
         if (!result) return;
         allPresetColumn = allPresetColumn.filter((m) => m.id !== id);
         renderPresetColumnList($("#presetColumnSelectableList"), allPresetColumn, $("#searchBox").val());
      } else {
         console.error("Unknown action by:", actionBy);
      }
   })

   // Open custom metric modal
   $("#openCustomMetricModal").on("click", function () {
      const modal = new bootstrap.Modal(document.getElementById("customMetricColumnModal"), {
         backdrop: "static",
         focus: true,
         keyboard: false,
      });
      modal.show();
   });

   // Open Remove metric modal
   $("#customSelectableList").on("click", ".remove-icon", function () {
      $("#modalRemoveItem").modal("show");
      $('#modalRemoveItem #btnConfirmRemove').attr("data-action-by", "metric").attr("data-id", $(this).data("key"));
   });

   // Handle click on "Remove preset columns" button
   $("#presetColumnSelectableList").on("click", ".remove-icon", function () {
      $("#modalRemoveItem").modal("show");
      $('#modalRemoveItem #btnConfirmRemove').attr("data-action-by", "preset").attr("data-id", $(this).data("id"));
   });

   // Render Metric List By create custom metric
   window.customColumnModalContent = {
      renderCustomMetricList: function (metric) {
         const index = allMetricFields.findIndex((m) => m.id === metric.id);
         if (index > -1) {
            allMetricFields[index] = metric; // Update existing metric
         } else {
            allMetricFields.push(metric);
         }
         renderMetricList($("#customSelectableList"), allMetricFields, "", "custom");
      },
   };

</script>
}

templ styleForCustomColumnTable() {
	<style>
   #customColumnModal .selectable-list li.active,
   #customColumnModal .selectable-list li:hover {
      background-color: var(--tiktok-primary-50);
   }

   #customColumnModal .column-box h3 {
      font-size: 16px;
      margin-bottom: 12px;
   }

   #customColumnModal ul.column-list {
      list-style: none;
      padding: 0;
      margin: 0;
      max-height: 400px;
      overflow-y: auto;
   }

   #customColumnModal ul.column-list li {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      border: 1px solid #eee;
      border-radius: 6px;
      margin-bottom: 4px;
      background-color: #fff;
      font-size: 14px;
      cursor: grab;
      transition: background 0.2s;
   }

   #customColumnModal ul.column-list li:hover {
      background-color: #f2f2f2;
   }

   #customColumnModal .drag-icon {
      font-size: 18px;
      cursor: move;
      margin-right: 6px;
      padding: 0 3px;
      color: #999;
      user-select: none;
   }

   #customColumnModal .column-label {
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
   }

   #customColumnModal .remove-icon,
   #customColumnModal .edit-icon {
      font-size: 16px;
      color: #aaa;
      cursor: pointer;
      padding-left: 10px;
   }

   #customColumnModal .selectable-list .remove-icon,
   #customColumnModal .selectable-list .edit-icon,
   #customColumnModal .selectable-list .btnApplyPresetColumn {
      visibility: hidden;
      transition: visibility 0.2s ease-in-out;
   }

   #customColumnModal .selectable-list li.active .remove-icon,
   #customColumnModal .selectable-list li.active .edit-icon,
   #customColumnModal .selectable-list li.active .btnApplyPresetColumn,
   #customColumnModal .selectable-list li:hover .remove-icon,
   #customColumnModal .selectable-list li:hover .edit-icon,
   #customColumnModal .selectable-list li:hover .btnApplyPresetColumn {
      visibility: visible;
      transition: visibility 0.2s ease-in-out;
   }

   #customColumnModal .remove-icon:hover {
      color: #e74c3c;
   }

   #customColumnModal .edit-icon:hover {
      color: var(--tiktok-primary-500);
   }
</style>
}
