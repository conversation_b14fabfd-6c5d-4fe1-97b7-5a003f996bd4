package campaign

import (
	"github.com/dev-networldasia/dspgos/gos/templates"
	"godsp/modules/facebook/campaign/response"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
	"godsp/views/v2/facebook/campaign/components"
	fbCop "godsp/views/v2/facebook/components"
)

func getDataLayoutMaster(data *response.ListCampaignResp) masters.LayoutMasterDataFacebook {
	return masters.LayoutMasterDataFacebook{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}

func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "Facebook", DataKey: "facebook", Url: "/dsp/facebook"},
		{Title: "Campaigns", DataKey: "campaigns", Url: "/dsp/facebook/campaigns/list"},
	}
}

func getDataLayoutTable(data *response.ListCampaignResp) components.LayoutTableData {
	return components.LayoutTableData{
		AuthPermission:     data.AuthPermission,
		UserInfo:           data.UserInfo,
		Clients:            data.Clients,
		AdAccounts:         data.AdAccounts,
		Advertisers:        data.Advertisers,
		PresetsColumnTable: data.PresetsColumnTable,
	}
}

templ ListCampaign(data *response.ListCampaignResp) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	dataLayoutTable := getDataLayoutTable(data)
	}}
	@layouts.MasterFacebook(dataLayoutMaster, []templ.Component{cssHeader()}, scriptCampaignList()) {
		@layoutCops.ListBreadcrumdCpn("Facebook Campaigns", getPathBreadcrumb())
		if data != nil && data.FlashMsg != "" {
			@components.FlashMsgCpn(data.FlashMsg)
		}
		@components.Table(dataLayoutTable)
		@fbCop.ModalCustomColumnTable()
		@components.OffCanvasCampaign()
	}
}

templ cssHeader() {
	<link href={ templates.AssetURL("/static/themes/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css") } rel="stylesheet" type="text/css" />
	<link href={ templates.AssetURL("/static/themes/libs/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css") } rel="stylesheet" type="text/css" />
	<link href={ templates.AssetURL("/static/themes/libs/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css") } rel="stylesheet" type="text/css" />
	<link href={ templates.AssetURL("/static/themes/libs/sortable/sortable.min.css") } rel="stylesheet" type="text/css" />
	<link href={ templates.AssetURL("/static/css/facebook.css") } rel="stylesheet" type="text/css" />
}

templ scriptCampaignList() {
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net/js/jquery.dataTables.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-responsive/js/dataTables.responsive.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-buttons/js/dataTables.buttons.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-buttons/js/buttons.html5.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-buttons/js/buttons.print.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/datatables.net-buttons/js/buttons.colVis.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/sortable/sortable.min.js") } defer></script>
	<script src={ templates.AssetURL("/static/themes/libs/mathjs/math.min.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/campaign/index.js") } defer></script>
	<script type="module" src={ templates.AssetURL("/static/js/facebook/config-table.js") } defer></script>
	<script>
		document.addEventListener("DOMContentLoaded", function () {
			numberFormat(".number-format");
		});
	</script>
}
