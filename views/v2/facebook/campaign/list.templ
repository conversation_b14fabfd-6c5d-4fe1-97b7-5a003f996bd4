package campaign

import (
	"godsp/modules/facebook/custom_column_table/common/constants"
	"godsp/modules/facebook/custom_column_table/transport/responses"
	"godsp/modules/facebook/iface"
	"godsp/views/v2/layouts"
	layoutCops "godsp/views/v2/layouts/components"
	"godsp/views/v2/layouts/masters"
)

type FacebookCampaignListData struct {
	FlashMsg           string
	AuthPermission     map[string]int
	UserInfo           iface.UserInfoAuth
	AdAccounts         interface{}
	Clients            interface{}
	ReportFields       *[]constants.ReportField
	MetricColumns      *[]responses.MetricColumnResp
	PresetColumns      *[]responses.PresetColumnResp
}

func getDataLayoutMaster(data *FacebookCampaignListData) masters.LayoutMasterData {
	return masters.LayoutMasterData{
		AuthPermission: data.AuthPermission,
		UserInfo:       data.UserInfo,
	}
}

func getPathBreadcrumb() []layoutCops.PathBreadcrumb {
	return []layoutCops.PathBreadcrumb{
		{Title: "Facebook", DataKey: "facebook", Url: "/dsp/facebook"},
		{Title: "Campaigns", DataKey: "campaigns", Url: "/dsp/facebook/campaign/list"},
	}
}

templ FacebookCampaignList(data *FacebookCampaignListData) {
	{{
	dataLayoutMaster := getDataLayoutMaster(data)
	}}
	@layouts.Master(dataLayoutMaster, []templ.Component{}, campaignListScript(data)) {
		@layoutCops.ListBreadcrumdCpn("Facebook Campaigns", getPathBreadcrumb())
		@FacebookCampaignListContent(data)
	}
}

templ FacebookCampaignListContent(data *FacebookCampaignListData) {
		<div class="page-content">
			<div class="container-fluid">
				<!-- start page title -->
				<div class="row">
					<div class="col-12">
						<div class="page-title-box d-sm-flex align-items-center justify-content-between">
							<h4 class="mb-sm-0">Facebook Campaigns</h4>
							<div class="page-title-right">
								<ol class="breadcrumb m-0">
									<li class="breadcrumb-item"><a href="javascript: void(0);">Facebook</a></li>
									<li class="breadcrumb-item active">Campaigns</li>
								</ol>
							</div>
						</div>
					</div>
				</div>
				<!-- end page title -->
				
				<!-- Campaign Table -->
				<div class="row">
					<div class="col-12">
						<div class="card">
							<div class="card-header">
								<div class="d-flex align-items-center justify-content-between">
									<h5 class="card-title mb-0">Campaign Management</h5>
									<div class="d-flex gap-2">
										<button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#customColumnModal">
											<i class="ri-settings-3-line"></i> Customize Columns
										</button>
										<button type="button" class="btn btn-primary">
											<i class="ri-add-line"></i> Create Campaign
										</button>
									</div>
								</div>
							</div>
							<div class="card-body">
								<!-- Filter Section -->
								<div class="row mb-3">
									<div class="col-md-3">
										<label class="form-label">Ad Account</label>
										<select class="form-select" id="adAccountFilter">
											<option value="">All Ad Accounts</option>
											<!-- Ad accounts will be populated here -->
										</select>
									</div>
									<div class="col-md-3">
										<label class="form-label">Client</label>
										<select class="form-select" id="clientFilter">
											<option value="">All Clients</option>
											<!-- Clients will be populated here -->
										</select>
									</div>
									<div class="col-md-3">
										<label class="form-label">Status</label>
										<select class="form-select" id="statusFilter">
											<option value="">All Status</option>
											<option value="ACTIVE">Active</option>
											<option value="PAUSED">Paused</option>
											<option value="ARCHIVED">Archived</option>
										</select>
									</div>
									<div class="col-md-3">
										<label class="form-label">Date Range</label>
										<input type="text" class="form-control" id="dateRangeFilter" placeholder="Select date range"/>
									</div>
								</div>
								
								<!-- Table -->
								<div class="table-responsive">
									<table id="campaignTable" class="table table-striped table-hover align-middle">
										<thead class="table-light">
											<tr>
												<th>
													<input type="checkbox" id="selectAll" class="form-check-input"/>
												</th>
												<th>Status</th>
												<th>Campaign Name</th>
												<th>Objective</th>
												<!-- Dynamic columns will be added here -->
											</tr>
										</thead>
										<tbody>
											<!-- Table data will be populated via JavaScript -->
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Custom Column Modal -->
		@customColumnModal(data)
	}

templ customColumnModal(data *FacebookCampaignListData) {
	<!-- Include the custom column modal components -->
	<div id="customColumnModalContainer">
		<!-- Modal content will be loaded here -->
	</div>
}

templ campaignListScript(data *FacebookCampaignListData) {
	<script type="module" defer>
		import { defaultConfigTableFacebook } from "/static/js/facebook/config-table.js";
		import { successAlert, errorAlert, loader } from "/static/js/common/helpers.js";
		import { requestApi } from "/static/js/common/httpService.js";

		// Initialize data
		const adAccounts = { templ.JSONString(data.AdAccounts) };
		const clients = { templ.JSONString(data.Clients) };
		const reportFields = { templ.JSONString(data.ReportFields) };
		const metricColumns = { templ.JSONString(data.MetricColumns) };
		const presetColumns = { templ.JSONString(data.PresetColumns) };

		let campaignTable;
		let selectedCampaigns = [];

		// Initialize page
		$(document).ready(function() {
			initializeFilters();
			initializeTable();
			bindEvents();
		});

		function initializeFilters() {
			// Populate ad account filter
			const adAccountSelect = $('#adAccountFilter');
			if (adAccounts && adAccounts.length > 0) {
				adAccounts.forEach(account => {
					adAccountSelect.append(`<option value="${ "{" }account.id}">${ "{" }account.name}</option>`);
				});
			}

			// Populate client filter
			const clientSelect = $('#clientFilter');
			if (clients && clients.length > 0) {
				clients.forEach(client => {
					clientSelect.append(`<option value="${ "{" }client.id}">${ "{" }client.name}</option>`);
				});
			}
		}

		function initializeTable() {
			campaignTable = $('#campaignTable').DataTable({
				...defaultConfigTableFacebook({ displayStart: 0 }, "Campaigns"),
				processing: true,
				serverSide: true,
				ajax: {
					url: '/dsp/facebook/api/campaigns/list-datatable',
					type: 'POST',
					data: function(d) {
						return {
							...d,
							ad_account_id: $('#adAccountFilter').val(),
							client_id: $('#clientFilter').val(),
							status: $('#statusFilter').val(),
							date_range: $('#dateRangeFilter').val()
						};
					}
				},
				columns: [
					{
						data: null,
						orderable: false,
						searchable: false,
						width: "30px",
						className: "align-middle",
						render: function(data, type, row) {
							return `<input type="checkbox" class="form-check-input row-select" value="${ "{" }row.campaign_id}">`;
						}
					},
					{
						data: 'status',
						name: 'status',
						className: "align-middle",
						render: function(data, type, row) {
							const statusClass = data === 'ACTIVE' ? 'success' : data === 'PAUSED' ? 'warning' : 'secondary';
							return `<span class="badge bg-${ "{" }statusClass}">${ "{" }data}</span>`;
						}
					},
					{
						data: 'campaign_name',
						name: 'campaign_name',
						className: "align-middle"
					},
					{
						data: 'objective',
						name: 'objective',
						className: "align-middle"
					}
					// Dynamic columns will be added based on custom column configuration
				],
				order: [[2, 'asc']],
				pageLength: 25,
				responsive: true
			});
		}

		function bindEvents() {
			// Select all checkbox
			$('#selectAll').on('change', function() {
				const isChecked = $(this).prop('checked');
				$('.row-select').prop('checked', isChecked);
				updateSelectedCampaigns();
			});

			// Individual row selection
			$(document).on('change', '.row-select', function() {
				updateSelectedCampaigns();
			});

			// Filter changes
			$('#adAccountFilter, #clientFilter, #statusFilter').on('change', function() {
				campaignTable.ajax.reload();
			});

			// Custom column modal events
			$('#customColumnModal').on('show.bs.modal', function() {
				loadCustomColumnModal();
			});

			// Listen for column configuration changes
			document.addEventListener('onChangeConfigColumnTable', function() {
				// Reload table with new column configuration
				campaignTable.ajax.reload();
			});
		}

		function updateSelectedCampaigns() {
			selectedCampaigns = [];
			$('.row-select:checked').each(function() {
				selectedCampaigns.push($(this).val());
			});
			
			// Update select all checkbox state
			const totalCheckboxes = $('.row-select').length;
			const checkedCheckboxes = $('.row-select:checked').length;
			
			if (checkedCheckboxes === 0) {
				$('#selectAll').prop('indeterminate', false).prop('checked', false);
			} else if (checkedCheckboxes === totalCheckboxes) {
				$('#selectAll').prop('indeterminate', false).prop('checked', true);
			} else {
				$('#selectAll').prop('indeterminate', true);
			}
		}

		function loadCustomColumnModal() {
			loader();
			requestApi("POST", "/dsp/facebook/api/custom-column-table/custom-modal")
				.then((response) => {
					if (response?.modal_custom_column_html) {
						$('#customColumnModalContainer').html(response.modal_custom_column_html);
					}
				})
				.catch((error) => {
					console.error("Error loading custom column modal:", error);
					errorAlert("Failed to load custom column modal. Please try again.");
				})
				.finally(() => {
					loader(false);
				});
		}

		// Export selected campaigns
		window.exportSelectedCampaigns = function() {
			if (selectedCampaigns.length === 0) {
				errorAlert("Please select at least one campaign to export.");
				return;
			}
			
			// Implement export functionality
			console.log("Exporting campaigns:", selectedCampaigns);
		};

		// Bulk actions
		window.bulkUpdateCampaigns = function(action) {
			if (selectedCampaigns.length === 0) {
				errorAlert("Please select at least one campaign.");
				return;
			}
			
			// Implement bulk update functionality
			console.log("Bulk action:", action, "for campaigns:", selectedCampaigns);
		};

		// Make functions available globally
		window.facebookCampaignTable = {
			reload: () => campaignTable.ajax.reload(),
			getSelected: () => selectedCampaigns
		};
	</script>
}
