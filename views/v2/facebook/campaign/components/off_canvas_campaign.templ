package components

templ OffCanvasCampaign() {
	<div class="offcanvas offcanvas-end" tabindex="-1" id="campaignOffCanvas" aria-labelledby="campaignOffCanvasLabel">
		<div class="offcanvas-header">
			<h5 class="offcanvas-title" id="campaignOffCanvasLabel">Campaign</h5>
			<button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
		</div>
		<div class="offcanvas-body">
			<div id="campaignOffCanvasContent">
				<!-- Content will be loaded here -->
				<div class="d-flex justify-content-center">
					<div class="spinner-border" role="status">
						<span class="visually-hidden">Loading...</span>
					</div>
				</div>
			</div>
		</div>
	</div>
}
