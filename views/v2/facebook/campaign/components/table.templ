package components



templ Table(data LayoutTableData) {
	{{
	LayoutHeaderTableData := LayoutHeaderTableData{
		AuthPermission:     data.AuthPermission,
		UserInfo:           data.UserInfo,
		Clients:            data.Clients,
		AdAccounts:         data.AdAccounts,
		Advertisers:        data.Advertisers,
		PresetsColumnTable: data.PresetsColumnTable,
	}
	}}
	@styleTable()
	<div class="row">
		<div class="col-lg-12">
			<div class="card">
				<div class="card-header d-flex justify-content-between">
					<div class="flex-1">
						<ul id="navTabTableFacebook" class="nav nav-tabs-custom nav_cus rounded card-header-tabs border-bottom-0" role="tablist">
							<li class="nav-item" style="width: auto;">
								<a
									data-navigo-href="dsp/facebook/campaigns/list"
									href="#campaigns"
									class="nav-link d-flex justify-content-between align-items-center active"
									data-bs-toggle="tab"
									data-tab="campaigns"
									role="tab"
								>
									<span class="d-flex align-items-center gap-1 fs-15">
										<i class="ri-megaphone-line fs-18 text-facebook"></i> 
										<span class="text-dark">Campaigns</span>
									</span>
									<button
										type="button"
										class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill ms-2 bg-facebook"
										id="campaign_selected"
									>
										<i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="campaign-selected-close"></i>
										0 selected
									</button>
								</a>
							</li>
							<li class="nav-item" style="width: auto;">
								<a
									data-navigo-href="dsp/facebook/adsets/list"
									href="#adsets"
									class="nav-link d-flex justify-content-between align-items-center"
									data-bs-toggle="tab"
									data-tab="adsets"
									role="tab"
								>
									<span class="d-flex align-items-center gap-1 fs-15">
										<i class="ri-stack-line fs-18 text-facebook"></i>
										<span id="selected_adset_name" class="text-dark">Ad Sets</span>
									</span>
									<button
										type="button"
										class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill ms-2 bg-facebook"
										id="adset_selected"
									>
										<i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="adset-selected-close"></i>
										0 selected
									</button>
								</a>
							</li>
							<li class="nav-item" style="width: auto;">
								<a
									data-navigo-href="dsp/facebook/ads/list"
									href="#ads"
									class="nav-link d-flex justify-content-between align-items-center"
									data-bs-toggle="tab"
									data-tab="ads"
									role="tab"
								>
									<span class="d-flex align-items-center gap-1 fs-15">
										<i class="ri-advertisement-line fs-18 text-facebook"></i> 
										<span id="selected_ad_name" class="text-dark">Ads</span>
									</span>
									<button
										type="button"
										class="d-none btn btn-cus-selected btn-info btn-label right rounded-pill ms-2 bg-facebook"
										id="ads_selected"
									>
										<i class="ri-close-line label-icon align-middle rounded-pill fs-12 ms-2" id="ads-selected-close"></i>
										0 selected
									</button>
								</a>
							</li>
						</ul>
					</div>
					@HeaderTableRight(LayoutHeaderTableData)
				</div>
				<div class="card-body">
					@HeaderTableLeft(LayoutHeaderTableData)
					<div class="tab-content" id="facebookTableTabContent">
						<div class="tab-pane fade show active" id="campaigns" role="tabpanel" aria-labelledby="campaigns-tab">
							@CampaignTable()
						</div>
						<div class="tab-pane fade" id="adsets" role="tabpanel" aria-labelledby="adsets-tab">
							@AdSetTable()
						</div>
						<div class="tab-pane fade" id="ads" role="tabpanel" aria-labelledby="ads-tab">
							@AdsTable()
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}

templ HeaderTableRight(data LayoutHeaderTableData) {
	<div class="d-flex align-items-center gap-2">
		<button type="button" class="btn btn-outline-primary btn-sm" id="customColumnBtn">
			<i class="ri-settings-3-line"></i> Columns
		</button>
		<div class="dropdown">
			<button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
				<i class="ri-add-line"></i> Create
			</button>
			<ul class="dropdown-menu">
				<li><a class="dropdown-item" href="#" id="createCampaignBtn">Campaign</a></li>
				<li><a class="dropdown-item" href="#" id="createAdSetBtn">Ad Set</a></li>
				<li><a class="dropdown-item" href="#" id="createAdBtn">Ad</a></li>
			</ul>
		</div>
	</div>
}

templ HeaderTableLeft(data LayoutHeaderTableData) {
	<div class="row mb-3" id="filterSection">
		<div class="col-md-3">
			<label class="form-label">Ad Account</label>
			<select class="form-select" id="adAccountFilter">
				<option value="">All Ad Accounts</option>
			</select>
		</div>
		<div class="col-md-3">
			<label class="form-label">Client</label>
			<select class="form-select" id="clientFilter">
				<option value="">All Clients</option>
			</select>
		</div>
		<div class="col-md-3">
			<label class="form-label">Status</label>
			<select class="form-select" id="statusFilter">
				<option value="">All Status</option>
				<option value="ACTIVE">Active</option>
				<option value="PAUSED">Paused</option>
				<option value="ARCHIVED">Archived</option>
			</select>
		</div>
		<div class="col-md-3">
			<label class="form-label">Date Range</label>
			<input type="text" class="form-control" id="dateRangeFilter" placeholder="Select date range"/>
		</div>
	</div>
}

templ CampaignTable() {
	<div class="table-responsive table-card mb-1">
		<table
			class="table table-hover align-middle m-0"
			id="campaignTable"
			style="min-height: 200px;width:100%"
		>
			<thead class="table-light text-muted">
				<tr class="text-uppercase" id="campaignTableHeader">
					<th style="width: 20px;">
						<div class="form-check">
							<input
								class="form-check-input"
								type="checkbox"
								id="checkAllCampaigns"
								value="option"
							/>
						</div>
					</th>
					<th data-key="status" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Status
					</th>
					<th data-key="campaign_name" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Campaign Name
					</th>
					<th data-key="objective" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Objective
					</th>
					<th data-key="impressions" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Impressions
					</th>
					<th data-key="clicks" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Clicks
					</th>
					<th data-key="spend" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Spend
					</th>
					<th data-key="actions">Actions</th>
				</tr>
			</thead>
			<tbody>
				<!-- Data will be populated via JavaScript -->
			</tbody>
		</table>
	</div>
}

templ AdSetTable() {
	<div class="table-responsive table-card mb-1">
		<table
			class="table table-hover align-middle m-0"
			id="adsetTable"
			style="min-height: 200px;width:100%"
		>
			<thead class="table-light text-muted">
				<tr class="text-uppercase" id="adsetTableHeader">
					<th style="width: 20px;">
						<div class="form-check">
							<input
								class="form-check-input"
								type="checkbox"
								id="checkAllAdSets"
								value="option"
							/>
						</div>
					</th>
					<th data-key="status" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Status
					</th>
					<th data-key="adset_name" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Ad Set Name
					</th>
					<th data-key="campaign_name" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Campaign
					</th>
					<th data-key="bid_strategy" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Bid Strategy
					</th>
					<th data-key="impressions" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Impressions
					</th>
					<th data-key="clicks" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Clicks
					</th>
					<th data-key="spend" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Spend
					</th>
					<th data-key="actions">Actions</th>
				</tr>
			</thead>
			<tbody>
				<!-- Data will be populated via JavaScript -->
			</tbody>
		</table>
	</div>
}

templ AdsTable() {
	<div class="table-responsive table-card mb-1">
		<table
			class="table table-hover align-middle m-0"
			id="adsTable"
			style="min-height: 200px;width:100%"
		>
			<thead class="table-light text-muted">
				<tr class="text-uppercase" id="adsTableHeader">
					<th style="width: 20px;">
						<div class="form-check">
							<input
								class="form-check-input"
								type="checkbox"
								id="checkAllAds"
								value="option"
							/>
						</div>
					</th>
					<th data-key="status" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Status
					</th>
					<th data-key="ad_name" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Ad Name
					</th>
					<th data-key="adset_name" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Ad Set
					</th>
					<th data-key="campaign_name" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Campaign
					</th>
					<th data-key="impressions" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Impressions
					</th>
					<th data-key="clicks" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Clicks
					</th>
					<th data-key="spend" class="sortable-column">
						<span class="drag-handle">⋮⋮</span>
						Spend
					</th>
					<th data-key="actions">Actions</th>
				</tr>
			</thead>
			<tbody>
				<!-- Data will be populated via JavaScript -->
			</tbody>
		</table>
	</div>
}

templ styleTable() {
	<style>
		.text-facebook {
			color: #1877f2 !important;
		}
		.bg-facebook {
			background-color: #1877f2 !important;
		}
		.sortable-column {
			cursor: move;
			position: relative;
		}
		.drag-handle {
			color: #6c757d;
			margin-right: 5px;
			cursor: grab;
		}
		.drag-handle:active {
			cursor: grabbing;
		}
		.sortable-ghost {
			opacity: 0.4;
		}
		.sortable-chosen {
			background-color: #e3f2fd;
		}
		.nav_cus .nav-link.active {
			background-color: #f8f9fa;
			border-color: #dee2e6 #dee2e6 #f8f9fa;
		}
		.btn-cus-selected {
			font-size: 0.75rem;
			padding: 0.25rem 0.5rem;
		}
	</style>
}
