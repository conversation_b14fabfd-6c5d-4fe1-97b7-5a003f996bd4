package components

import (
	"godsp/modules/facebook/iface"
)

type LayoutTableData struct {
	AuthPermission     map[string]int   `json:"auth_permission"`
	UserInfo           iface.UserInfoFb `json:"user_info"`
	Clients            interface{}      `json:"clients"`
	AdAccounts         interface{}      `json:"ad_accounts"`
	Advertisers        interface{}      `json:"advertisers"`
	PresetsColumnTable interface{}      `json:"presets_column_table"`
}

type LayoutHeaderTableData struct {
	AuthPermission     map[string]int   `json:"auth_permission"`
	UserInfo           iface.UserInfoFb `json:"user_info"`
	Clients            interface{}      `json:"clients"`
	AdAccounts         interface{}      `json:"ad_accounts"`
	Advertisers        interface{}      `json:"advertisers"`
	PresetsColumnTable interface{}      `json:"presets_column_table"`
}
