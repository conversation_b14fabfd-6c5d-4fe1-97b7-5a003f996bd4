package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type MetricColumnResp struct {
	ID           primitive.ObjectID `json:"id" bson:"_id"`
	Key          string             `json:"key" bson:"key"`
	Title        string             `json:"title" bson:"title"`
	TitlePrivate string             `json:"title_private,omitempty" bson:"title_private,omitempty"`
	Unit         string             `json:"unit,omitempty" bson:"unit,omitempty"`
	Metric       string             `json:"metric,omitempty" bson:"metric,omitempty"`
	Type         string             `json:"type,omitempty" bson:"type,omitempty"`
	Description  string             `json:"description,omitempty" bson:"description,omitempty"`
}
