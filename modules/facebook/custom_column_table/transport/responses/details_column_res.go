package responses

import "go.mongodb.org/mongo-driver/bson/primitive"

type DetailColumnResp struct {
	Key          string    `json:"key" bson:"key"`
	Title        string    `json:"title" bson:"title"`
	Unit         string    `json:"unit,omitempty" bson:"unit,omitempty"`
	Metric       *string   `json:"metric,omitempty" bson:"metric,omitempty"`
	Type         string    `json:"type,omitempty" bson:"type,omitempty"`
	Available    *[]string `json:"available,omitempty"`
	IsReport     *bool     `json:"is_report,omitempty"`
	IsCustomCell bool      `json:"is_custom_cell,omitempty"`

	ListUserIDs *[]primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientID    *[]primitive.ObjectID `bson:"client_id,omitempty" json:"client_id,omitempty"`
}
