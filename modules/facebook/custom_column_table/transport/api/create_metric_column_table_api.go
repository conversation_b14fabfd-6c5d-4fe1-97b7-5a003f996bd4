package api

import (
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
)

/**
 * Get Custom Column Table
 */
func (a *CustomColumnTableApi) CreateMetricColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.PayloadCreateMetricColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.CreatedBy = userId
		payload.CreatedAt = time.Now()

		metric, err := a.usc.CreateMetricColumnUsc(c.UserContext(), payload)

		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"message": "Create metric column successfully!",
			"data":    metric,
		}))
	}
}
