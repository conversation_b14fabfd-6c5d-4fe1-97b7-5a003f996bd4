package api

import (
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/modules/facebook/custom_column_table/usecase"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FacebookCustomColumnTableApi struct {
	usc usecase.FacebookCustomColumnTableUsecase
}

func NewFacebookCustomColumnTableApi(usc usecase.FacebookCustomColumnTableUsecase) *FacebookCustomColumnTableApi {
	return &FacebookCustomColumnTableApi{usc: usc}
}

// Create Metric Column
func (a *FacebookCustomColumnTableApi) CreateMetricColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadCreateMetricColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.CreatedBy = userId.Hex()
		payload.CreatedAt = time.Now()

		metric, err := a.usc.CreateMetricColumnUsc(c.UserContext(), payload)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.Status(http.StatusCreated).JSON(fiber.Map{
			"success": true,
			"data":    metric,
		})
	}
}

// Update Metric Column
func (a *FacebookCustomColumnTableApi) UpdateMetricColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadCreateMetricColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Get ID from request
		idStr := c.FormValue("id")
		if idStr == "" {
			return core.ReturnErrForApi(c, "Metric column ID is required")
		}

		id, err := primitive.ObjectIDFromHex(idStr)
		if err != nil {
			return core.ReturnErrForApi(c, "Invalid metric column ID")
		}

		// Validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.CreatedBy = userId.Hex()
		payload.CreatedAt = time.Now()

		err = a.usc.UpdateMetricColumnUsc(c.UserContext(), id, payload)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"message": "Metric column updated successfully",
		})
	}
}

// Delete Metric Column
func (a *FacebookCustomColumnTableApi) DeleteMetricColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		idStr := c.FormValue("id")
		if idStr == "" {
			return core.ReturnErrForApi(c, "Metric column ID is required")
		}

		id, err := primitive.ObjectIDFromHex(idStr)
		if err != nil {
			return core.ReturnErrForApi(c, "Invalid metric column ID")
		}

		err = a.usc.DeleteMetricColumnUsc(c.UserContext(), id)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"message": "Metric column deleted successfully",
		})
	}
}

// Create Preset Column
func (a *FacebookCustomColumnTableApi) CreatePresetColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadCreatePresetColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.CreatedBy = userId.Hex()
		payload.CreatedAt = time.Now()

		presetID, err := a.usc.CreatePresetColumnUsc(c.UserContext(), payload)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.Status(http.StatusCreated).JSON(fiber.Map{
			"success": true,
			"data":    fiber.Map{"id": presetID.Hex()},
		})
	}
}

// Update Preset Column
func (a *FacebookCustomColumnTableApi) UpdatePresetColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadCreatePresetColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Get ID from request
		idStr := c.FormValue("id")
		if idStr == "" {
			return core.ReturnErrForApi(c, "Preset column ID is required")
		}

		id, err := primitive.ObjectIDFromHex(idStr)
		if err != nil {
			return core.ReturnErrForApi(c, "Invalid preset column ID")
		}

		// Validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.CreatedBy = userId.Hex()
		payload.CreatedAt = time.Now()

		err = a.usc.UpdatePresetColumnUsc(c.UserContext(), id, payload)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"message": "Preset column updated successfully",
		})
	}
}

// Delete Preset Column
func (a *FacebookCustomColumnTableApi) DeletePresetColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		idStr := c.FormValue("id")
		if idStr == "" {
			return core.ReturnErrForApi(c, "Preset column ID is required")
		}

		id, err := primitive.ObjectIDFromHex(idStr)
		if err != nil {
			return core.ReturnErrForApi(c, "Invalid preset column ID")
		}

		err = a.usc.DeletePresetColumnUsc(c.UserContext(), id)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"message": "Preset column deleted successfully",
		})
	}
}

// Get Details Preset Column
func (a *FacebookCustomColumnTableApi) GetDetailsPresetColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.PayloadDetailsPresetColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		// Validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		name, keys, err := a.usc.GetDetailsPresetColumnUsc(c.UserContext(), payload)
		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"name": name,
				"keys": keys,
			},
		})
	}
}
