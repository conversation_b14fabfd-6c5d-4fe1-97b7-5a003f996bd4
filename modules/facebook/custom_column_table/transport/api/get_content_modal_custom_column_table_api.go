package api

import (
	"godsp/modules/facebook/custom_column_table/common/constants"
	"godsp/modules/facebook/custom_column_table/mapping"
	"godsp/pkg/sctx/core"
	apiTempl "godsp/views/v2/facebook/tableAds/components/apis"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

func (a *CustomColumnTableApi) GetContentCustomColumnTableModalApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		data := apiTempl.CustomColumnTableTempl{
			ReportFields: &constants.AdServerReportFields, // Initialize with nil or an empty slice
		}

		metricColumn, err := a.usc.GetListMetricColumnUsc(c.Context())
		if err != nil || metricColumn == nil {
			return core.ReturnErrsForApi(c, err)
		}
		data.MetricColumns = metricColumn

		presetsColumnEnt, err := a.usc.GetListPresetColumnUsc(c.Context())
		if err != nil || presetsColumnEnt == nil {
			return core.ReturnErrsForApi(c, err)
		}

		data.PresetColumns = mapping.MappingListDetailPresetColumnTable(presetsColumnEnt)

		customColumnTableHtml, err := templates.RenderToString(c, apiTempl.ModalCustomColumnTableContent(data))
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		customMetricColumnTableHtml, err := templates.RenderToString(c, apiTempl.ModalCustomMetricColumnTableContent(data))
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.JSON(fiber.Map{
			"msg":                             "Get content custom column table modal successfully",
			"modal_custom_column_html":        customColumnTableHtml,
			"modal_custom_metric_column_html": customMetricColumnTableHtml,
		})
	}
}
