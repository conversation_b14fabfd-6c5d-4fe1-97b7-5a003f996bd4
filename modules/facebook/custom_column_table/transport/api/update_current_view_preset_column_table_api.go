package api

import (
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/pkg/sctx/core"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

/**
 * Get Custom Column Table
 */
func (a *CustomColumnTableApi) UpdateCurrentViewPresetColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.PayloadUpdateCurrentViewPresetColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		err := a.usc.UpdateCurrentViewPresetColumnUsc(c.UserContext(), payload)
		if err != nil {
			return core.ReturnErrsForApi(c, err)
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"message": "Update current preset column successfully!",
		}))
	}
}
