package api

import (
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
)

/**
 * Get Custom Column Table
 */
func (a *CustomColumnTableApi) DeleteMetricColumnTableApi() fiber.Handler {
	return func(c *fiber.Ctx) error {

		var payload requests.PayloadDeleteMetricColumnReq
		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		//validate
		validationErrors := payload.Validate()
		if len(validationErrors) > 0 {
			return core.ReturnErrsForApi(c, validationErrors)
		}

		userId, err := utils.GetUserId(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		payload.UpdatedBy = userId
		payload.UpdatedAt = time.Now()

		err = a.usc.DeleteMetricColumnUsc(c.UserContext(), payload)

		if err != nil {
			return core.ReturnErrsForApi(c, []string{err.Error()})
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"message": "Delete metric column successfully!",
		}))
	}
}
