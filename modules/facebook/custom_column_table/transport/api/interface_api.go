package api

import (
	"context"
	"godsp/modules/facebook/custom_column_table/entity"
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/modules/facebook/custom_column_table/transport/responses"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ApiCustomColumnTabletUsc interface {
	GetDetailsPresetColumnUsc(context context.Context, payload requests.PayloadDetailsPresetColumnReq) (*string, *[]responses.DetailColumnResp, error)
	GetListPresetColumnUsc(context context.Context) (*[]entity.PresetColumnTableEntity, error)
	UpdateCurrentViewPresetColumnUsc(context context.Context, payload requests.PayloadUpdateCurrentViewPresetColumnReq) error
	UpdateMetricColumnUsc(context context.Context, payload requests.PayloadUpdateMetricColumnReq) error
	GetListMetricColumnUsc(context context.Context) (*[]responses.MetricColumnResp, error)
	DeleteMetricColumnUsc(context context.Context, payload requests.PayloadDeleteMetricColumnReq) error
	DeletePresetColumnUsc(context context.Context, payload requests.PayloadDeletePresetColumnReq) error
	UpdatePresetColumnUsc(context context.Context, payload requests.PayloadUpdatePresetColumnReq) error
	CreatePresetColumnUsc(context context.Context, payload requests.PayloadCreatePresetColumnReq) (*primitive.ObjectID, error)
	CreateMetricColumnUsc(context context.Context, payload requests.PayloadCreateMetricColumnReq) (*responses.MetricColumnResp, error)
}

type CustomColumnTableApi struct {
	usc ApiCustomColumnTabletUsc
}

func NewCustomColumnTableApi(usc ApiCustomColumnTabletUsc) *CustomColumnTableApi {
	return &CustomColumnTableApi{
		usc: usc,
	}
}
