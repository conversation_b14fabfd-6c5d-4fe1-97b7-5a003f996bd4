package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadUpdatePresetColumnReq struct {
	ID          primitive.ObjectID `json:"id,omitempty" bson:"id"`
	Keys        []string           `json:"keys" bson:"keys" validate:"required,min=1,dive,min=2,max=125"`
	Name        string             `json:"name" bson:"name" validate:"min=5,max=125"`
	NamePrivate *string            `json:"name_private,omitempty" bson:"name_private,omitempty" validate:"omitempty,min=2,max=100"`

	ClientID *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`

	UpdatedBy primitive.ObjectID `json:"-" bson:"-"`
	UpdatedAt time.Time          `json:"-" bson:"-"`
}

func (p *PayloadUpdatePresetColumnReq) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				errMsg := "ID is required"
				validationErrors = append(validationErrors, &errMsg)
			case "NamePrivate":
				errMsg := "NamePrivate must be between 2 and 100 characters"
				validationErrors = append(validationErrors, &errMsg)
			case "Keys":
				errMsg := "Keys must be provided and should have at least 1 item, each key must be between 2 and 125 characters"
				validationErrors = append(validationErrors, &errMsg)
			case "Name":
				errMsg := "Name must be between 5 and 125 characters"
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		if len(validationErrors) > 0 {
			return validationErrors // Return the first error for simplicity
		}

	}
	return validationErrors // No validation errors
}
