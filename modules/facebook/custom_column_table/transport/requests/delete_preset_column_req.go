package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadDeletePresetColumnReq struct {
	ID primitive.ObjectID `json:"id,omitempty" bson:"id" validate:"required"`

	UpdatedBy primitive.ObjectID `json:"-" bson:"-"`
	UpdatedAt time.Time          `json:"-" bson:"-"`
}

func (p *PayloadDeletePresetColumnReq) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				errMsg := "ID is required"
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		if len(validationErrors) > 0 {
			return validationErrors // Return the first error for simplicity
		}

	}
	return validationErrors // No validation errors
}
