package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
)

type PayloadCreateMetricColumnReq struct {
	Title        string  `json:"title,omitempty" bson:"title,omitempty" validate:"min=2,max=150"`
	TitlePrivate *string `json:"title_private,omitempty" bson:"title_private,omitempty" validate:"omitempty,min=2,max=100"`
	Unit         string  `json:"unit,omitempty" bson:"unit,omitempty"`
	Formula      string  `json:"formula" bson:"formula" validate:"required"`
	Type         string  `json:"type,omitempty" bson:"type,omitempty"`

	CreatedAt time.Time `json:"-" bson:"-"`
	CreatedBy string    `json:"-" bson:"-"`
}

func (p *PayloadCreateMetricColumnReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Title":
				validationErrors = append(validationErrors, "Name must be between 2 and 150 characters")
			case "TitlePrivate":
				validationErrors = append(validationErrors, "Name Private must be between 2 and 100 characters")
			case "Formula":
				validationErrors = append(validationErrors, "Formula is required")
			case "Type":
				validationErrors = append(validationErrors, "Type must be one of the predefined types")
			}
		}
	}
	return validationErrors
}
