package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
)

type PayloadCreatePresetColumnReq struct {
	Name        string   `json:"name" bson:"name" validate:"required,min=2,max=150"`
	NamePrivate *string  `json:"name_private,omitempty" bson:"name_private,omitempty" validate:"omitempty,min=2,max=100"`
	Keys        []string `json:"keys" bson:"keys" validate:"required,min=1"`

	CreatedAt time.Time `json:"-" bson:"-"`
	CreatedBy string    `json:"-" bson:"-"`
}

func (p *PayloadCreatePresetColumnReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Name":
				validationErrors = append(validationErrors, "Name must be between 2 and 150 characters")
			case "NamePrivate":
				validationErrors = append(validationErrors, "Name Private must be between 2 and 100 characters")
			case "Keys":
				validationErrors = append(validationErrors, "At least one column key is required")
			}
		}
	}
	return validationErrors
}
