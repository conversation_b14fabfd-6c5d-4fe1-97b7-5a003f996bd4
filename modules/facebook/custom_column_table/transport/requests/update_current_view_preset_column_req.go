package requests

import (
	"github.com/go-playground/validator/v10"
)

type PayloadUpdateCurrentViewPresetColumnReq struct {
	Keys []string `json:"keys" bson:"keys" validate:"required,min=1"`
}

func (p *PayloadUpdateCurrentViewPresetColumnReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "Keys":
				validationErrors = append(validationErrors, "At least one column key is required")
			}
		}
	}
	return validationErrors
}
