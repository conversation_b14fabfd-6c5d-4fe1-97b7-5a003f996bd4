package requests

import (
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadUpdateCurrentViewPresetColumnReq struct {
	Keys     []string            `json:"keys" bson:"keys" validate:"required,min=1,dive,min=5,max=100"`
	ClientID *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
}

func (p *PayloadUpdateCurrentViewPresetColumnReq) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ClientID":
				errMsg := "ClientID is required"
				validationErrors = append(validationErrors, &errMsg)
			case "Keys":
				errMsg := "Keys must be provided and should have at least 1 item, each key must be between 5 and 100 characters"
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		if len(validationErrors) > 0 {
			return validationErrors // Return the first error for simplicity
		}

	}
	return validationErrors // No validation errors
}
