package requests

import (
	"time"

	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadUpdateMetricColumnReq struct {
	ID           primitive.ObjectID `json:"id" bson:"_id" validate:"required"`
	Title        string             `json:"title,omitempty" bson:"title,omitempty" validate:"min=2,max=150"`
	TitlePrivate *string            `json:"title_private,omitempty" bson:"title_private,omitempty" validate:"omitempty,min=2,max=100"`
	Unit         string             `json:"unit,omitempty" bson:"unit,omitempty"`
	Metric       string             `json:"metric" bson:"metric" validate:"required"`
	Type         string             `json:"type,omitempty" bson:"type,omitempty"`
	Description  string             `json:"description,omitempty" bson:"description,omitempty"`

	// Key       string             `json:"-" bson:"-"`
	UpdatedAt time.Time          `json:"-" bson:"-"`
	UpdatedBy primitive.ObjectID `json:"-" bson:"-" `
}

func (p *PayloadUpdateMetricColumnReq) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				errMsg := "ID is required"
				validationErrors = append(validationErrors, &errMsg)
			case "Title":
				errMsg := "Title must be between 2 and 150 characters"
				validationErrors = append(validationErrors, &errMsg)
			case "TitlePrivate":
				errMsg := "Name Private must be between 2 and 100 characters"
				validationErrors = append(validationErrors, &errMsg)
			case "Metric":
				errMsg := "Metric is required"
				validationErrors = append(validationErrors, &errMsg)
			case "Type":
				errMsg := "Type must be one of the predefined types"
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		if len(validationErrors) > 0 {
			return validationErrors // Return the first error for simplicity
		}

	}
	return validationErrors // No validation errors
}
