package requests

import (
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadDetailsPresetColumnReq struct {
	ID       *primitive.ObjectID `json:"preset_column_id,omitempty" bson:"preset_column_id"`
	IsDefult *bool               `json:"is_defult,omitempty" bson:"is_defult,omitempty"`
	ClientID *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
}

func (p *PayloadDetailsPresetColumnReq) Validate() []*string {
	validate := validator.New()
	var validationErrors []*string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				errMsg := "ID is required"
				validationErrors = append(validationErrors, &errMsg)
			case "ClientID":
				errMsg := "ClientID is required"
				validationErrors = append(validationErrors, &errMsg)
			}
		}
		if len(validationErrors) > 0 {
			return validationErrors // Return the first error for simplicity
		}

	}
	return validationErrors // No validation errors
}
