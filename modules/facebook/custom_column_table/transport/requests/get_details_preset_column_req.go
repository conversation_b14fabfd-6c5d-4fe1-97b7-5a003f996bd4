package requests

import (
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayloadDetailsPresetColumnReq struct {
	ID primitive.ObjectID `json:"id" bson:"id" validate:"required"`
}

func (p *PayloadDetailsPresetColumnReq) Validate() []string {
	validate := validator.New()
	var validationErrors []string
	err := validate.Struct(p)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "ID":
				validationErrors = append(validationErrors, "Preset column ID is required")
			}
		}
	}
	return validationErrors
}
