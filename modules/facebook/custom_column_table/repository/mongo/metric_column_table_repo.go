package mongo

import (
	"context"
	"errors"
	"godsp/modules/facebook/custom_column_table/entity"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type metricColumnTableRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewMetricColumnTableRepo(DB *mongo.Database) *metricColumnTableRepo {
	return &metricColumnTableRepo{
		DB:         DB,
		Collection: DB.Collection(entity.CustomMetricColumnTableEntity{}.CollectionName()),
	}
}

/**
 * FindOneAdRepo
 */
func (r *metricColumnTableRepo) FindOneMetricColumnRepo(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*entity.CustomMetricColumnTableEntity, error) {
	var result entity.CustomMetricColumnTableEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

/**
 * UpsertMetricColumnRepo
 */
func (r *metricColumnTableRepo) UpsertMetricColumnRepo(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	_, err := r.Collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	return nil
}

/**
 * Update Metric Column
 */
func (r *metricColumnTableRepo) UpdateMetricColumnRepo(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	_, err := r.Collection.UpdateOne(ctx, filter, update, opts...)
	if err != nil {
		return err
	}
	return nil
}

/**
 * FindListMetricColumnRepo
 */
func (r *metricColumnTableRepo) FindListMetricColumnRepo(ctx context.Context, filter any, opts ...*options.FindOptions) (*[]entity.CustomMetricColumnTableEntity, error) {
	var metrics []entity.CustomMetricColumnTableEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, mongo.ErrNilCursor
	}

	if err = cursor.All(ctx, &metrics); err != nil {
		return nil, err
	}

	return &metrics, nil
}

/**
 * Insert Metric Column
 */
func (r *metricColumnTableRepo) InsertMetricColumnRepo(ctx context.Context, filter any, data any) (*primitive.ObjectID, error) {
	result, err := r.Collection.InsertOne(ctx, data)
	if err != nil {
		return nil, err
	}
	insertedID, ok := result.InsertedID.(primitive.ObjectID)

	if !ok {
		return nil, errors.New("failed to convert inserted ID to ObjectID")
	}

	return &insertedID, nil
}

/**
 * DeleteMetricColumnRepo
 */
func (r *metricColumnTableRepo) DeleteMetricColumnRepo(ctx context.Context, filter any) error {
	_, err := r.Collection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}
