package mongo

import (
	"context"
	"godsp/modules/facebook/custom_column_table/entity"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type FacebookPresetColumnTableRepo struct {
	db *mongo.Database
}

func NewFacebookPresetColumnTableRepo(db *mongo.Database) *FacebookPresetColumnTableRepo {
	return &FacebookPresetColumnTableRepo{db: db}
}

func (r *FacebookPresetColumnTableRepo) getCollection() *mongo.Collection {
	return r.db.Collection(entity.FacebookPresetColumnTableEntity{}.CollectionName())
}

func (r *FacebookPresetColumnTableRepo) Create(ctx context.Context, data *entity.FacebookPresetColumnTableEntity) (*entity.FacebookPresetColumnTableEntity, error) {
	result, err := r.getCollection().InsertOne(ctx, data)
	if err != nil {
		return nil, err
	}

	data.ID = result.InsertedID.(primitive.ObjectID)
	return data, nil
}

func (r *FacebookPresetColumnTableRepo) Update(ctx context.Context, id primitive.ObjectID, data *entity.FacebookPresetColumnTableEntity) error {
	filter := bson.M{"_id": id}
	update := bson.M{"$set": data}

	_, err := r.getCollection().UpdateOne(ctx, filter, update)
	return err
}

func (r *FacebookPresetColumnTableRepo) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}
	_, err := r.getCollection().DeleteOne(ctx, filter)
	return err
}

func (r *FacebookPresetColumnTableRepo) FindByID(ctx context.Context, id primitive.ObjectID) (*entity.FacebookPresetColumnTableEntity, error) {
	var result entity.FacebookPresetColumnTableEntity
	filter := bson.M{"_id": id}

	err := r.getCollection().FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *FacebookPresetColumnTableRepo) FindByCreatedBy(ctx context.Context, createdBy string) (*[]entity.FacebookPresetColumnTableEntity, error) {
	var results []entity.FacebookPresetColumnTableEntity
	filter := bson.M{"created_by": createdBy}

	cursor, err := r.getCollection().Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return &results, nil
}

func (r *FacebookPresetColumnTableRepo) FindAll(ctx context.Context) (*[]entity.FacebookPresetColumnTableEntity, error) {
	var results []entity.FacebookPresetColumnTableEntity

	cursor, err := r.getCollection().Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return &results, nil
}
