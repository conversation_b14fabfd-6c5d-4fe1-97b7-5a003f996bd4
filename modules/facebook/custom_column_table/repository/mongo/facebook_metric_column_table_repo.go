package mongo

import (
	"context"
	"godsp/modules/facebook/custom_column_table/entity"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type FacebookMetricColumnTableRepo struct {
	db *mongo.Database
}

func NewFacebookMetricColumnTableRepo(db *mongo.Database) *FacebookMetricColumnTableRepo {
	return &FacebookMetricColumnTableRepo{db: db}
}

func (r *FacebookMetricColumnTableRepo) getCollection() *mongo.Collection {
	return r.db.Collection(entity.FacebookMetricColumnTableEntity{}.CollectionName())
}

func (r *FacebookMetricColumnTableRepo) Create(ctx context.Context, data *entity.FacebookMetricColumnTableEntity) (*entity.FacebookMetricColumnTableEntity, error) {
	result, err := r.getCollection().InsertOne(ctx, data)
	if err != nil {
		return nil, err
	}

	data.ID = result.InsertedID.(primitive.ObjectID)
	return data, nil
}

func (r *FacebookMetricColumnTableRepo) Update(ctx context.Context, id primitive.ObjectID, data *entity.FacebookMetricColumnTableEntity) error {
	filter := bson.M{"_id": id}
	update := bson.M{"$set": data}

	_, err := r.getCollection().UpdateOne(ctx, filter, update)
	return err
}

func (r *FacebookMetricColumnTableRepo) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}
	_, err := r.getCollection().DeleteOne(ctx, filter)
	return err
}

func (r *FacebookMetricColumnTableRepo) FindByID(ctx context.Context, id primitive.ObjectID) (*entity.FacebookMetricColumnTableEntity, error) {
	var result entity.FacebookMetricColumnTableEntity
	filter := bson.M{"_id": id}

	err := r.getCollection().FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *FacebookMetricColumnTableRepo) FindByCreatedBy(ctx context.Context, createdBy string) (*[]entity.FacebookMetricColumnTableEntity, error) {
	var results []entity.FacebookMetricColumnTableEntity
	filter := bson.M{"created_by": createdBy}

	cursor, err := r.getCollection().Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return &results, nil
}

func (r *FacebookMetricColumnTableRepo) FindAll(ctx context.Context) (*[]entity.FacebookMetricColumnTableEntity, error) {
	var results []entity.FacebookMetricColumnTableEntity

	cursor, err := r.getCollection().Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return &results, nil
}
