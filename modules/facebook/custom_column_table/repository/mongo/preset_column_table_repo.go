package mongo

import (
	"context"
	"errors"
	"godsp/modules/facebook/custom_column_table/entity"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type presetColumnTableRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewPresetColumnTableRepo(DB *mongo.Database) *presetColumnTableRepo {
	return &presetColumnTableRepo{
		DB:         DB,
		Collection: DB.Collection(entity.PresetColumnTableEntity{}.CollectionName()),
	}
}

/**
 * UpsertPresetRepo
 */
func (r *presetColumnTableRepo) UpsertPresetRepo(ctx context.Context, filter any, update any, opts ...*options.UpdateOptions) error {
	_, err := r.Collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	return nil
}

/**
 * UpsertPresetRepo
 */
func (r *presetColumnTableRepo) UpdatePresetRepo(ctx context.Context, filter any, update any, opts ...*options.UpdateOptions) error {
	_, err := r.Collection.UpdateOne(ctx, filter, update, options.Update())
	if err != nil {
		return err
	}
	return nil
}

/**
 * UpsertPresetRepo
 */
func (r *presetColumnTableRepo) InsertPresetRepo(ctx context.Context, filter any, data any) (*primitive.ObjectID, error) {
	result, err := r.Collection.InsertOne(ctx, data)

	if err != nil {
		return nil, err
	}
	insertID, ok := result.InsertedID.(primitive.ObjectID)
	if !ok {
		return nil, errors.New("failed to convert inserted ID to ObjectID")
	}
	return &insertID, nil
}

/**
 * FindOneAdRepo
 */
func (r *presetColumnTableRepo) FindOnePresetColumnRepo(ctx context.Context, filter any, opts ...*options.FindOneOptions) (*entity.PresetColumnTableEntity, error) {
	var result entity.PresetColumnTableEntity
	err := r.Collection.FindOne(ctx, filter, opts...).Decode(&result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

/**
 * FindListMetricColumnRepo
 */
func (r *presetColumnTableRepo) FindListPresetColumnRepo(ctx context.Context, filter any, opts ...*options.FindOptions) (*[]entity.PresetColumnTableEntity, error) {
	var presets []entity.PresetColumnTableEntity

	cursor, err := r.Collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor == nil {
		return nil, mongo.ErrNilCursor
	}

	if err = cursor.All(ctx, &presets); err != nil {
		return nil, err
	}

	return &presets, nil
}

/**
 * Delete Preset Column
 */
func (r *presetColumnTableRepo) DeletePresetColumnRepo(ctx context.Context, filter any) error {
	_, err := r.Collection.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

/**
 * Update BsonM Filed for Preset Column
 */
func (r *presetColumnTableRepo) UpdateManyPresetColumnRepo(ctx context.Context, filter interface{}, update interface{}) error {

	result, err := r.Collection.UpdateMany(ctx, filter, update, options.Update())
	if err != nil {
		return err
	}
	if result.MatchedCount == 0 && result.ModifiedCount == 0 {
		return nil
	}

	return nil
}
