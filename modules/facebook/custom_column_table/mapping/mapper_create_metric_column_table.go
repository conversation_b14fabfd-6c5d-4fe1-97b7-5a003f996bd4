package mapping

import (
	"crypto/md5"
	"encoding/hex"
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/modules/facebook/custom_column_table/transport/responses"
	"regexp"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
)

func MapperCreateMetricColumnTable(payload requests.PayloadCreateMetricColumnReq) (any, *responses.MetricColumnResp) {
	key := GenerateMetricKey(payload.Title, payload.Metric)
	if payload.TitlePrivate == nil {
		title := ""
		payload.TitlePrivate = &title
	}
	data := bson.M{
		"key":           key,
		"title":         payload.Title,
		"title_private": payload.TitlePrivate,
		"description":   payload.Description,
		"unit":          payload.Unit,
		"metric":        payload.Metric,
		"type":          payload.Type,
		"created_by":    payload.CreatedBy,
		"created_at":    payload.CreatedAt,
	}

	metricRes := responses.MetricColumnResp{
		Title:        payload.Title,
		TitlePrivate: *payload.TitlePrivate,
		Unit:         payload.Unit,
		Metric:       payload.Metric,
		Type:         payload.Type,
		Description:  payload.Description,
	}
	return data, &metricRes
}

func GenerateMetricKey(title, metric string) string {
	// Normalize title
	re := regexp.MustCompile(`[^\w]+`)
	slug := strings.ToLower(re.ReplaceAllString(title, "_"))
	slug = strings.Trim(slug, "_")

	// Tạo hash để phòng tránh trùng
	hash := md5.Sum([]byte(title + metric))
	shortHash := hex.EncodeToString(hash[:])[:5]

	return "facebook_metric_" + slug + "_" + shortHash
}
