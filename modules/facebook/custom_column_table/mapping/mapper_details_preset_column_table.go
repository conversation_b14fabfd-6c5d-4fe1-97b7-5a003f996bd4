package mapping

import (
	"godsp/modules/facebook/custom_column_table/common/constants"
	"godsp/modules/facebook/custom_column_table/transport/responses"
)

func MappingDetailPresetColumnTable(presetFields *[]string, metricFields *[]responses.MetricColumnResp) *[]responses.DetailColumnResp {
	var result []responses.DetailColumnResp

	if presetFields == nil || len(*presetFields) == 0 {
		return &result
	}

	// Tạo map tra nhanh primitiveFields và metricFields
	primitiveMap := make(map[string]responses.DetailColumnResp)
	for _, field := range constants.AdServerReportFields {
		primitiveMap[field.Key] = responses.DetailColumnResp{
			Key:          field.Key,
			Title:        field.Title,
			Unit:         field.Unit,
			Type:         field.Type,
			Available:    field.Available,
			IsCustomCell: field.IsCustomCell,
		}
	}

	metricMap := make(map[string]responses.DetailColumnResp)
	if metricFields != nil {
		for _, field := range *metricFields {
			metricMap[field.ID.Hex()] = responses.DetailColumnResp{
				Key:    field.ID.Hex(),
				Title:  field.Title,
				Unit:   field.Unit,
				Metric: &field.Metric,
				Type:   field.Type,
			}
		}
	}

	// Duyệt đúng thứ tự presetFields
	for _, key := range *presetFields {
		if val, ok := primitiveMap[key]; ok {
			result = append(result, val)
		} else if val, ok := metricMap[key]; ok {
			result = append(result, val)
		}
	}

	return &result
}
