package mapping

import (
	"encoding/json"
	"godsp/modules/facebook/custom_column_table/entity"
	"godsp/modules/facebook/custom_column_table/transport/responses"
)

func MappingListMetricColumnTable(metricColumns *[]entity.CustomMetricColumnTableEntity) *[]responses.MetricColumnResp {
	result := make([]responses.MetricColumnResp, 0, len(*metricColumns))

	for _, pre := range *metricColumns {
		jsonData, _ := json.Marshal(pre)
		var mapped responses.MetricColumnResp
		json.Unmarshal(jsonData, &mapped)
		result = append(result, mapped)
	}

	return &result

}
