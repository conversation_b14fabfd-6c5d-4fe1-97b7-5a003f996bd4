package mapping

import (
	"encoding/json"
	"godsp/modules/facebook/custom_column_table/entity"
	"godsp/modules/facebook/custom_column_table/transport/responses"
)

func MappingListDetailPresetColumnTable(presets *[]entity.PresetColumnTableEntity) *[]responses.PresetColumnResp {
	result := make([]responses.PresetColumnResp, 0, len(*presets))

	for _, pre := range *presets {
		jsonData, _ := json.Marshal(pre)
		var mapped responses.PresetColumnResp
		json.Unmarshal(jsonData, &mapped)
		result = append(result, mapped)
	}

	return &result

}
