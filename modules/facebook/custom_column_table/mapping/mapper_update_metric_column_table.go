package mapping

import (
	"godsp/modules/facebook/custom_column_table/transport/requests"

	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpdateMetricColumnTable(payload requests.PayloadUpdateMetricColumnReq) *bson.M {
	if payload.TitlePrivate == nil {
		title := ""
		payload.TitlePrivate = &title
	}
	data := bson.M{
		"$set": bson.M{
			"title":         payload.Title,
			"title_private": payload.TitlePrivate,
			"unit":          payload.Unit,
			"metric":        payload.Metric,
			"type":          payload.Type,
			"description":   payload.Description,
			"updated_by":    payload.UpdatedBy,
			"updated_at":    payload.UpdatedAt,
		},
	}

	return &data
}
