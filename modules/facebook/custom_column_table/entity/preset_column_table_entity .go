package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PresetColumnTableEntity struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	Name        string             `json:"name" bson:"name"`
	NamePrivate string             `json:"name_private" bson:"name_private"`
	Keys        *[]string          `json:"keys,omitempty" bson:"keys,omitempty"` //use key with default field column and object id with metric column
	Type        int32              `json:"type" bson:"type"`                     //0:Default - readonly,  1: current view, 2: custom preset
	// MetricFields *[]MetricField     `json:"metric_fields,omitempty" bson:"metric_fields,omitempty"`

	ListUserIDs *[]primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids"`
	ClientIDs   *[]primitive.ObjectID `bson:"client_ids,omitempty" json:"client_ids,omitempty"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by" `
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

func (PresetColumnTableEntity) CollectionName() string {
	return "preset_column_table"
}
