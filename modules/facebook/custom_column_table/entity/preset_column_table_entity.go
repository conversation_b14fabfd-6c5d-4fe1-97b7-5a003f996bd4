package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FacebookPresetColumnTableEntity struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Name        string             `json:"name" bson:"name"`
	NamePrivate string             `json:"name_private,omitempty" bson:"name_private,omitempty"`
	Keys        []string           `json:"keys" bson:"keys"`
	CreatedBy   string             `json:"created_by" bson:"created_by"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
}

func (FacebookPresetColumnTableEntity) CollectionName() string {
	return "facebook_preset_column_table"
}
