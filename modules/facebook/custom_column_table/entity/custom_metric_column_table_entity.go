package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CustomMetricColumnTableEntity struct {
	ID primitive.ObjectID `json:"id" bson:"_id"`

	Key          string `json:"key" bson:"key"`
	Title        string `json:"title" bson:"title"`
	TitlePrivate string `json:"title_private,omitempty" bson:"title_private,omitempty"`
	Unit         string `json:"unit,omitempty" bson:"unit,omitempty"`
	Metric       string `json:"metric,omitempty" bson:"metric,omitempty"`
	Type         string `json:"type,omitempty" bson:"type,omitempty"`
	Description  string `json:"description,omitempty" bson:"description,omitempty"`

	ListUserIDs []primitive.ObjectID `json:"list_user_ids,omitempty" bson:"list_user_ids,omitempty"`
	ClientID    primitive.ObjectID   `bson:"client_id,omitempty" json:"client_id,omitempty"`

	CreatedBy primitive.ObjectID `json:"created_by" bson:"created_by" `
	UpdatedBy primitive.ObjectID `json:"updated_by" bson:"updated_by"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

func (CustomMetricColumnTableEntity) CollectionName() string {
	return "custom_metric_column_table"
}
