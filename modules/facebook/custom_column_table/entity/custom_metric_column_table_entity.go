package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FacebookMetricColumnTableEntity struct {
	ID          primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Title       string             `json:"title" bson:"title"`
	TitlePrivate string            `json:"title_private,omitempty" bson:"title_private,omitempty"`
	Formula     string             `json:"formula" bson:"formula"`
	Type        string             `json:"type" bson:"type"`
	CreatedBy   string             `json:"created_by" bson:"created_by"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
}

func (FacebookMetricColumnTableEntity) CollectionName() string {
	return "facebook_metric_column_table"
}
