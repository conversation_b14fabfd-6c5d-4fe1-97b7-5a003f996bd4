package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesFacebookCustomColumnTable(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	apiGroup := app.Group("dsp/facebook/api/custom-column-table")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerFacebookCustomColumnTableApiService(serviceCtx)

		// Metric Column routes
		apiGroup.Post("/create-metric-column", compApi.CreateMetricColumnTableApi()).Name("facebook.custom_column_table.create-metric-column")
		apiGroup.Patch("/update-metric-column", compApi.UpdateMetricColumnTableApi()).Name("facebook.custom_column_table.update-metric-column")
		apiGroup.Delete("/delete-metric-column", compApi.DeleteMetricColumnTableApi()).Name("facebook.custom_column_table.delete-metric-column")

		// Preset Column routes
		apiGroup.Post("/create-preset-column", compApi.CreatePresetColumnTableApi()).Name("facebook.custom_column_table.create-preset-column")
		apiGroup.Patch("/update-preset-column", compApi.UpdatePresetColumnTableApi()).Name("facebook.custom_column_table.update-preset-column")
		apiGroup.Delete("/delete-preset-column", compApi.DeletePresetColumnTableApi()).Name("facebook.custom_column_table.delete-preset-column")
		apiGroup.Post("/details-preset-column", compApi.GetDetailsPresetColumnTableApi()).Name("facebook.custom_column_table.details-preset-column")
	}
}
