package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesCustomColumnTable(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	apiGroup := app.Group("dsp/facebook/api/custom-column-table")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}
		compApi := ComposerCustomColumnTableApiService(serviceCtx)

		apiGroup.Post("/custom-modal", compApi.GetContentCustomColumnTableModalApi()).Name("facebook.custom_column_table.custom-modal")
		apiGroup.Post("/details-preset-column", compApi.GetDetailsPresetColumnTableApi()).Name("facebook.custom_column_table.details-preset-column-table")
		apiGroup.Patch("/update-current-preset-column", compApi.UpdateCurrentViewPresetColumnTableApi()).Name("facebook.custom_column_table.update-current-preset-column-table")
		apiGroup.Post("/create-preset-column", compApi.CreatePresetColumnTableApi()).Name("facebook.custom_column_table.create-preset-column-table")
		apiGroup.Patch("/update-preset-column", compApi.UpdatePresetColumnTableApi()).Name("facebook.custom_column_table.update-preset-column-table")
		apiGroup.Delete("/delete-preset-column", compApi.DeletePresetColumnTableApi()).Name("facebook.custom_column_table.delete-preset-column-table")

		apiGroup.Post("/create-metric-column", compApi.CreateMetricColumnTableApi()).Name("facebook.custom_column_table.create-metric-column-table")
		apiGroup.Patch("/update-metric-column", compApi.UpdateMetricColumnTableApi()).Name("facebook.custom_column_table.update-metric-column-table")
		apiGroup.Delete("/delete-metric-column", compApi.DeleteMetricColumnTableApi()).Name("facebook.custom_column_table.delete-metric-column-table")
	}

}
