package routes

import (
	"godsp/modules/facebook/custom_column_table/repository/mongo"
	"godsp/modules/facebook/custom_column_table/transport/api"
	"godsp/modules/facebook/custom_column_table/usecase"
	"godsp/pkg/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	// "github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/gofiber/fiber/v2"
)

type ComposerCustomColumnTableApi interface {
	GetContentCustomColumnTableModalApi() fiber.Handler
	GetDetailsPresetColumnTableApi() fiber.Handler
	UpdateCurrentViewPresetColumnTableApi() fiber.Handler
	UpdateMetricColumnTableApi() fiber.Handler
	UpdatePresetColumnTableApi() fiber.Handler
	CreatePresetColumnTableApi() fiber.Handler
	CreateMetricColumnTableApi() fiber.Handler
	DeleteMetricColumnTableApi() fiber.Handler
	DeletePresetColumnTableApi() fiber.Handler
}

func ComposerCustomColumnTableApiService(serviceCtx sctx.ServiceContext) ComposerCustomColumnTableApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	// Init api
	metricColumnTableRepo := mongo.NewMetricColumnTableRepo(mongoDB)
	presetColumnTableRepo := mongo.NewPresetColumnTableRepo(mongoDB)

	usc := usecase.NewApiCustomColumnTableApiUsc(logger, presetColumnTableRepo, metricColumnTableRepo)

	api := api.NewCustomColumnTableApi(usc)
	return api
}
