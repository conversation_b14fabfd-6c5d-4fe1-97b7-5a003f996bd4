package routes

import (
	"godsp/modules/facebook/custom_column_table/repository/mongo"
	"godsp/modules/facebook/custom_column_table/transport/api"
	"godsp/modules/facebook/custom_column_table/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"
	"github.com/gofiber/fiber/v2"
)

type ComposerFacebookCustomColumnTableApi interface {
	CreateMetricColumnTableApi() fiber.Handler
	UpdateMetricColumnTableApi() fiber.Handler
	DeleteMetricColumnTableApi() fiber.Handler
	CreatePresetColumnTableApi() fiber.Handler
	UpdatePresetColumnTableApi() fiber.Handler
	DeletePresetColumnTableApi() fiber.Handler
	GetDetailsPresetColumnTableApi() fiber.Handler
}

func ComposerFacebookCustomColumnTableApiService(serviceCtx sctx.ServiceContext) ComposerFacebookCustomColumnTableApi {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	// Init repositories
	metricColumnTableRepo := mongo.NewFacebookMetricColumnTableRepo(mongoDB)
	presetColumnTableRepo := mongo.NewFacebookPresetColumnTableRepo(mongoDB)

	// Init usecase
	usc := usecase.NewFacebookCustomColumnTableUsc(logger, presetColumnTableRepo, metricColumnTableRepo)

	// Init API
	apiHandler := api.NewFacebookCustomColumnTableApi(usc)
	return apiHandler
}
