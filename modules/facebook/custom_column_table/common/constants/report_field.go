package constants

type ReportField struct {
	Key          string    `json:"key"`
	Title        string    `json:"title"`
	Type         string    `json:"type"`
	Unit         string    `json:"unit,omitempty"`
	Available    *[]string `json:"available,omitempty"`
	IsReport     bool      `json:"is_report,omitempty"`
	IsCustomCell bool      `json:"is_custom_cell,omitempty"`
}

var AdServerReportFields = []ReportField{
	{Key: "budget", Title: "Budget", Type: "string", Unit: "CURRENCY", IsCustomCell: true, Available: &[]string{"CAMPAIGN", "ADGROUP"}, IsReport: false},
	{Key: "scheduling", Title: "Ad scheduling", Type: "string", IsCustomCell: true, Available: &[]string{"ADGROUP"}, Unit: "", IsReport: false},
	{Key: "optimization_goal", Title: "Bid", Type: "string", Unit: "CURRENCY", IsCustomCell: true, Available: &[]string{"ADGROUP"}, IsReport: false},
	{Key: "result", Title: "Results", Type: "string", Unit: "", IsCustomCell: true, Available: &[]string{"ADGROUP", "CAMPAIGN"}, IsReport: false},
	{Key: "cost_per_result", Title: "Cost per result", Type: "string", IsCustomCell: true, Unit: "CURRENCY", Available: &[]string{"ADGROUP", "CAMPAIGN"}, IsReport: false},
	{Key: "result_rate", Title: "Result rate", Type: "string", Unit: "%", IsCustomCell: true, Available: &[]string{"ADGROUP", "CAMPAIGN"}, IsReport: false},

	{Key: "ad_id", Title: "Ad Id", Type: "string", Unit: "", Available: &[]string{"AD"}, IsReport: false},
	{Key: "adgroup_id", Title: "Adgroup Id", Type: "string", Available: &[]string{"AD", "ADGROUP"}, Unit: "", IsReport: false},
	{Key: "advertiser_id", Title: "Advertiser Id", Type: "string", Unit: "", IsReport: false},
	{Key: "campaign_id", Title: "Campaign Id", Type: "string", Unit: "", Available: &[]string{"AD", "ADGROUP", "CAMPAIGN"}, IsReport: false},
	{Key: "date", Title: "Date", Type: "string", Unit: "", IsReport: false},
	{Key: "ad_name", Title: "Ad Name", Type: "string", Unit: "", Available: &[]string{"AD"}, IsReport: false},
	{Key: "adgroup_name", Title: "Adgroup Name", Type: "string", Unit: "", Available: &[]string{"AD"}, IsReport: false},
	{Key: "advertiser_currency", Title: "Advertiser Currency", Type: "string", Unit: "", IsReport: false},
	{Key: "advertiser_name", Title: "Advertiser Name", Type: "string", Unit: "", IsReport: false},
	{Key: "campaign_name", Title: "Campaign Name", Type: "string", Unit: "", Available: &[]string{"AD", "ADGROUP"}, IsReport: false},

	{Key: "average_video_play", Title: "Average Video Play", Type: "float64", Unit: "", IsReport: true},
	{Key: "clicks", Title: "Clicks", Type: "int32", Unit: "", IsReport: true},
	{Key: "clicks_on_music_disc", Title: "Clicks On Music Disc", Type: "int32", Unit: "", IsReport: true},
	{Key: "comments", Title: "Comments", Type: "int32", Unit: "", IsReport: true},
	{Key: "conversion", Title: "Conversion", Type: "int32", Unit: "", IsReport: true},
	{Key: "conversion_rate", Title: "Conversion Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "cost_per_conversion", Title: "Cost Per Conversion", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_landing_page_view", Title: "Cost Per Landing Page View", Type: "int32", Unit: "CURRENCY", IsReport: true},
	// {Key: "cost_per_result", Title: "Cost Per Result", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cpc", Title: "Cpc", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "cpm", Title: "Cpm", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "ctr", Title: "Ctr", Type: "float64", Unit: "%", IsReport: true},

	{Key: "engaged_view", Title: "Engaged View", Type: "int32", Unit: "", IsReport: true},
	{Key: "engaged_view_15s", Title: "Engaged View 15s", Type: "int32", Unit: "", IsReport: true},
	{Key: "follows", Title: "Follows", Type: "int32", Unit: "", IsReport: true},
	{Key: "gross_impressions", Title: "Gross Impressions", Type: "int32", Unit: "", IsReport: true},
	{Key: "impressions", Title: "Impressions", Type: "int32", Unit: "", IsReport: true},
	{Key: "landing_page_view_rate", Title: "Landing Page View Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "likes", Title: "Likes", Type: "int32", Unit: "", IsReport: true},
	{Key: "profile_visits", Title: "Profile Visits", Type: "int32", Unit: "", IsReport: true},
	{Key: "profile_visits_rate", Title: "Profile Visits Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "real_time_conversion", Title: "Real Time Conversion", Type: "int32", Unit: "", IsReport: true},
	{Key: "real_time_conversion_rate", Title: "Real Time Conversion Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "real_time_cost_per_conversion", Title: "Real Time Cost Per Conversion", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "real_time_cost_per_result", Title: "Real Time Cost Per Result", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "real_time_result", Title: "Real Time Result", Type: "int32", Unit: "", IsReport: true},
	{Key: "real_time_result_rate", Title: "Real Time Result Rate", Type: "float64", Unit: "%", IsReport: true},

	// {Key: "result_rate", Title: "Result Rate", Type: "float64", Unit: "%", IsReport: true},
	// {Key: "run_date", Title: "Run Date", Type: "string", Unit: "", IsReport: false},
	{Key: "shares", Title: "Shares", Type: "int32", Unit: "", IsReport: true},
	{Key: "skan_app_install", Title: "Skan App Install", Type: "int32", Unit: "", IsReport: true},
	{Key: "skan_app_install_withheld", Title: "Skan App Install Withheld", Type: "int32", Unit: "", IsReport: true},
	{Key: "skan_conversion", Title: "Skan Conversion", Type: "int32", Unit: "", IsReport: true},
	{Key: "skan_conversion_rate", Title: "Skan Conversion Rate", Type: "int32", Unit: "%", IsReport: true},
	{Key: "skan_cost_per_app_install", Title: "Skan Cost Per App Install", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "skan_cost_per_conversion", Title: "Skan Cost Per Conversion", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "spend", Title: "Cost", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "stat_time_day", Title: "Stat Time Day", Type: "int64", Unit: "", IsReport: false},
	{Key: "total_landing_page_view", Title: "Total Landing Page View", Type: "int32", Unit: "", IsReport: true},
	{Key: "updated_by", Title: "Updated By", Type: "string", Unit: "", IsReport: false},
	{Key: "video_play_actions", Title: "Video Play Actions", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_views_p100", Title: "Video Views at 100%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_views_p25", Title: "Video Views at 25%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_views_p50", Title: "Video Views at 50%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_views_p75", Title: "Video Views at 75%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_watched_2s", Title: "2-second video views", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_watched_6s", Title: "6-second video views", Type: "int32", Unit: "", IsReport: true},
	{Key: "app_install", Title: "App Install", Type: "int32", Unit: "", IsReport: true},

	{Key: "cost_per_app_install", Title: "Cost Per App Install", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_offline_add_to_cart_event", Title: "Cost Per Offline Add To Cart Event", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_offline_initiate_checkout_event", Title: "Cost Per Offline Initiate Checkout Event", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_offline_place_order_event", Title: "Cost Per Offline Place Order Event", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_offline_shopping_event", Title: "Cost Per Offline Shopping Event", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_offline_view_content_event", Title: "Cost Per Offline View Content Event", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_onsite_initiate_checkout_count", Title: "Cost Per Onsite Initiate Checkout Count", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_onsite_on_web_cart", Title: "Cost Per Onsite On Web Cart", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_onsite_shopping", Title: "Cost Per Onsite Shopping", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_sales_lead", Title: "Cost Per Sales Lead", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_total_sales_lead", Title: "Cost Per Total Sales Lead", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "frequency", Title: "Frequency", Type: "float64", Unit: "", IsReport: true},
	{Key: "ix_page_view_count", Title: "Ix Page View Count", Type: "int32", Unit: "", IsReport: true},
	{Key: "ix_product_click_count", Title: "Ix Product Click Count", Type: "int32", Unit: "", IsReport: true},
	{Key: "live_effective_views", Title: "Live Effective Views", Type: "int32", Unit: "", IsReport: true},
	{Key: "live_product_clicks", Title: "Live Product Clicks", Type: "int32", Unit: "", IsReport: true},
	{Key: "live_unique_views", Title: "Live Unique Views", Type: "int32", Unit: "", IsReport: true},
	{Key: "live_views", Title: "Live Views", Type: "int32", Unit: "", IsReport: true},
	{Key: "offline_add_to_cart_event_rate", Title: "Offline Add To Cart Event Rate", Type: "int32", Unit: "%", IsReport: true},
	{Key: "offline_add_to_cart_events", Title: "Offline Add To Cart Events", Type: "int32", Unit: "", IsReport: true},
	{Key: "offline_add_to_cart_events_value", Title: "Offline Add To Cart Events Value", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "offline_initiate_checkout_event_rate", Title: "Offline Initiate Checkout Event Rate", Type: "int32", Unit: "%", IsReport: true},
	{Key: "offline_initiate_checkout_events", Title: "Offline Initiate Checkout Events", Type: "int32", Unit: "", IsReport: true},

	{Key: "offline_initiate_checkout_events_value", Title: "Offline Initiate Checkout Events Value", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "offline_place_order_event_rate", Title: "Offline Place Order Event Rate", Type: "int32", Unit: "%", IsReport: true},
	{Key: "offline_place_order_events", Title: "Offline Place Order Events", Type: "int32", Unit: "", IsReport: true},
	{Key: "offline_place_order_events_value", Title: "Offline Place Order Events Value", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "offline_shopping_event_rate", Title: "Offline Shopping Event Rate", Type: "int32", Unit: "%", IsReport: true},
	{Key: "offline_shopping_events", Title: "Offline Shopping Events", Type: "int32", Unit: "", IsReport: true},
	{Key: "offline_shopping_events_roas", Title: "Offline Shopping Events Roas", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "offline_shopping_events_value", Title: "Offline Shopping Events Value", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "offline_view_content_event_rate", Title: "Offline View Content Event Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "offline_view_content_events", Title: "Offline View Content Events", Type: "int32", Unit: "", IsReport: true},
	{Key: "offline_view_content_events_value", Title: "Offline View Content Events Value", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "onsite_initiate_checkout_count", Title: "Onsite Initiate Checkout Count", Type: "int32", Unit: "", IsReport: true},
	{Key: "onsite_initiate_checkout_count_rate", Title: "Onsite Initiate Checkout Count Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "onsite_on_web_cart", Title: "Onsite On Web Cart", Type: "int32", Unit: "", IsReport: true},
	{Key: "onsite_on_web_cart_rate", Title: "Onsite On Web Cart Rate", Type: "float64", Unit: "%", IsReport: true},

	{Key: "onsite_shopping", Title: "Onsite Shopping", Type: "int32", Unit: "", IsReport: true},
	{Key: "onsite_shopping_rate", Title: "Onsite Shopping Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "onsite_shopping_roas", Title: "Onsite Shopping Roas", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "sales_lead", Title: "Sales Lead", Type: "int32", Unit: "", IsReport: true},
	{Key: "total_onsite_initiate_checkout_count_value", Title: "Total Onsite Initiate Checkout Count Value", Type: "int32", Unit: "CURRENCY", IsReport: true},
	{Key: "total_onsite_on_web_cart_value", Title: "Total Onsite On Web Cart Value", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "total_sales_lead", Title: "Total Sales Lead", Type: "int32", Unit: "", IsReport: true},
	{Key: "engagement_rate", Title: "Engagement Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "engagements", Title: "Engagements", Type: "int32", Unit: "", IsReport: true},
}
