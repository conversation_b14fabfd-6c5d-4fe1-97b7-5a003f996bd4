package constants

type ReportField struct {
	Key          string    `json:"key"`
	Title        string    `json:"title"`
	Type         string    `json:"type"`
	Unit         string    `json:"unit,omitempty"`
	Available    *[]string `json:"available,omitempty"`
	IsReport     bool      `json:"is_report,omitempty"`
	IsCustomCell bool      `json:"is_custom_cell,omitempty"`
}

var FacebookAdserverReportFields = []ReportField{
	// Custom cells for Facebook
	{Key: "budget", Title: "Budget", Type: "string", Unit: "CURRENCY", IsCustomCell: true, Available: &[]string{"CAMPAIGN", "ADSET"}, IsReport: false},
	{Key: "bid_strategy", Title: "Bid Strategy", Type: "string", IsCustomCell: true, Available: &[]string{"ADSET"}, Unit: "", IsReport: false},
	{Key: "optimization_goal", Title: "Optimization Goal", Type: "string", IsCustomCell: true, Available: &[]string{"ADSET"}, IsReport: false},
	{Key: "result", Title: "Results", Type: "string", Unit: "", IsCustomCell: true, Available: &[]string{"ADSET", "CAMPAIGN", "AD"}, IsReport: false},
	{Key: "cost_per_result", Title: "Cost per result", Type: "string", IsCustomCell: true, Unit: "CURRENCY", Available: &[]string{"ADSET", "CAMPAIGN", "AD"}, IsReport: false},
	{Key: "result_rate", Title: "Result rate", Type: "string", Unit: "%", IsCustomCell: true, Available: &[]string{"ADSET", "CAMPAIGN", "AD"}, IsReport: false},

	// Basic fields
	{Key: "ad_id", Title: "Ad ID", Type: "string", Unit: "", Available: &[]string{"AD"}, IsReport: false},
	{Key: "adset_id", Title: "Ad Set ID", Type: "string", Available: &[]string{"AD", "ADSET"}, Unit: "", IsReport: false},
	{Key: "account_id", Title: "Account ID", Type: "string", Unit: "", IsReport: false},
	{Key: "campaign_id", Title: "Campaign ID", Type: "string", Unit: "", Available: &[]string{"AD", "ADSET", "CAMPAIGN"}, IsReport: false},
	{Key: "date_start", Title: "Date Start", Type: "string", Unit: "", IsReport: false},
	{Key: "date_stop", Title: "Date Stop", Type: "string", Unit: "", IsReport: false},
	{Key: "ad_name", Title: "Ad Name", Type: "string", Unit: "", Available: &[]string{"AD"}, IsReport: false},
	{Key: "adset_name", Title: "Ad Set Name", Type: "string", Unit: "", Available: &[]string{"AD", "ADSET"}, IsReport: false},
	{Key: "campaign_name", Title: "Campaign Name", Type: "string", Unit: "", Available: &[]string{"AD", "ADSET", "CAMPAIGN"}, IsReport: false},
	{Key: "account_name", Title: "Account Name", Type: "string", Unit: "", IsReport: false},
	{Key: "account_currency", Title: "Account Currency", Type: "string", Unit: "", IsReport: false},

	// Performance metrics
	{Key: "impressions", Title: "Impressions", Type: "int32", Unit: "", IsReport: true},
	{Key: "clicks", Title: "Clicks", Type: "int32", Unit: "", IsReport: true},
	{Key: "spend", Title: "Amount Spent", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "ctr", Title: "CTR (Click-through rate)", Type: "float64", Unit: "%", IsReport: true},
	{Key: "cpc", Title: "CPC (Cost per click)", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "cpm", Title: "CPM (Cost per 1,000 impressions)", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "cpp", Title: "CPP (Cost per 1,000 people reached)", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "frequency", Title: "Frequency", Type: "float64", Unit: "", IsReport: true},
	{Key: "reach", Title: "Reach", Type: "int32", Unit: "", IsReport: true},

	// Conversion metrics
	{Key: "conversions", Title: "Conversions", Type: "int32", Unit: "", IsReport: true},
	{Key: "conversion_rate", Title: "Conversion Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "cost_per_conversion", Title: "Cost per Conversion", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "conversion_value", Title: "Conversion Value", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "roas", Title: "ROAS (Return on ad spend)", Type: "float64", Unit: "", IsReport: true},

	// Video metrics
	{Key: "video_play_actions", Title: "Video Plays", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_p25_watched_actions", Title: "Video Watches at 25%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_p50_watched_actions", Title: "Video Watches at 50%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_p75_watched_actions", Title: "Video Watches at 75%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_p95_watched_actions", Title: "Video Watches at 95%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_p100_watched_actions", Title: "Video Watches at 100%", Type: "int32", Unit: "", IsReport: true},
	{Key: "video_avg_time_watched_actions", Title: "Average Video Watch Time", Type: "float64", Unit: "seconds", IsReport: true},

	// Engagement metrics
	{Key: "post_engagements", Title: "Post Engagements", Type: "int32", Unit: "", IsReport: true},
	{Key: "post_engagement_rate", Title: "Post Engagement Rate", Type: "float64", Unit: "%", IsReport: true},
	{Key: "page_engagement", Title: "Page Engagement", Type: "int32", Unit: "", IsReport: true},
	{Key: "post_reactions", Title: "Post Reactions", Type: "int32", Unit: "", IsReport: true},
	{Key: "post_comments", Title: "Post Comments", Type: "int32", Unit: "", IsReport: true},
	{Key: "post_shares", Title: "Post Shares", Type: "int32", Unit: "", IsReport: true},
	{Key: "page_likes", Title: "Page Likes", Type: "int32", Unit: "", IsReport: true},

	// Link clicks and landing page views
	{Key: "link_clicks", Title: "Link Clicks", Type: "int32", Unit: "", IsReport: true},
	{Key: "outbound_clicks", Title: "Outbound Clicks", Type: "int32", Unit: "", IsReport: true},
	{Key: "landing_page_views", Title: "Landing Page Views", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_landing_page_view", Title: "Cost per Landing Page View", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// App install metrics
	{Key: "app_installs", Title: "App Installs", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_app_install", Title: "Cost per App Install", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Lead generation metrics
	{Key: "leads", Title: "Leads", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_lead", Title: "Cost per Lead", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Purchase metrics
	{Key: "purchases", Title: "Purchases", Type: "int32", Unit: "", IsReport: true},
	{Key: "purchase_value", Title: "Purchase Value", Type: "float64", Unit: "CURRENCY", IsReport: true},
	{Key: "cost_per_purchase", Title: "Cost per Purchase", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Add to cart metrics
	{Key: "add_to_cart", Title: "Add to Cart", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_add_to_cart", Title: "Cost per Add to Cart", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Initiate checkout metrics
	{Key: "initiate_checkout", Title: "Initiate Checkout", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_initiate_checkout", Title: "Cost per Initiate Checkout", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// View content metrics
	{Key: "view_content", Title: "View Content", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_view_content", Title: "Cost per View Content", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Search metrics
	{Key: "searches", Title: "Searches", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_search", Title: "Cost per Search", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Complete registration metrics
	{Key: "complete_registration", Title: "Complete Registration", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_complete_registration", Title: "Cost per Complete Registration", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Messaging metrics
	{Key: "messaging_conversations_started", Title: "Messaging Conversations Started", Type: "int32", Unit: "", IsReport: true},
	{Key: "cost_per_messaging_conversation_started", Title: "Cost per Messaging Conversation Started", Type: "float64", Unit: "CURRENCY", IsReport: true},

	// Attribution window
	{Key: "attribution_setting", Title: "Attribution Setting", Type: "string", Unit: "", IsReport: false},
}
