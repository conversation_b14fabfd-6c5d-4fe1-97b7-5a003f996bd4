package usecase

import (
	"context"
	"godsp/modules/facebook/custom_column_table/entity"

	"github.com/dev-networldasia/dspgos/sctx"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MetricColumnRepo interface {
	FindOneMetricColumnRepo(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*entity.CustomMetricColumnTableEntity, error)
	FindListMetricColumnRepo(ctx context.Context, filter any, opts ...*options.FindOptions) (*[]entity.CustomMetricColumnTableEntity, error)
	UpsertMetricColumnRepo(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error
	InsertMetricColumnRepo(ctx context.Context, filter any, data any) (*primitive.ObjectID, error)
	UpdateMetricColumnRepo(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error
	DeleteMetricColumnRepo(ctx context.Context, filter any) error
}
type PresetColumnRepo interface {
	FindListPresetColumnRepo(ctx context.Context, filter any, opts ...*options.FindOptions) (*[]entity.PresetColumnTableEntity, error)
	FindOnePresetColumnRepo(ctx context.Context, filter any, opts ...*options.FindOneOptions) (*entity.PresetColumnTableEntity, error)
	UpsertPresetRepo(ctx context.Context, filter any, update any, opts ...*options.UpdateOptions) error
	UpdatePresetRepo(ctx context.Context, filter any, update any, opts ...*options.UpdateOptions) error
	InsertPresetRepo(ctx context.Context, filter any, data any) (*primitive.ObjectID, error)
	DeletePresetColumnRepo(ctx context.Context, filter any) error
}

type apiCustomColumnTableApiUsc struct {
	logger           sctx.Logger
	presetColumnRepo PresetColumnRepo
	metricColumnRepo MetricColumnRepo
}

func NewApiCustomColumnTableApiUsc(logger sctx.Logger, presetColumnRepo PresetColumnRepo, metricColumnRepo MetricColumnRepo) *apiCustomColumnTableApiUsc {
	return &apiCustomColumnTableApiUsc{
		logger:           logger,
		presetColumnRepo: presetColumnRepo,
		metricColumnRepo: metricColumnRepo,
	}
}
