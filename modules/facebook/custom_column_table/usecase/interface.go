package usecase

import (
	"context"
	"godsp/modules/facebook/custom_column_table/entity"
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/modules/facebook/custom_column_table/transport/responses"

	"github.com/dev-networldasia/dspgos/sctx"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FacebookMetricColumnRepo interface {
	Create(ctx context.Context, data *entity.FacebookMetricColumnTableEntity) (*entity.FacebookMetricColumnTableEntity, error)
	Update(ctx context.Context, id primitive.ObjectID, data *entity.FacebookMetricColumnTableEntity) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*entity.FacebookMetricColumnTableEntity, error)
	FindByCreatedBy(ctx context.Context, createdBy string) (*[]entity.FacebookMetricColumnTableEntity, error)
	FindAll(ctx context.Context) (*[]entity.FacebookMetricColumnTableEntity, error)
}

type FacebookPresetColumnRepo interface {
	Create(ctx context.Context, data *entity.FacebookPresetColumnTableEntity) (*entity.FacebookPresetColumnTableEntity, error)
	Update(ctx context.Context, id primitive.ObjectID, data *entity.FacebookPresetColumnTableEntity) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	FindByID(ctx context.Context, id primitive.ObjectID) (*entity.FacebookPresetColumnTableEntity, error)
	FindByCreatedBy(ctx context.Context, createdBy string) (*[]entity.FacebookPresetColumnTableEntity, error)
	FindAll(ctx context.Context) (*[]entity.FacebookPresetColumnTableEntity, error)
}

type FacebookCustomColumnTableUsecase interface {
	// Metric Column methods
	CreateMetricColumnUsc(ctx context.Context, payload requests.PayloadCreateMetricColumnReq) (*responses.MetricColumnResp, error)
	UpdateMetricColumnUsc(ctx context.Context, id primitive.ObjectID, payload requests.PayloadCreateMetricColumnReq) error
	DeleteMetricColumnUsc(ctx context.Context, id primitive.ObjectID) error
	GetMetricColumnListUsc(ctx context.Context, createdBy string) (*[]responses.MetricColumnResp, error)

	// Preset Column methods
	CreatePresetColumnUsc(ctx context.Context, payload requests.PayloadCreatePresetColumnReq) (*primitive.ObjectID, error)
	UpdatePresetColumnUsc(ctx context.Context, id primitive.ObjectID, payload requests.PayloadCreatePresetColumnReq) error
	DeletePresetColumnUsc(ctx context.Context, id primitive.ObjectID) error
	GetPresetColumnListUsc(ctx context.Context, createdBy string) (*[]responses.PresetColumnResp, error)
	GetDetailsPresetColumnUsc(ctx context.Context, payload requests.PayloadDetailsPresetColumnReq) (string, *[]string, error)
}

type facebookCustomColumnTableUsc struct {
	logger           sctx.Logger
	presetColumnRepo FacebookPresetColumnRepo
	metricColumnRepo FacebookMetricColumnRepo
}

func NewFacebookCustomColumnTableUsc(logger sctx.Logger, presetColumnRepo FacebookPresetColumnRepo, metricColumnRepo FacebookMetricColumnRepo) FacebookCustomColumnTableUsecase {
	return &facebookCustomColumnTableUsc{
		logger:           logger,
		presetColumnRepo: presetColumnRepo,
		metricColumnRepo: metricColumnRepo,
	}
}

// Metric Column implementations
func (u *facebookCustomColumnTableUsc) CreateMetricColumnUsc(ctx context.Context, payload requests.PayloadCreateMetricColumnReq) (*responses.MetricColumnResp, error) {
	entity := &entity.FacebookMetricColumnTableEntity{
		Title:        payload.Title,
		TitlePrivate: *payload.TitlePrivate,
		Formula:      payload.Formula,
		Type:         payload.Type,
		CreatedBy:    payload.CreatedBy,
		CreatedAt:    payload.CreatedAt,
	}

	result, err := u.metricColumnRepo.Create(ctx, entity)
	if err != nil {
		return nil, err
	}

	response := &responses.MetricColumnResp{
		ID:           result.ID,
		Key:          result.ID.Hex(),
		Title:        result.Title,
		TitlePrivate: result.TitlePrivate,
		Formula:      result.Formula,
		Type:         result.Type,
	}

	return response, nil
}

func (u *facebookCustomColumnTableUsc) UpdateMetricColumnUsc(ctx context.Context, id primitive.ObjectID, payload requests.PayloadCreateMetricColumnReq) error {
	entity := &entity.FacebookMetricColumnTableEntity{
		Title:        payload.Title,
		TitlePrivate: *payload.TitlePrivate,
		Formula:      payload.Formula,
		Type:         payload.Type,
		UpdatedAt:    payload.CreatedAt, // Using CreatedAt as UpdatedAt for simplicity
	}

	return u.metricColumnRepo.Update(ctx, id, entity)
}

func (u *facebookCustomColumnTableUsc) DeleteMetricColumnUsc(ctx context.Context, id primitive.ObjectID) error {
	return u.metricColumnRepo.Delete(ctx, id)
}

func (u *facebookCustomColumnTableUsc) GetMetricColumnListUsc(ctx context.Context, createdBy string) (*[]responses.MetricColumnResp, error) {
	entities, err := u.metricColumnRepo.FindByCreatedBy(ctx, createdBy)
	if err != nil {
		return nil, err
	}

	var result []responses.MetricColumnResp
	for _, entity := range *entities {
		result = append(result, responses.MetricColumnResp{
			ID:           entity.ID,
			Key:          entity.ID.Hex(),
			Title:        entity.Title,
			TitlePrivate: entity.TitlePrivate,
			Formula:      entity.Formula,
			Type:         entity.Type,
		})
	}

	return &result, nil
}

// Preset Column implementations
func (u *facebookCustomColumnTableUsc) CreatePresetColumnUsc(ctx context.Context, payload requests.PayloadCreatePresetColumnReq) (*primitive.ObjectID, error) {
	entity := &entity.FacebookPresetColumnTableEntity{
		Name:        payload.Name,
		NamePrivate: *payload.NamePrivate,
		Keys:        payload.Keys,
		CreatedBy:   payload.CreatedBy,
		CreatedAt:   payload.CreatedAt,
	}

	result, err := u.presetColumnRepo.Create(ctx, entity)
	if err != nil {
		return nil, err
	}

	return &result.ID, nil
}

func (u *facebookCustomColumnTableUsc) UpdatePresetColumnUsc(ctx context.Context, id primitive.ObjectID, payload requests.PayloadCreatePresetColumnReq) error {
	entity := &entity.FacebookPresetColumnTableEntity{
		Name:        payload.Name,
		NamePrivate: *payload.NamePrivate,
		Keys:        payload.Keys,
		UpdatedAt:   payload.CreatedAt, // Using CreatedAt as UpdatedAt for simplicity
	}

	return u.presetColumnRepo.Update(ctx, id, entity)
}

func (u *facebookCustomColumnTableUsc) DeletePresetColumnUsc(ctx context.Context, id primitive.ObjectID) error {
	return u.presetColumnRepo.Delete(ctx, id)
}

func (u *facebookCustomColumnTableUsc) GetPresetColumnListUsc(ctx context.Context, createdBy string) (*[]responses.PresetColumnResp, error) {
	entities, err := u.presetColumnRepo.FindByCreatedBy(ctx, createdBy)
	if err != nil {
		return nil, err
	}

	var result []responses.PresetColumnResp
	for _, entity := range *entities {
		result = append(result, responses.PresetColumnResp{
			ID:          entity.ID,
			Name:        entity.Name,
			NamePrivate: entity.NamePrivate,
			Keys:        entity.Keys,
		})
	}

	return &result, nil
}

func (u *facebookCustomColumnTableUsc) GetDetailsPresetColumnUsc(ctx context.Context, payload requests.PayloadDetailsPresetColumnReq) (string, *[]string, error) {
	entity, err := u.presetColumnRepo.FindByID(ctx, payload.ID)
	if err != nil {
		return "", nil, err
	}

	return entity.Name, &entity.Keys, nil
}
