package usecase

import (
	"context"
	"errors"
	"fmt"
	"godsp/modules/facebook/custom_column_table/mapping"
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/modules/facebook/custom_column_table/transport/responses"
	"godsp/pkg/sctx/core"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/** * GetListMetricColumnUsc
 */
func (a *apiCustomColumnTableApiUsc) GetListMetricColumnUsc(context context.Context) (*[]responses.MetricColumnResp, error) {

	filter := bson.M{}
	opts := options.Find()
	per := core.GetPermission(context).GetPermissions()
	if per != nil {
		opts.SetProjection(bson.M{
			"title_private": 0,
		})
	}

	metricColumnsRaw, err := a.metricColumnRepo.FindListMetricColumnRepo(context, filter, opts)

	if err != nil {
		a.logger.Error("Error finding metric column", "error", err)
		return nil, err
	}
	if metricColumnsRaw == nil {
		a.logger.Warn("Metric column not found", "filter", filter)
		return nil, errors.New("metric column not found")
	}
	if len(*metricColumnsRaw) == 0 {
		a.logger.Warn("No metric columns found", "filter", filter)
		return nil, errors.New("no metric columns found")
	}

	metricColumns := mapping.MappingListMetricColumnTable(metricColumnsRaw)
	return metricColumns, nil
}

/**
 * Create Preset Column
 */
func (a *apiCustomColumnTableApiUsc) CreateMetricColumnUsc(context context.Context, payload requests.PayloadCreateMetricColumnReq) (*responses.MetricColumnResp, error) {
	// Implementation for getting preset columns
	dataCreate, metric := mapping.MapperCreateMetricColumnTable(payload)

	metricID, err := a.metricColumnRepo.InsertMetricColumnRepo(context, bson.M{}, dataCreate)

	if err != nil {
		a.logger.Error("Error create metric column", "error", err)
		return nil, err
	}
	metric.ID = *metricID

	return metric, nil
}

/**
 * Create Preset Column
 */
func (a *apiCustomColumnTableApiUsc) UpdateMetricColumnUsc(context context.Context, payload requests.PayloadUpdateMetricColumnReq) error {

	filter := bson.M{
		"_id": payload.ID,
	}

	dataUpdate := mapping.MapperUpdateMetricColumnTable(payload)

	err := a.metricColumnRepo.UpdateMetricColumnRepo(context, filter, *dataUpdate)

	if err != nil {
		a.logger.Error("Error create metric column", "error", err)
		return err
	}

	return nil
}

/**
 * Delete Metric Column
 */
func (a *apiCustomColumnTableApiUsc) DeleteMetricColumnUsc(context context.Context, payload requests.PayloadDeleteMetricColumnReq) error {
	filter := bson.M{
		"_id": payload.ID,
	}
	fmt.Printf("filter: %v\n", filter)
	err := a.metricColumnRepo.DeleteMetricColumnRepo(context, filter)

	if err != nil {
		a.logger.Error("Error delete metric column", "error", err)
		return err
	}

	return nil
}
