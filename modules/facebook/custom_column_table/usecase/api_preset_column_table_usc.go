package usecase

import (
	"context"
	"errors"
	"godsp/modules/facebook/custom_column_table/entity"
	"godsp/modules/facebook/custom_column_table/mapping"
	"godsp/modules/facebook/custom_column_table/transport/requests"
	"godsp/modules/facebook/custom_column_table/transport/responses"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

/**
 * GetListPresetColumnUsc
 */
func (a *apiCustomColumnTableApiUsc) GetDetailsPresetColumnUsc(context context.Context, payload requests.PayloadDetailsPresetColumnReq) (*string, *[]responses.DetailColumnResp, error) {
	// Implementation for getting preset columns
	filter := bson.M{
		"type": 1,
	}
	if payload.ID != nil {
		filter["_id"] = payload.ID
		filter["type"] = 2
	}

	if payload.ClientID != nil {
		filter["client_id"] = payload.ClientID
	}

	// Get default preset column
	if payload.IsDefult != nil && *payload.IsDefult {
		filter = bson.M{
			"type": 0, // Assuming 0 is the type for default preset columns
		}
	}

	presetColumn, err := a.presetColumnRepo.FindOnePresetColumnRepo(context, filter)

	if err != nil {
		filter = bson.M{
			"type": 0, // Assuming 0 is the type for default preset columns
		}
		presetColumn, err = a.presetColumnRepo.FindOnePresetColumnRepo(context, filter)
		if err != nil {
			a.logger.Error("Error finding preset column", "error", err)
			return nil, nil, errors.New("preset column not found")
		}
	}

	metricColumn, err := a.GetListMetricColumnUsc(context)
	if err != nil {
		a.logger.Error("Error finding metric column", "error", err)
	}

	customFields := mapping.MappingDetailPresetColumnTable(presetColumn.Keys, metricColumn)

	if presetColumn == nil {
		a.logger.Warn("Preset column not found", "filter", filter)
		return nil, nil, errors.New("preset column not found")
	}

	presetName := ""
	if presetColumn.Type == 2 {
		presetName = presetColumn.Name
	}
	return &presetName, customFields, nil
}

/**
 * GetListPresetColumnUsc
 */
func (a *apiCustomColumnTableApiUsc) GetListPresetColumnUsc(context context.Context) (*[]entity.PresetColumnTableEntity, error) {
	// Implementation for getting preset columns
	filter := bson.M{
		"type": 2,
	}

	presetsColumn, err := a.presetColumnRepo.FindListPresetColumnRepo(context, filter)

	if err != nil {
		a.logger.Error("Error finding preset column", "error", err)
		return nil, err
	}

	if presetsColumn == nil {
		a.logger.Warn("Preset column not found", "filter", filter)
		return nil, errors.New("preset column not found")
	}
	return presetsColumn, nil
}

/**
 * Update CurrentViewPresetColumnUsc
 */
func (a *apiCustomColumnTableApiUsc) UpdateCurrentViewPresetColumnUsc(context context.Context, payload requests.PayloadUpdateCurrentViewPresetColumnReq) error {
	// Implementation for getting preset columns
	filter := bson.M{
		"type": 1,
	}
	if payload.ClientID != nil {
		filter["client_id"] = payload.ClientID
	}
	updateData := bson.M{
		"$set": bson.M{
			"keys": payload.Keys,
		},
	}

	err := a.presetColumnRepo.UpsertPresetRepo(context, filter, updateData)

	if err != nil {
		a.logger.Error("Error update current preset column", "error", err)
		return err
	}

	return nil
}

/**
 * Update Preset ColumnUsc
 */
func (a *apiCustomColumnTableApiUsc) UpdatePresetColumnUsc(context context.Context, payload requests.PayloadUpdatePresetColumnReq) error {
	// Implementation for getting preset columns
	filter := bson.M{
		"type": 2,
		"_id":  payload.ID,
	}

	if payload.ClientID != nil {
		filter["client_id"] = payload.ClientID
	}

	updateData := bson.M{
		"$set": bson.M{
			"keys":         payload.Keys,
			"name":         payload.Name,
			"name_private": payload.NamePrivate,
			"updated_by":   payload.UpdatedBy,
			"updated_at":   payload.UpdatedAt,
		},
	}

	err := a.presetColumnRepo.UpsertPresetRepo(context, filter, updateData)

	if err != nil {
		msg := "error update preset column"
		a.logger.Error("Error update preset column", "error", err)
		return errors.New(msg)
	}

	return nil
}

/**
 * Create Preset Column
 */
func (a *apiCustomColumnTableApiUsc) CreatePresetColumnUsc(context context.Context, payload requests.PayloadCreatePresetColumnReq) (*primitive.ObjectID, error) {
	// Implementation for getting preset columns
	data := bson.M{
		"keys":         payload.Keys,
		"name":         payload.Name,
		"name_private": payload.NamePrivate,
		"created_by":   payload.CreatedBy,
		"created_at":   payload.CreatedAt,
		"type":         2,
	}

	ID, err := a.presetColumnRepo.InsertPresetRepo(context, bson.M{}, data)

	if err != nil {
		a.logger.Error("Error create preset column", "error", err)
		return nil, err
	}

	return ID, nil
}

/**
 * Delete Preset Column
 */
func (a *apiCustomColumnTableApiUsc) DeletePresetColumnUsc(context context.Context, payload requests.PayloadDeletePresetColumnReq) error {
	filter := bson.M{
		"_id": payload.ID,
	}
	err := a.presetColumnRepo.DeletePresetColumnRepo(context, filter)

	if err != nil {
		a.logger.Error("Error delete preset column", "error", err)
		return err
	}

	return nil
}
