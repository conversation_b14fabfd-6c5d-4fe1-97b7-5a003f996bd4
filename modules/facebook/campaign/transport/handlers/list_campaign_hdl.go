package handlers

import (
	"godsp/conf"
	"godsp/modules/facebook/campaign/response"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/facebook/campaign"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func (h *campaignHdl) ListCampaignHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {

		adAccounts, _ := h.usc.ListAdAccountCampaignUsc(c.Context())
		clients, _ := h.usc.ListClientCampaignUsc(c.Context())

		userInfo, err := utils.GetInfoUserBasic(c.Context())

		if err != nil && userInfo.RoleName != conf.SysConf.RoleAdmin {
			return c.Redirect("/page-forbidden")
		}

		// Get flash message from session
		sess, _ := store.Get(c)
		flashMsg := ""
		if msg := sess.Get("flash_msg"); msg != nil {
			flashMsg = msg.(string)
			sess.Delete("flash_msg")
			sess.Save()
		}

		// Prepare response data
		data := &response.ListCampaignResp{
			FlashMsg:           flashMsg,
			AuthPermission:     core.GetPermission(c.Context()).GetPermissions(),
			UserInfo:           userInfo,
			Clients:            clients,
			AdAccounts:         adAccounts,
			Advertisers:        nil, // TODO: Add advertisers if needed
			PresetsColumnTable: nil, // TODO: Add presets if needed
		}

		// Render using templ
		return campaign.ListCampaign(data).Render(c.Context(), c.Response().BodyWriter())
	}
}
