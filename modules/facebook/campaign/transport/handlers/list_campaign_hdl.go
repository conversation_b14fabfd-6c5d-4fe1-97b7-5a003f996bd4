package handlers

import (
	"godsp/conf"
	"godsp/modules/facebook/custom_column_table/common/constants"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/facebook/campaign"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
)

func (h *campaignHdl) ListCampaignHdl() fiber.Handler {
	return func(c *fiber.Ctx) error {

		adAccounts, _ := h.usc.ListAdAccountCampaignUsc(c.Context())
		clients, _ := h.usc.ListClientCampaignUsc(c.Context())

		userInfo, err := utils.GetInfoUserBasic(c.Context())

		if err != nil && userInfo.RoleName != conf.SysConf.RoleAdmin {
			return c.Redirect("/page-forbidden")
		}

		// Get flash message
		msg := core.GetFlashMessage(c.Context())

		// Get custom column data
		metricColumns, _ := h.usc.GetMetricColumnList(c.Context())
		presetColumns, _ := h.usc.GetPresetColumnList(c.Context())

		// Prepare report fields
		reportFields := &constants.FacebookAdserverReportFields

		return templates.Render(c, campaign.FacebookCampaignList(&campaign.FacebookCampaignListData{
			FlashMsg:       msg,
			AuthPermission: core.GetPermission(c.Context()).GetPermissions(),
			UserInfo:       userInfo,
			AdAccounts:     adAccounts,
			Clients:        clients,
			ReportFields:   reportFields,
			MetricColumns:  metricColumns,
			PresetColumns:  presetColumns,
		}))
	}
}
