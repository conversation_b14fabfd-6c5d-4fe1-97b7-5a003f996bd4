package handlers

import (
	"godsp/modules/facebook/custom_column_table/common/constants"
	"godsp/modules/facebook/custom_column_table/transport/responses"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/facebook/campaign"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func (h *campaignHdl) ListCampaignHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {

		adAccounts, _ := h.usc.ListAdAccountCampaignUsc(c.Context())
		clients, _ := h.usc.ListClientCampaignUsc(c.Context())

		userInfo, err := utils.GetInfoUserAuth(c.Context())

		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		// Get flash message
		flashMsg := utils.GetFlashMessage(c, store, "campaign_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		// Get custom column data (for now, use empty slices)
		metricColumns, _ := h.usc.GetMetricColumnList(c.Context())
		presetColumns, _ := h.usc.GetPresetColumnList(c.Context())

		// Prepare report fields
		reportFields := &constants.FacebookAdserverReportFields

		// Convert interface{} to proper types (for now, use empty slices)
		emptyMetricColumns := make([]responses.MetricColumnResp, 0)
		emptyPresetColumns := make([]responses.PresetColumnResp, 0)

		return templates.Render(c, campaign.FacebookCampaignList(&campaign.FacebookCampaignListData{
			FlashMsg:       msg,
			AuthPermission: core.GetPermission(c.Context()).GetPermissions(),
			UserInfo:       userInfo,
			AdAccounts:     adAccounts,
			Clients:        clients,
			ReportFields:   reportFields,
			MetricColumns:  &emptyMetricColumns,
			PresetColumns:  &emptyPresetColumns,
		}))
	}
}
