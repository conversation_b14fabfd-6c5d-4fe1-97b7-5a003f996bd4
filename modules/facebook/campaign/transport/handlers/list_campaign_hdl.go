package handlers

import (
	clientE "godsp/modules/admin/client/entity"
	adAccountRes "godsp/modules/facebook/ad_account/transport/response"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"
	"godsp/views/v2/facebook/tableAds"

	"github.com/dev-networldasia/dspgos/gos/templates"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func (h *campaignHdl) ListCampaignHdl(store *session.Store) fiber.Handler {
	return func(c *fiber.Ctx) error {
		flashMsg := utils.GetFlashMessage(c, store, "campaign_list_flash_msg")
		msg := ""
		if _, ok := flashMsg.(string); ok {
			msg = flashMsg.(string)
		}

		userInfo, err := utils.GetInfoUserAuth(c.Context())
		if err != nil {
			return core.ReturnErrForPermissionDenied(c)
		}

		authPermission := core.GetPermission(c.Context()).GetPermissions()
		clients := make([]*clientE.ClientEntity, 0)

		if authPermission == nil {
			clients, _ = h.usc.ListClientCampaignUsc(c.Context())
		}

		adAccounts := []*adAccountRes.AdAccountEditClient{}
		if authPermission == nil {
			adAccounts, _ = h.usc.ListAdAccountCampaignUsc(c.Context())
		}

		presetColumn, _ := h.usc.GetPresetColumnList(c.Context())
		// jsonData, _ := json.MarshalIndent(presetColumn, "", "  ")
		// fmt.Println("presetColumn Column Data: ", string(jsonData))

		return templates.Render(c, tableAds.ListDatatableAds(&tableAds.ListTableAdsLayoutData{
			FlashMsg:           msg,
			AuthPermission:     core.GetPermission(c.Context()).GetPermissions(),
			UserInfo:           userInfo,
			Clients:            clients,
			AdAccounts:         adAccounts,
			PresetsColumnTable: presetColumn,
		}))
	}
}
