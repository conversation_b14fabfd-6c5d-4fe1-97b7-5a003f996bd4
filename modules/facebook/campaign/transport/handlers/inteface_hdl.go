package handlers

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	userRes "godsp/modules/admin/user/transport/responses"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CampaignUsc interface {
	GetUserInfo(ctx context.Context, userId primitive.ObjectID) (*userRes.DetailsUser, error)
	ListAdAccountCampaignUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error)
	ListClientCampaignUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
	GetMetricColumnList(ctx context.Context) (*[]interface{}, error)
	GetPresetColumnList(ctx context.Context) (*[]interface{}, error)
}
type campaignHdl struct {
	usc CampaignUsc
}

func NewCampaignHdl(usc CampaignUsc) *campaignHdl {
	return &campaignHdl{
		usc,
	}
}
