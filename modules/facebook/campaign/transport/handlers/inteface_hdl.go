package handlers

import (
	"context"
	clientE "godsp/modules/admin/client/entity"
	adAccountRes "godsp/modules/facebook/ad_account/transport/response"
	customColumnTableResponse "godsp/modules/facebook/custom_column_table/transport/responses"

	"github.com/gofiber/fiber/v2/middleware/session"
)

type CampaignUsc interface {
	ListClientCampaignUsc(ctx context.Context) ([]*clientE.ClientEntity, error)
	ListAdAccountCampaignUsc(ctx context.Context) ([]*adAccountRes.AdAccountEditClient, error)
	GetPresetColumnList(ctx context.Context) ([]*customColumnTableResponse.PresetColumnResp, error)
}
type campaignHdl struct {
	usc   CampaignUsc
	store *session.Store
}

func NewCampaignHdl(usc CampaignUsc, store *session.Store) *campaignHdl {
	return &campaignHdl{
		usc,
		store,
	}
}
