package usecase

import (
	"context"
	"errors"

	clientE "godsp/modules/admin/client/entity"
	"godsp/modules/admin/user/common/pipelines"
	"godsp/modules/admin/user/entity"
	userRes "godsp/modules/admin/user/transport/responses"
	adAccRes "godsp/modules/facebook/ad_account/transport/response"
	"godsp/modules/facebook/campaign/mapping"
	"godsp/modules/facebook/common/fbenums"
	cusColRes "godsp/modules/facebook/custom_column_table/transport/responses"
	cusColRepo "godsp/modules/facebook/custom_column_table/usecase"
	"godsp/modules/facebook/iface_repo"

	v20 "godsp/pkg/fb-marketing/marketing/v20"
	"godsp/pkg/gos/utils"
	"godsp/pkg/sctx/core"

	"github.com/dev-networldasia/dspgos/sctx"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type CampaignRepo interface {
}

type UserRepo interface {
	FindOneUserRepo(ctx context.Context, filter interface{}, opts ...*options.FindOneOptions) (*entity.UserEntity, error)
	FindOneDetailUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*userRes.DetailsUser, error)
	// FindOneDetailUserWithPipelineRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*responses.DetailsUser, error)
}

type ClientRepo interface {
	FindClientRepo(ctx context.Context, filter interface{}, opts ...*options.FindOptions) ([]*clientE.ClientEntity, error)
}

type campaignUsc struct {
	fbService        *v20.Service
	repo             CampaignRepo
	userRepo         UserRepo
	logger           sctx.Logger
	adAcc            iface_repo.AdAccountRepo
	clientRepo       ClientRepo
	presetColumnRepo cusColRepo.PresetColumnRepo
}

func NewCampaignUsc(fbService *v20.Service, repo CampaignRepo, userRepo UserRepo, logger sctx.Logger, adAcc iface_repo.AdAccountRepo, clientRepo ClientRepo) *campaignUsc {
	return &campaignUsc{
		fbService:  fbService,
		repo:       repo,
		userRepo:   userRepo,
		logger:     logger,
		adAcc:      adAcc,
		clientRepo: clientRepo,
	}
}

/**
 * Get User by ID
 */
func (usc *campaignUsc) GetUserInfo(ctx context.Context, userId primitive.ObjectID) (*userRes.DetailsUser, error) {

	pipeline := pipelines.PipelineGetDetatilInfoUser(userId)

	user, err := usc.userRepo.FindOneDetailUserWithPipelineRepo(ctx, pipeline)
	if err != nil {
		usc.logger.Error(err)
		return nil, err
	}

	return user, nil
}

/**
 * list ad account
 */
func (usc *campaignUsc) ListAdAccountCampaignUsc(ctx context.Context) ([]*adAccRes.AdAccountEditClient, error) {
	allowDiskUse := fbenums.ALLOW_DISK_USE_TRUE
	opts := &options.FindOptions{
		AllowDiskUse: &allowDiskUse,
		Sort:         bson.D{{Key: "name", Value: -1}},
		Projection:   bson.M{"id": 1, "name": 1, "account_id": 1},
	}
	return usc.adAcc.FindAdAccountEditClientRepo(ctx, bson.M{}, opts)
}

/**
 * List client
 */
func (usc *campaignUsc) ListClientCampaignUsc(ctx context.Context) ([]*clientE.ClientEntity, error) {
	return usc.clientRepo.FindClientRepo(ctx, bson.M{})
}

/**
 * List Preset columns for Client
 */
func (usc *campaignUsc) GetPresetColumnList(ctx context.Context) ([]*cusColRes.PresetColumnResp, error) {
	// Implementation for getting preset columns
	clientID := utils.GetClientIdPrimitive(ctx)
	permission := core.GetPermission(ctx).GetPermissions()

	filter := bson.M{
		"type": 2,
	}
	if permission != nil {
		filter["client_ids"] = bson.M{"$in": []primitive.ObjectID{*clientID}}
	}

	result, err := usc.presetColumnRepo.FindListPresetColumnRepo(ctx, filter)

	if err != nil {
		usc.logger.Error("Error finding preset column", err)
		return nil, err
	}

	if result == nil {
		usc.logger.Warn("Preset column not found", "filter", filter)
		return nil, errors.New("preset column not found")
	}
	presetsColumn := mapping.MappingListPresetColumnTable(result)

	return presetsColumn, nil
}
