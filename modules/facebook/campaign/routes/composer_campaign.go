package routes

import (
	clientR "godsp/modules/admin/client/repository/mongo"

	userR "godsp/modules/admin/user/repository/mongo"
	adAccR "godsp/modules/facebook/ad_account/repository/mongo"
	"godsp/modules/facebook/campaign/repository/mongo"
	"godsp/modules/facebook/campaign/transport/handlers"
	"godsp/modules/facebook/campaign/usecase"
	"godsp/pkg/sctx/component/facebook/fbmarketing"

	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"

	"github.com/dev-networldasia/dspgos/sctx/configs"

	"github.com/dev-networldasia/dspgos/sctx"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

type ComposerCampaign interface {
	ListCampaignHdl(store *session.Store) fiber.Handler
}

func ComposerCampaignService(serviceCtx sctx.ServiceContext) ComposerCampaign {

	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("webfb")
	fbService := serviceCtx.MustGet(configs.KeyFBMarketingV20).(fbmarketing.FBMarketingServices).GetFBMarketingService()

	// Init api
	repo := mongo.NewCampaignRepo(mongoDB)
	adAccRepo := adAccR.NewAdAccountRepo(mongoDB)
	clientRepo := clientR.NewClientRepo(mongoDB)
	userRepo := userR.NewUserRepo(mongoDB)
	usc := usecase.NewCampaignUsc(fbService, repo, userRepo, logger, adAccRepo, clientRepo)
	hdl := handlers.NewCampaignHdl(usc)

	return hdl
}
