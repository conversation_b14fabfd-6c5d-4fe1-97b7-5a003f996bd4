package response

import (
	"godsp/modules/facebook/iface"
)

type ListCampaignResp struct {
	FlashMsg           string           `json:"flash_msg,omitempty"`
	AuthPermission     map[string]int   `json:"auth_permission"`
	UserInfo           iface.UserInfoFb `json:"user_info"`
	Clients            interface{}      `json:"clients"`
	AdAccounts         interface{}      `json:"ad_accounts"`
	Advertisers        interface{}      `json:"advertisers"`
	PresetsColumnTable interface{}      `json:"presets_column_table"`
}
