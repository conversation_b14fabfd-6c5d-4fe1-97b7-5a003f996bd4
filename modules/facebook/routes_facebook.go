package facebook

import (
	rAd "godsp/modules/facebook/ad/routes"
	rAdAccount "godsp/modules/facebook/ad_account/routes"
	rCreative "godsp/modules/facebook/ad_creative/routes"
	rAdPixel "godsp/modules/facebook/ad_pixel/routes"
	rAdset "godsp/modules/facebook/adset/routes"
	rAudience "godsp/modules/facebook/audience/routes"
	rBudget "godsp/modules/facebook/budget_schedule/routes"
	rCampaign "godsp/modules/facebook/campaign/routes"
	rCatalogue "godsp/modules/facebook/catalogue/routes"
	rImg "godsp/modules/facebook/image/routes"
	rInstagramMedia "godsp/modules/facebook/instagram_media/routes"
	rInterestBehaviorDemographic "godsp/modules/facebook/interest/routes"
	rMessageTemplate "godsp/modules/facebook/message_template/routes"
	rPage "godsp/modules/facebook/pages/routes"
	rPost "godsp/modules/facebook/posts/routes"
	rProduct "godsp/modules/facebook/product/routes"
	rProductSet "godsp/modules/facebook/product_set/routes"
	rTargeting "godsp/modules/facebook/targeting/routes"
	rVideo "godsp/modules/facebook/video/routes"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesFacebook(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	rAdAccount.SetupRoutesAdAcount(app, serviceCtx)
	rAd.SetupRoutesAd(app, serviceCtx)
	rCreative.SetupRouterAdCreative(app, serviceCtx)
	rAdPixel.SetupRoutesAdPixel(app, serviceCtx)
	rAdset.SetupRoutesAdset(app, serviceCtx)
	rAudience.SetupRoutesAudience(app, serviceCtx)
	rBudget.SetupRouterBudgetSchedule(app, serviceCtx)
	rCampaign.SetupRouterCampaign(app, serviceCtx, store, midds...)
	rCatalogue.SetupRoutesCatalogue(app, serviceCtx)
	rImg.SetupRoutesImages(app, serviceCtx)
	rInstagramMedia.SetupRouterInstagramMedia(app, serviceCtx)
	rInterestBehaviorDemographic.SetupRoutesInterestBehaviorDemographic(app, serviceCtx)
	rMessageTemplate.SetupRoutesMessageTemplates(app, serviceCtx)
	rPage.SetupRoutesPages(app, serviceCtx)
	rPost.SetupRoutesPosts(app, serviceCtx)
	rProduct.SetupRoutesProduct(app, serviceCtx)
	rProductSet.SetupRoutesProductSet(app, serviceCtx)
	rTargeting.SetupRouterTargeting(app, serviceCtx)
	rVideo.SetupRoutesVideos(app, serviceCtx)
}
