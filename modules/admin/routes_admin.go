package admin

import (
	"godsp/modules/admin/auths"
	"godsp/modules/admin/billing"
	"godsp/modules/admin/client"
	"godsp/modules/admin/permission"
	"godsp/modules/admin/role"
	"godsp/modules/admin/user"
	rHome "godsp/modules/admin/home/<USER>"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesAdmin(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {
	role.SetupRoutesRole(app, serviceCtx)
	user.SetupRoutesUser(app, serviceCtx, store)
	auths.SetupRoutesAuth(app, serviceCtx, store)
	permission.SetupRoutesPermission(app, serviceCtx, store)
	client.SetupRoutesClients(app, serviceCtx, store)
	billing.SetupRoutesBilling(app, serviceCtx)
	rHome.SetupRoutesHomePages(app, serviceCtx)
}
